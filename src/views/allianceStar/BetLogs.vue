<script setup lang="ts">
import { allianceBettingLog } from '@/api';
import { TOPIC_LANG_MAP } from '@/enum';
import { load } from '@/utils/loading';
import { formatDate } from '@/utils/utils';
import { reactive } from 'vue';

interface ILogItem {
  id: number
  uid: number
  activity_id: string
  group: string
  topic: string
  team_id: string
  bet_nums: number
  ts: number
  odds: string
  name: string
  suc_send_nums: number
  fail_send_nums: number
  send_status: number
  send_ts: number
  result_status: boolean
  is_win: boolean
}

const props = defineProps({
  activityId: String,
  group: String
})

const state = reactive({
  isShow: false,
  logList: [] as Array<ILogItem>
})
const getLogs = () => {
  const params = {
    activity_id: props.activityId,
    group: props.group
  }
  load.show()
  allianceBettingLog(params).then((res: any) => {
    state.logList = res
    state.isShow = true
  }).finally(() => load.hide())
}

const close = () => {
  state.isShow = false
  setTimeout(() => {
    state.logList = []
  }, 300);
}
</script>

<template>
  <div class="logs" @click="getLogs"><div class="icon"></div>{{ $t('alliance_guess_logs_button') }}</div>
  <van-overlay :show="state.isShow" :lock-scroll="false">
    <div class="wrapper">
      <div class="body">
        <div class="close" @click="close"></div>
        <div class="title">{{ $t('alliance_guess_logs_title') }}</div>
        <div class="line"></div>
        <div class="content log">
          <div class="log-item" v-for="(item, i) in state.logList" :key="item.id">
            <div class="flex-c l">
              <div class="t guess-title">
                <div class="guess-t">{{ $t('alliance_guess_logs_subtitle', [i + 1]) }}</div>
                <div class="guess-time">
                  <div class="icon"></div>
                  UTC
                  {{ formatDate(item.ts) }}
                </div>
              </div>
              <div class="b guess-content" v-html="$t('alliance_guess_logs_detail', [, $t(TOPIC_LANG_MAP[item.topic]), item.name, item.bet_nums, item.odds, item.suc_send_nums, item.fail_send_nums])"></div>
            </div>
            <div class="flex-c r">
              <div class="t d-f d-f-center guess-result-t">{{ $t('alliance_guess_logs_result') }}</div>
              <div class="b d-f d-f-c d-f-center guess-result">
                <div v-if="!item.result_status" class="label unlock">{{ $t('alliance_guess_logs_result_3') }}</div>
                <div v-else-if="item.is_win" class="label success">{{ $t('alliance_guess_logs_result_1') }}</div>
                <div v-else class="label failed">{{ $t('alliance_guess_logs_result_2') }}</div>
              </div>
            </div>
          </div>
          <div v-if="state.logList.length === 0" class="empty-list">{{ $t('nothingHere') }}</div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/dialog.scss');
.logs {
  position: absolute;
  top: -3px;
  right: 26px;
  height: 36px;
  padding: 0 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-family: Adobe Heiti Std;
  font-weight: normal;
  line-height: 1;
  color: #E8E2B6;
  cursor: pointer;
  @include backgroundSec('alliance/bg-log.png');
  .icon {
    width: 21px;
    height: 25px;
    @include backgroundSec('alliance/icon-log.png');
    margin-right: 8px;
  }
}
.body {
  width: 720px;
}
.empty-list {
  line-height: 5;
  font-size: 22px;
  color: rgba(88, 70, 49, 1);
  text-align: center;
}
</style>