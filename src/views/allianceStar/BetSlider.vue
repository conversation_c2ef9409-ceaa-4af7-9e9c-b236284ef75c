<script setup lang="ts">
import { useAllianceStore } from '@/stores';
import { storeToRefs } from 'pinia';
import { watch, ref } from 'vue';

const props = defineProps({
  crtTeamIndex: {
    type: Number,
    default: 0
  }
})

const { state, computedCrtAct } = storeToRefs(useAllianceStore())
const guessCount = ref(0)
watch(guessCount, nv => {
  if (nv >= state.value.userInfo.chalons_coin) {
    guessCount.value = state.value.userInfo.chalons_coin
  }
  if (nv <= 0) {
    guessCount.value = 0
  }
})
const emit = defineEmits(['bet'])
const oddsClick = () => {
  emit('bet', guessCount.value)
}

const reset = () => guessCount.value = 0
defineExpose({
  reset
})
</script>

<template>
  <div class="guess-wrap" v-if="computedCrtAct.status === 1">
    <i18n-t keypath="alliance_guess_bet_infomation_1" tag="div" class="tips-wrap">
      <span>{{ computedCrtAct.team_info[props.crtTeamIndex].name }}</span>
      <input type="number" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" v-model="guessCount" min="0" :max="state.userInfo.chalons_coin" >
    </i18n-t>
    <div class="slider">
      <van-slider v-model="guessCount" :max="state.userInfo.chalons_coin" />
      <div class="limit min">0</div>
      <div class="limit max">{{ state.userInfo.chalons_coin }}</div>
    </div>
    <button class="button submit-guess" :disabled="guessCount === 0 || computedCrtAct.can_bet_nums <= 0" @click="oddsClick">{{ $t('alliance_guess_bet_button') }}</button>
  </div>
</template>

<style lang="scss" scoped>
.guess-wrap {
  width: 100%;
  &.guess-txt {
    padding: 0;
    line-height: 1.4;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: center;
    font-size: 20px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #D3CBB4;
    padding: 0 28px;
    // @include textStroke(#000000);
    .guess-betting {
      ::v-deep(i:nth-of-type(1)) {
        color: #CFC09A;
        margin-bottom: 10px;
        line-height: 1;
        display: inline-block;
      }
      ::v-deep(i:nth-of-type(3)) {
        color: #77D92E;
        text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
      }
      ::v-deep(i:nth-of-type(4)) {
        display: inline-block;
        color: transparent;

        background: linear-gradient(0deg, #F8B514 0%, #FFE87B 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      ::v-deep(i:nth-of-type(5)) {
        color: #F6E2B1;
        @include textStroke(rgba(0, 0, 0, 0.5));
      }
    }
    .guess-win {
      ::v-deep(i:nth-of-type(3)) {
        color: #77D92E;
      }
      ::v-deep(i:nth-of-type(4)) {
        font-weight: bolder;
        color: #F6E2B1;
        @include textStroke(rgba(0, 0, 0, 0.5));
      }
    }
    .guess-loss {
      ::v-deep(i:nth-of-type(3)) {
        color: #D9423B;
      }
    }
  }
  .tips-wrap {
    font-size: 20px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #CFC09A;
    line-height: 1.1;
    @include textStroke(#000000);
  }
  input {
    display: inline-block;
    min-width: 130px;
    width: auto;
    height: 35px;
    background-color: none;
    @include backgroundSec('bg-input.png');
    border: none;
    font-size: 20px;
    font-family: Flareserif821 BT;
    font-weight: normal;
    color: #77D92E;
    line-height: 35px;
    text-align: center;
    -moz-appearance:textfield;
    &::-webkit-inner-spin-button, 
    &::-webkit-outer-spin-button { 
      -webkit-appearance: none;
      margin: 0; 
    }
  }
  .slider {
    margin: 48px 4px;
    position: relative;
    .van-slider {
      background-size: 100% 100%;
    }
    .limit {
      position: absolute;
      font-size: 18px;
      font-family: Flareserif821 BT;
      font-weight: normal;
      color: #E2D3A4;
      line-height: 1;
      @include textStroke(#000000);
      bottom: -35px;
    }
    .min {
      left: 0;
    }
    .max {
      right: 0;
    }
  }
  .submit-guess {
    width: 214px;
    height: 58px;
    margin: 40px auto 0;
    @include backgroundSec('bg-btn.png');
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #FEFFF5;
  }
}
</style>