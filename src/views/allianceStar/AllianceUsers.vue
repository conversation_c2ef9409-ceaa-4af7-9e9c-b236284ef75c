<script setup lang="ts">
import { useAllianceStore } from '@/stores';
import { formatNum } from '@/utils/utils';
import { ref } from 'vue';

const allianceStore = useAllianceStore()

const crt = ref(0)
</script>

<template>
  <!-- 联盟成员 -->
  <div class="alliance-section alliance-list" v-if="allianceStore.computedCrtTeamInfos.length">
    <div class="section-title"><div>{{ $t('alliance_guess_subtitle_3') }}</div></div>

    <div class="teams-list">
      <div
        v-for="(team, i) in allianceStore.computedCrtTeamInfos"
        :key="i"
        :class="['team-item', { active: i === crt }]"
        @click="crt = i"
      >
        <div :class="['flag', `flag-${team.order}`, { active: i === crt }]">
          <div class="flag-active" v-show="i === crt"></div>
          <div class="team-logo"></div>
        </div>
        <div class="same-server" v-if="team.same_kingdom"></div>
        <div class="team-info">
          <div class="team-name">{{ team.name }}</div>
          <div class="divider"></div>
          <div class="numericals">
            <div>{{ $t('alliance_guess_alliance_info_2', [formatNum(team.total_power || 0)]) }}</div>
            <div>{{ $t('alliance_guess_alliance_info_3', [formatNum(team.total_score)]) }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <TeamUsers
      :show-title="false"
      :crt-team-index="crt"
      :data="allianceStore.computedCrtTeamInfos[crt].users"
      :columns="[
        { lang: 'alliance_guess_member_info_1', prop: 'nick_name' },
        { lang: 'alliance_guess_member_info_3', prop: 'power', width: '135px', isNumber: true },
        { lang: 'alliance_guess_member_info_2', prop: 'score', width: '135px', isNumber: true },
        { lang: 'alliance_guess_member_info_4', prop: 'title', width: '150px' }
      ]"
    >
    </TeamUsers>
  </div>
</template>

<style lang="scss" scoped>
.alliance-section.alliance-list {
  ::v-deep(.table-wrap) {
    @include backgroundSec('alliance/bg-table.png');
    .t-row.odd {
      @include backgroundSec('alliance/bg-trow-odd.png');
    }
    .t-row.even {
      @include backgroundSec('alliance/bg-trow-even.png');
    }
    .t-head {
      color: #727C84;
    }
    .integral-tips .integral-wrap {
      color: #727C84;
      background-color: rgba(0,0,0,.8);
    }
    .t-body {
      color: #BBC0C4;
    }
  }

  .teams-list {
    margin: 0 26px 20px;
    padding: 8px 0 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    @include backgroundSec('alliance/bg-alliance.png');
    .team-item {
      width: 136px;
      height: 250px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      cursor: pointer;
      & + .team-item {
        margin-left: 25px;
      }
    }
    .flag {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      &.active {
        .flag-active {
          display: block;
        }
      }
      .team-logo {
        position: absolute;
        width: 136px;
        height: 158px;
        top: 30px;
        left: 1px;
        z-index: 2;
      }

      &.flag-1 {
        @include backgroundSec('alliance/team/bg_team_1.png');
        .team-logo {
          @include backgroundSec('alliance/team/team_1.png');

        }
        .flag-active {
          @include backgroundSec('alliance/team/bg_team_1_active.png');
        }
      }
      &.flag-2 {
        @include backgroundSec('alliance/team/bg_team_2.png');
        .team-logo {
          @include backgroundSec('alliance/team/team_2.png');

        }
        .flag-active {
          @include backgroundSec('alliance/team/bg_team_2_active.png');
        }
      }
      &.flag-3 {
        @include backgroundSec('alliance/team/bg_team_3.png');
        .team-logo {
          @include backgroundSec('alliance/team/team_3.png');

        }
        .flag-active {
          @include backgroundSec('alliance/team/bg_team_3_active.png');
        }
      }
      &.flag-4 {
        @include backgroundSec('alliance/team/bg_team_4.png');
        .team-logo {
          @include backgroundSec('alliance/team/team_4.png');

        }
        .flag-active {
          @include backgroundSec('alliance/team/bg_team_4_active.png');
        }
      }
      .flag-active {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: -18px;
        z-index: 1;
        display: none;
      }
    }
    .winner {
      width: 176px;
      height: 37px;
      @include backgroundSec('icon-win.png');
      position: absolute;
      top: 15px;
      left: -7px;
      z-index: 2;
    }
    .same-server {
      width: 92px;
      height: 30px;
      position: absolute;
      top: 47px;
      left: 1px;
      @include backgroundSec('bg-server.png');
    }
    .team-info {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
    }
    .numericals {
      width: 100%;
      margin-top: 2px;
      text-align: center;
      white-space: nowrap;
      font-size: 12px;
      line-height: 1;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #E9BE75;
      div {
        margin: 2px 0;
      }
    }
    .team-name {
      height: 13px;
      font-size: 14px;
      font-family: Flareserif821 BT;
      font-weight: normal;
      color: #E1E6EA;
      line-height: 16px;
      display: flex;
      align-items: flex-end;
      text-align: center;
    }
    .divider {
      height: 15px;
      width: 100%;
      margin-top: 7px;
      @include backgroundSec('alliance/team/divider.png');
    }
  }
}
</style>