<script setup lang="ts" name="HomePage">
import { useAllianceStore } from '@/stores';
import RulesComp from '@/components/common/RulesComp.vue'
import BetComp from './BetComp.vue'
import BetLogs from './BetLogs.vue'
import BetSlider from './BetSlider.vue'
import { getAllianceTeamInfo, getAllianceTeamOdds } from '@/api/index'
import { storeToRefs } from 'pinia';
import { ref, computed, onMounted } from 'vue';
import { formatDate, goEventWeb, gtagEvent } from '@/utils/utils'
import { load } from '@/utils/loading';
import i18n from '@/assets/lang';
import TipsComp from '@/components/common/TipsComp.vue';
import MyCoin from './MyCoin.vue';
import AllianceUsers from './AllianceUsers.vue';
import { TOPIC_LANG_MAP } from '@/enum';
import { pushLog } from '@/utils/log';

const props = defineProps({
  rulesState: {
    type: Object,
    default: () => ({
      betTime: 0,
      returnRatio: 0,
      betLimit: 0
    })
  }
})

const allianceStore = useAllianceStore()
const { state, computedCrtAct, computedCrtTeamInfos } = storeToRefs(allianceStore)
const { setTeamInfo, setCrtActIndex, updateTeamOdds } = allianceStore

const guessCount = ref(0)
const betSlider = ref(null)
// 投注
const betComp = ref(null)
const oddsClick = async (coin: number) => {
  load.show()
  await getTeamOdds(crtTopicIndex.value, false)
  load.hide()
  guessCount.value = coin
  ;(betComp.value as any).show()
}

// 题目&赔率
const crtTopicIndex = ref(0)
const crtTeamOdds = computed((): Record<string, string> => {
  const key = computedCrtAct.value.activity_id + '_' + computedCrtAct.value.group + '_' + computedCrtAct.value.topic_info[crtTopicIndex.value]
  return state.value.teamOddsInfo[key] || {}
})

// 当前队伍
const crtTeamIndex = ref(0)
const changeTeam = (i: number) => {
  crtTeamIndex.value = i
}

// 重新请求的倒计时
let reloadTimer: NodeJS.Timeout
// 切换活动
const changeActive = (actIndex: number, mand = false) => {
  if (!mand && state.value.crtActIndex === actIndex) return
  setCrtActIndex(actIndex)
  gtagEvent('change_active', {
    activity_id: computedCrtAct.value.activity_id,
    group: computedCrtAct.value.group
  })
  guessCount.value = 0
  crtTeamIndex.value = 0
  getCrtActTeamInfo(actIndex)
  getTeamOdds(0)
  clearTimeout(reloadTimer)
  pushLog({
    context: {
      season_id: computedCrtAct.value.season,
      zone_pk: state.value.userInfo.zone_id,
      round: computedCrtAct.value.round,
      match_id: computedCrtAct.value.group
    },
    action: 'match_click'
  })
}
// 获取活动队伍数据
const getCrtActTeamInfo = (actIndex: number) => {
  const crtAct = state.value.activeInfo[actIndex]
  load.show()
  getAllianceTeamInfo({ activity_id: crtAct.activity_id, group: crtAct.group })
    .then((res: any) => {
      setTeamInfo(`${crtAct.activity_id}_${crtAct.group}`, res)
    })
    .finally(() => {
      load.hide()

      // 计算当前活动的刷新倒计时 - 投票截止和出结果
      const timeArr = [state.value.activeInfo[actIndex].bet_end_time, state.value.activeInfo[actIndex].result_time].filter(item => item > window.timeNow).sort()
      if (timeArr.length) {
        const time = timeArr[0] - window.timeNow
        if (time < 86400) {
          reloadTimer = setTimeout(() => {
            changeActive(actIndex, true)
          }, time * 1000)
        }
      }
    })
}
// 获取当前场次当前题目下的队伍信息
const getTeamOdds = async (topicIndex: number, isResetTeam = true) => {
  // 打点
  pushLog({
    context: {
      season_id: computedCrtAct.value.season,
      zone_pk: state.value.userInfo.zone_id,
      round: computedCrtAct.value.round,
      match_id: computedCrtAct.value.group
    },
    action: 'question_click',
    question_id: computedCrtAct.value.topic_info[topicIndex]
  })

  crtTopicIndex.value = topicIndex
  const params = {
    activity_id: computedCrtAct.value.activity_id,
    group: computedCrtAct.value.group,
    topic: computedCrtAct.value.topic_info[topicIndex]
  }
  load.show()
  try {
    const res = await getAllianceTeamOdds(params)
    updateTeamOdds(params, res)
    if (!isResetTeam) return
    let heighestKey = ''
    for (const teamId in res) {
      heighestKey = heighestKey ? (res[heighestKey] < res[teamId] ? teamId : heighestKey) : teamId
    }
    computedCrtAct.value.team_info.forEach((team, index) => {
      if (team.team_id === heighestKey) {
        crtTeamIndex.value = index
      }
    })
  } catch (err) {
    console.log(err)
  }
  load.hide()
}
const actEle = ref<HTMLDivElement>()
onMounted(() => {
  // 设置默认活动
  for (let i = 0; i < state.value.activeInfo.length; i++) {
    const act = state.value.activeInfo[i];
    if (act.status === 1) {
      setCrtActIndex(i)
      break
    }
    setCrtActIndex(0)
  }
  changeActive(state.value.crtActIndex, true)
  if (state.value.crtActIndex > 1) {
    actEle.value!.scrollLeft = window.rootFontSize * 320 * state.value.crtActIndex / 75
  }
})

const betSuc = () => {
  (betSlider.value as any).reset()
}

const jumpLog = () => {
  // 打点
  pushLog({
    context: {
      season_id: computedCrtAct.value.season
    },
    action: 'bubble_click'
  })
}

const goEventPage = () => {
  // 打点
  pushLog({
    context: {
      season_id: computedCrtAct.value.season
    },
    action: 'button_click'
  })
  goEventWeb()
}

const recordBtnCLick = () => {
  goEventWeb('/record', {
    topic_data: JSON.stringify(computedCrtTeamInfos.value.map(item => ({
      id: Number(item.alliance_id),
      king: item.king,
      order: item.order,
      power: item.total_power,
      name: item.acronym
    })))
  }, false)
}

</script>

<template>
  <div class="main-page">
    <div class="header">
      <div class="logo"></div>
      <div class="event-center" @click="goEventPage">{{ $t('alliance_guess_top_button') }}</div>
    </div>

    <div class="top">
      <div class="act-title" :class="i18n.global.locale"></div>
      <div class="act-time">
        <div class="t">{{ $t('alliance_guess_start_time', [formatDate(state.activeInfo[state.crtActIndex].game_time)]) }}</div>
        <div class="b">{{ $t('alliance_guess_end_time', [formatDate(state.activeInfo[state.crtActIndex].bet_end_time)]) }}</div>
      </div>
    </div>

    <!-- 比赛场次 -->
    <div class="alliance-section act-list">
      <div class="section-title"><div>{{ $t('alliance_guess_subtitle_1') }}</div></div>
      <div class="content" ref="actEle">
        <div
          v-for="(item, actIndex) in state.activeInfo"
          :key="item.activity_id"
          :class="['act-item', { active: actIndex === state.crtActIndex }]"
          @click="changeActive(actIndex)"
        >
          <div class="bg-active"></div>
          <div class="time">
            <div class="icon"></div>UTC
            {{ formatDate(item.game_time, 'MM/DD hh:mm') }}
          </div>
          <div class="title">{{ $t('alliance_guess_game_name') }}</div>
          <div class="line"></div>
          <div class="body">
            <div class="empty" v-if="item.status === 0">{{ $t('alliance_guess_game_name_none') }}</div>
            <div class="alliance-list" v-else>
              <div class="alliance-item" v-for="(team, teamI) in item.team_info" :key="teamI">[K{{ team.king }}] {{ team.acronym }}</div>
            </div>
          </div>
          <div :class="['status', `status-${item.status}`]">{{ $t(`all_star_guess_state_${item.status}`) }}</div>
        </div>
      </div>
    </div>

    <!-- 竞猜模块 -->
    <div class="alliance-section betting" v-if="computedCrtAct.status > 0">
      <div class="section-title"><div>{{ $t('alliance_guess_subtitle_2') }}</div></div>
      <BetLogs :activity-id="state.activeInfo[state.crtActIndex].activity_id" :group="state.activeInfo[state.crtActIndex].group"></BetLogs>
      <MyCoin></MyCoin>

      <div class="bet-tips">
        <div class="txt-line">{{ $t('alliance_guess_question_detail_0', [computedCrtAct.topic_nums, computedCrtAct.total_bet_nums]) }}</div>
        <div class="txt-line">{{ $t('alliance_guess_question_detail_1', [computedCrtAct.has_bet_nums, computedCrtAct.can_bet_nums]) }}</div>
      </div>

      <!-- 题目 -->
      <div class="topic-list">
        <div class="scroll">
          <div
            v-for="(topicId, i) in computedCrtAct.topic_info"
            :key="computedCrtAct.activity_id+computedCrtAct.group+topicId"
            :class="['topic-item', { active: crtTopicIndex === i }]"
            @click="getTeamOdds(i)"
          >
            {{ $t('alliance_guess_question_title', [i + 1]) }}
          </div>
        </div>
      </div>
      
      <div class="guess">
        <div class="guess-top">
          <div class="btn record-btn" @click="recordBtnCLick">{{ $t('alliance_btn_record') }}</div>
          <div class="topic-num">
            {{ $t('alliance_guess_question_title', [crtTopicIndex + 1]) }}
          </div>
          <div class="topic-name">{{ $t(TOPIC_LANG_MAP[computedCrtAct.topic_info[crtTopicIndex]]) }}</div>
          <div class="betting-list">
            <div
              v-for="(item, i) in computedCrtAct.team_info"
              :key="item.team_id"
              :class="['betting-item', { active: crtTeamIndex === i }]"
              @click="changeTeam(i)"
            >
              <div class="title">{{ $t('all_star_guess_odds_subtitle') }}</div>
              <div class="allance-flag" :class="`flag-${item.order}`"></div>
              <div class="team">{{ item.name }}</div>
              <div class="odds"><div :text="crtTeamOdds[item.team_id]" :class="{ high: Number(crtTeamOdds[item.team_id]) >= 4.00 }">{{ crtTeamOdds[item.team_id] }}</div></div>
            </div>
          </div>
        </div>

        <div class="line-divider"></div>

        <div class="guess-bottom">
          <!-- 可投注 -->
          <BetSlider ref="betSlider" @bet="oddsClick" v-if="computedCrtAct.status === 1" :crt-team-index="crtTeamIndex"></BetSlider>
          <!-- 已截止 -->
          <div class="tips" v-if="computedCrtAct.status >= 2">{{ $t('all_star_guess_result_3') }}</div>
        </div>

      </div>

    </div>

    <!-- 比赛未开始 -->
    <div class="alliance-section betting" v-if="computedCrtAct.status === 0">
      <div class="guess guess-unopen">
        <div class="guess-wrap">
          <div class="tips">{{ $t('all_star_guess_no_team') }}</div>
        </div>
      </div>
    </div>

    <AllianceUsers></AllianceUsers>

    <RulesComp lang-key="alliance_guess_rule_detail" class="alliance-section" :lang-slot="[props.rulesState.betTime, props.rulesState.betLimit, props.rulesState.returnRatio]"></RulesComp>
    <BetComp
      v-if="computedCrtAct.status > 0"
      @betted="betSuc"
      ref="betComp"
      :team-id="computedCrtAct.team_info[crtTeamIndex].team_id"
      :team-name="computedCrtAct.team_info[crtTeamIndex].name"
      :coin="guessCount"
      :odds="computedCrtAct.team_info[crtTeamIndex].odds"
      :activity-id="state.activeInfo[state.crtActIndex].activity_id"
      :topic-id="computedCrtAct.topic_info[crtTopicIndex]"
      :group="state.activeInfo[state.crtActIndex].group"
    />
  </div>
  <TipsComp @jump="jumpLog" />
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/alliance.scss');
</style>
