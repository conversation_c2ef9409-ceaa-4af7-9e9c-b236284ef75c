<script setup lang="ts">
import { ref } from 'vue';
import RulesComp from '@/components/common/RulesComp.vue';
import i18n from '@/assets/lang';
import TipsComp from '@/components/common/TipsComp.vue';
// require styles
import 'swiper/css'
import { pushLog } from '@/utils/log';
import { goEventWeb } from '@/utils/utils';

const actMap = [
  {
    start_day: '06/10',
    start_time: '06/10 20:00',
    end_time: '06/10 19:59'
  },
  {
    start_day: '06/10',
    start_time: '06/10 20:00',
    end_time: '06/10 19:59'
  },
  {
    start_day: '06/10',
    start_time: '06/10 20:00',
    end_time: '06/10 19:59'
  },
  {
    start_day: '06/10',
    start_time: '06/10 20:00',
    end_time: '06/10 19:59'
  },
  {
    start_day: '06/12',
    start_time: '06/12 20:00',
    end_time: '06/12 19:59'
  }
]
const crtActIndex = ref(0)
const goEventPage = () => {
  // 打点
  pushLog({
    context: {
      season_id: 'none'
    },
    action: 'button_click'
  })
  goEventWeb()
}
</script>

<template>
  <div class="main-page landing">
    <div class="header">
      <div class="logo"></div>
      <div class="event-center" @click="goEventPage">{{ $t('alliance_guess_top_button') }}</div>
    </div>
    <div class="top">
      <div class="act-title" :class="i18n.global.locale"></div>
      <div class="act-time">
        <div class="t">{{ $t('all_star_guess_game_time', [actMap[crtActIndex].start_time]) }}</div>
        <div class="b">{{ $t('all_star_guess_end_time', [actMap[crtActIndex].end_time]) }}</div>
      </div>
    </div>

    <!-- 比赛场次 -->
    <div class="alliance-section act-list">
      <div class="section-title"><div>{{ $t('alliance_guess_subtitle_1') }}</div></div>
      <div class="content">
        <div
          v-for="(item, actIndex) in actMap"
          :key="actIndex"
          :class="['act-item', { active: actIndex === crtActIndex }]"
          @click="crtActIndex = actIndex"
        >
          <div class="bg-active"></div>
          <div class="time"><div class="icon"></div> UTC {{ item.start_time }}</div>
          <div class="title">{{ $t('alliance_guess_game_name') }}</div>
          <div class="line"></div>
          <div class="body">
            <div class="empty">{{ $t('alliance_guess_game_name_none') }}</div>
          </div>
          <div :class="['status', 'status-0']">{{ $t(`all_star_guess_state_0`) }}</div>
        </div>
      </div>
    </div>

    <div class="alliance-section betting">
      <div class="guess guess-unopen">
        <div class="guess-wrap">
          <div class="tips">{{ $t('all_star_guess_no_team') }}</div>
        </div>
      </div>
    </div>
    <RulesComp lang-key="alliance_guess_rule_detail" class="alliance-section" :lang-slot="[1, 5, 30]"></RulesComp>
  </div>
  <TipsComp></TipsComp>
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/alliance.scss');
</style>
