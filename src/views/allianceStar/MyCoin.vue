<script setup lang="ts">
import { useAllianceStore } from '@/stores';
import { getImgUrl } from '@/utils/utils';
import { storeToRefs } from 'pinia';
import { ref } from 'vue';

// 竞猜币 tips
const coinComp = ref(null)
const showCoinTips = () => {
  (coinComp.value as any).show()
}
const { state, computedCrtAct } = storeToRefs(useAllianceStore())
</script>

<template>
  <div class="my-coin" v-if="computedCrtAct.status > 0">
    <div class="l">
      <div class="txt">{{ $t('all_star_guess_coin') }}</div>
      <div class="icon"></div>
      <div class="count">{{ state.userInfo.chalons_coin }}</div>
    </div>
    <div class="r" @click="showCoinTips"></div>
  </div>
  <CoinComp ref="coinComp" lang="alliance_guess_detail" :imgs="[getImgUrl('swiper3')]" />
</template>

<style lang="scss" scoped>

.my-coin {
  height: 88px;
  @include backgroundSec('alliance/bg-coin.png');
  margin: 0 26px;
  display: flex;
  padding: 0 28px;
  align-items: center;
  justify-content: space-between;
  .l {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex: 1;
    margin-right: 5px;
  }
  .txt {
    font-size: 24px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #F1E7C4;
    margin-right: 5px;
    @include textStroke(#000000);
  }
  .icon {
    width: 44px;
    height: 44px;
    @include backgroundSec('alliance/icon-gold.png');
  }
  .count {
    font-size: 30px;
    font-family: Calisto MT;
    font-weight: 400;
    color: #F6E2B1;
    line-height: 12px;
    margin-left: 4px;
  }
  .r {
    width: 39px;
    height: 39px;
    @include backgroundSec('icon-q.png');
    cursor: pointer;
  }
}
</style>