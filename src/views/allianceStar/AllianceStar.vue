<script setup lang="ts">
import HomePage from '@/views/allianceStar/HomePage.vue'
import LandingPage from '@/views/allianceStar/LandingPage.vue'
import { reactive, ref } from 'vue';
import { allianceInit } from '@/api';
import { useAllianceStore } from '@/stores'
import InvalidUser from '@/components/common/InvalidUser.vue';
import { load } from '@/utils/loading';
import { getCurrentInstance } from 'vue';
import { onMounted } from 'vue';

const urlParams = JSON.parse(window.sessionStorage.getItem('query') || '{}')

const isActivity = ref(false)
const isInvalidUser = ref(false)
const rulesState = reactive({
  betTime: 0,
  returnRatio: 0,
  betLimit: 0
})

onMounted(() => {
  const { ctx } = getCurrentInstance() as any
  document.getElementsByTagName('title')[0].innerText = ctx.$t('alliance_guess_main_title')
})

const store = useAllianceStore()
const { setActiveInfo, setUserInfo } = store
const initActive = () => {
  load.show()
  allianceInit().then((res: any) => {
    load.hide()
    if (res.activity_info.length && res.activity_info[0].game_time < 1711929600) {
      isActivity.value = false
      isInvalidUser.value = false
    } else {
      isActivity.value = true
      isInvalidUser.value = false
      setActiveInfo(res.activity_info)
      setUserInfo(res.user_info)
      rulesState.betTime = res.bet_countdown
      rulesState.returnRatio = res.return_ratio
      rulesState.betLimit = res.total_bet_nums
    }
  }).catch(err => {
    load.hide()
    if (err.errCode === 7010) {
      isActivity.value = false
      isInvalidUser.value = false
    } else if (err.errCode === 7000) {
      isInvalidUser.value = true
      isActivity.value = false
    }
  })
}

// 有 openid 请求数据
// 无 openid 展示提示页面
if (urlParams.openid) {
  initActive()
} else {
  isInvalidUser.value = true
  isActivity.value = false
}

const showLading = () => {
  isInvalidUser.value = false
  isActivity.value = false
}
</script>

<template>
  <InvalidUser v-if="isInvalidUser" @back="showLading" act-type="allianceStar" />
  <template v-else>
    <HomePage v-if="isActivity" :rules-state="rulesState"/>
    <LandingPage v-else />
  </template>
</template>

<style scoped lang="scss">
</style>
