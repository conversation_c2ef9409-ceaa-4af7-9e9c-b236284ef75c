<script setup lang="ts">
import { ref } from 'vue';
import { allianceBetting } from '@/api'
import { useAllianceStore } from '@/stores';
import { goStore, gtagEvent } from '@/utils/utils'
import { pushLog } from '@/utils/log';
import { storeToRefs } from 'pinia';
const props = defineProps({
  topicId: {
    type: String,
    default: ''
  },
  teamId: {
    type: String,
    default: ''
  },
  teamName: {
    type: String,
    default: ''
  },
  coin: {
    type: Number,
    default: 0
  },
  odds: {
    type: [String, Number],
    default: '0'
  },
  activityId: {
    type: String,
    default: ''
  },
  group: {
    type: String,
    default: ''
  }
})

const store = useAllianceStore()
const { computedCrtAct, state } = storeToRefs(store)
const { updateUserBettind } = store

const isShow = ref(false)
const show = () => {
  isShow.value = true
}

defineExpose({ show })

// ! 投注
const emit = defineEmits(['betted'])
const loading = ref(false)
const submit = () => {
  if (loading.value) return
  loading.value = true
  allianceBetting({ activity_id: props.activityId, group: props.group, team_id: props.teamId, bet_nums: props.coin, topic: props.topicId })
    .then(res => {
      gtagEvent('bet', { activity_id: props.activityId, group: props.group, team_id: props.teamId, bet_nums: props.coin, topic: props.topicId })
      updateUserBettind(res.chalons_coin)
      isShowFirstBet.value = res.is_first
      emit('betted')
      isShow.value = false
    })
    .catch((err) => { setTimeout(() => {
      if (err && (err.errCode === 7021 || err.errCode === 7022)) return
      window.location.reload()
    }, 300) })
    .finally(() => { loading.value = false })
}

const isShowFirstBet = ref(false)

const goStoreClick = () => {
  // 打点
  pushLog({
    context: {
      season_id: computedCrtAct.value.season,
      zone_pk: state.value.userInfo.zone_id,
      round: computedCrtAct.value.round,
      match_id: computedCrtAct.value.group,
    },
    action: 'tp_click'
  })
  goStore()
}

</script>

<template>
  <van-overlay :show="isShow">
    <div class="wrapper">
      <div class="body">
        <div class="close" @click="isShow = false"></div>
        <div class="title">{{ $t('all_star_guess_bet_title') }}</div>
        <div class="line"></div>
        <div class="content bet">
          <div class="txt" v-html="$t('alliance_guess_bet_infomation_1', [props.teamName, props.coin])"></div>
          <div class="odds" v-html="$t('alliance_guess_bet_infomation_2', [, props.odds])"></div>
          <div class="btns">
            <button class="btn cancel" @click="isShow = false">{{ $t('alliance_guess_bet_pop_button_1') }}</button>
            <button class="btn submit" @click="submit">{{ $t('alliance_guess_bet_pop_button_2') }}</button>
          </div>
          <div class="tips">{{ $t('alliance_guess_bet_infomation_3') }}</div>
        </div>
      </div>
    </div>
  </van-overlay>
  <van-overlay :show="isShowFirstBet">
    <div class="wrapper">
      <div class="body">
        <div class="close" @click="isShowFirstBet = false"></div>
        <div class="title">{{ $t('alliance_guess_bet_success_pop_title') }}</div>
        <div class="line"></div>
        <div class="content first-bet">
          <div class="txt">{{ $t('alliance_guess_bet_success_pop_slogan') }}</div>
          <div class="diamond-icon"></div>
          <div class="btn" @click="goStoreClick">{{ $t('alliance_guess_bet_success_pop_button') }}</div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/dialog.scss');
</style>