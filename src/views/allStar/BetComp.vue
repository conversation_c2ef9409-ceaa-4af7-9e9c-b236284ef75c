<script setup lang="ts">
import { ref } from 'vue';
import { betting, getTeamOdds } from '@/api'
import { useStore } from '@/stores';
import { gtagEvent } from '@/utils/utils'
const props = withDefaults(defineProps<{
  teamKey: string
  teamId: string
  coin: number
  odds: string | number
  activityId: string
  group: string
}>(), {
  teamKey: '',
  teamId: '',
  coin: 0,
  odds: '0',
  activityId: '',
  group: ''
})

const { updateTeamOdds, updateUserCoin } = useStore()

const isShow = ref(false)
const show = () => {
  fetchOdds()
  isShow.value = true
}
const fetchOdds = () => {
  getTeamOdds({ activity_id: props.activityId, group: props.group })
    .then(res => updateTeamOdds(res))
}

defineExpose({ show })

// ! 投注
const emit = defineEmits(['betted'])
const loading = ref(false)
const submit = () => {
  if (loading.value) return
  loading.value = true
  betting({ activity_id: props.activityId, group: props.group, team_id: props.teamId, bet_nums: props.coin })
    .then(res => {
      gtagEvent('bet', { activity_id: props.activityId, group: props.group, team_id: props.teamId, bet_nums: props.coin })
      fetchOdds()
      updateUserCoin(res.allstar_coin)
      emit('betted')
      isShow.value = false
    })
    .catch(() => { setTimeout(() => {
      window.location.reload()
    }, 300) })
    .finally(() => { loading.value = false })
}

</script>

<template>
  <van-overlay :show="isShow">
    <div class="wrapper">
      <div class="body">
        <div class="close" @click="isShow = false"></div>
        <div class="title">{{ $t('all_star_guess_bet_title') }}</div>
        <div class="line"></div>
        <div class="content bet">
          <div class="txt" v-html="$t('all_star_guess_bet_infomation_1', [$t(`all_star_${props.teamKey}_name`), props.coin])"></div>
          <div class="odds" v-html="$t('all_star_guess_bet_infomation_2', [, props.odds])"></div>
          <div class="btns">
            <button class="btn cancel" @click="isShow = false">{{ $t('all_star_guess_bet_pop_button_1') }}</button>
            <button class="btn submit" @click="submit">{{ $t('all_star_guess_bet_pop_button_2') }}</button>
          </div>
          <div class="tips">{{ $t('all_star_guess_bet_infomation_3') }}</div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/dialog.scss');
</style>