<script setup lang="ts" name="HomePage">
import { useStore } from '@/stores';
import RulesComp from '@/components/common/RulesComp.vue'
import BetComp from './BetComp.vue'
import { getTeamInfo } from '@/api/index'
import { storeToRefs } from 'pinia';
import { watch, ref } from 'vue';
import { formatDate, formatNum, gtagEvent, getImgUrl, goStore } from '@/utils/utils'
import { onMounted } from 'vue';
import { load } from '@/utils/loading';
import i18n from '@/assets/lang';
import TipsComp from '@/components/common/TipsComp.vue';
import TeamUsers from '@/components/common/TeamUsers.vue';
import CoinComp from '@/components/common/CoinComp.vue';
import { showToast } from 'vant';

const props = defineProps({
  betTime: {
    type: Number,
    default: 0
  },
  totalBetTimes: {
    type: Number,
    default: 1
  }
})

// 修改投注金额
const store = useStore()
const { state } = storeToRefs(store)
const { crtTeamKey, setActInfos } = store
const guessCount = ref(0)
watch(guessCount, nv => {
  if (nv >= state.value.userInfo.allstar_coin) {
    guessCount.value = state.value.userInfo.allstar_coin
  }
  if (nv <= 0) {
    guessCount.value = 0
  }
})
// 投注
const betComp = ref(null)
const oddsClick = () => {
  (betComp.value as any).show()
}

// 竞猜币 tips
const coinComp = ref(null)
const showCoinTips = () => {
  (coinComp.value as any).show()
}

// 当前队伍
const crtTeamIndex = ref(0)
const changeTeam = (i: number) => {
  crtTeamIndex.value = i
}

// 当前活动
const crtActiveIndex = ref(0)
// 重新请求的倒计时
let reloadTimer: NodeJS.Timeout
// 切换活动
const changeActive = (actIndex: number, mand = false) => {
  if (!mand && crtActiveIndex.value === actIndex) return
  gtagEvent('change_active', {
    activity_id: state.value.activeInfo[actIndex].activity_id,
    group: state.value.activeInfo[actIndex].group
  })
  guessCount.value = 0
  crtTeamIndex.value = 0
  crtActiveIndex.value = actIndex
  getCrtActTeamInfo(actIndex)
  clearTimeout(reloadTimer)
}
// 获取活动队伍数据
const getCrtActTeamInfo = (actIndex: number) => {
  const crtAct = state.value.activeInfo[actIndex]
  load.show()
  getTeamInfo({ activity_id: crtAct.activity_id, group: crtAct.group })
    .then((res: any) => {
      setActInfos(res)
      // 未投注时默认选中赔率最高的队伍
      if (state.value.currentActInfo.status === 1 && !state.value.currentActInfo.betting_status) {
        let max = 0, index = 0
        state.value.currentActInfo.team_infos.forEach((item, i) => {
          if (Number(item.odds) > max) {
            max = Number(item.odds)
            index = i
          }
        })
        changeTeam(index)
      }
    })
    .finally(() => {
      load.hide()

      // 计算当前活动的刷新倒计时 - 投票截止和出结果
      const timeArr = [state.value.activeInfo[actIndex].bet_end_time, state.value.activeInfo[actIndex].result_time].filter(item => item > window.timeNow).sort()
      if (timeArr.length) {
        const time = timeArr[0] - window.timeNow
        if (time < 86400) {
          reloadTimer = setTimeout(() => {
            changeActive(actIndex, true)
          }, time * 1000)
        }
      }
    })
}

const actEle = ref<HTMLElement>()
onMounted(() => {
  // 设置默认活动
  for (let i = 0; i < state.value.activeInfo.length; i++) {
    const act = state.value.activeInfo[i];
    if (act.status === 1) {
      crtActiveIndex.value = i
      break
    }
    crtActiveIndex.value = 0
  }
  changeActive(crtActiveIndex.value, true)
  if (crtActiveIndex.value > 1) {
    actEle.value!.scrollLeft = window.rootFontSize * 244 * (crtActiveIndex.value - 0.8) / 75
  }
})

// 投注成功弹窗
const isShowFirstBet = ref(false)
const bettedFn = () => {
  // 当天第一次投注弹窗
  const flag = window.localStorage.getItem('isAvatarBonusPop')
  const popDate = `${(new Date().getFullYear())}/${(new Date().getMonth() + 1)}/${(new Date().getDate())}`
  if (popDate !== flag) {
    window.localStorage.setItem('isAvatarBonusPop', popDate)
    isShowFirstBet.value = true
  }
  else showToast(i18n.global.t('alliance_guess_bet_success'))
  changeActive(crtActiveIndex.value, true)
}

</script>

<template>
  <div class="main-page">
    <div class="top">
      <div class="logo"></div>
      <div class="act-title" :class="i18n.global.locale"></div>
      <div class="act-time">
        <div class="t">{{ $t('all_star_guess_game_time', [formatDate(state.activeInfo[crtActiveIndex].game_time)]) }}</div>
        <div class="b">{{ $t('all_star_guess_end_time', [formatDate(state.activeInfo[crtActiveIndex].bet_end_time)]) }}</div>
      </div>
    </div>
    <div class="act-list" ref="actEle">
      <div
        v-for="(item, actIndex) in state.activeInfo"
        :key="item.activity_id"
        :class="['act-item', { active: actIndex === crtActiveIndex }]"
        @click="changeActive(actIndex)"
      >
        <div class="time">{{ formatDate(item.game_time, 'MM/DD') }}</div>
        <div class="title">{{ $t(`all_star_guess_game_name_${actIndex + 1}`) }}</div>
        <div :class="['status', `status-${item.status}`]">{{ $t(`all_star_guess_state_${item.status}`) }}</div>
      </div>
    </div>
    <div class="my-coin" v-if="state.currentActInfo.status > 0">
      <div class="l">
        <div class="txt">{{ $t('all_star_guess_coin') }}</div>
        <div class="icon"></div>
        <div class="count">{{ state.userInfo.allstar_coin }}</div>
      </div>
      <div class="r" @click="showCoinTips"></div>
    </div>

    <div class="section betting" v-if="state.currentActInfo.status > 0">
      <div class="section-title"><div>{{ $t('all_star_guess_odds_title') }}</div></div>
      <div class="betting-list">
        <div
          v-for="(item, i) in state.currentActInfo.team_infos"
          :key="item.team_id"
          :class="['betting-item', { active: crtTeamIndex === i }]"
          @click="changeTeam(i)"
        >
          <div class="title">{{ $t('all_star_guess_odds_subtitle') }}</div>
          <div class="odds"><div :text="item.odds" :class="{ high: Number(item.odds) >= 4.00 }">{{ item.odds }}</div></div>
          <div class="team">{{ $t(`all_star_team_${i + 1}_name`) }}</div>
        </div>
      </div>

      <div class="guess">
        <!-- 未投注 -->
        <template v-if="state.currentActInfo.betting_status === false">
          <!-- 可投注 -->
          <div class="guess-wrap" v-if="state.currentActInfo.status === 1">
            <i18n-t keypath="all_star_guess_bet_choose" tag="div" class="tips-wrap">
              <span>{{ $t(`all_star_team_${crtTeamIndex + 1}_name`) }}</span>
              <input type="number" onkeyup="value=value.replace(/^(0+)|[^\d]+/g,'')" v-model="guessCount" min="0" :max="state.userInfo.allstar_coin" >
            </i18n-t>
            <div class="slider">
              <van-slider v-model="guessCount" :max="state.userInfo.allstar_coin" />
              <div class="limit min">0</div>
              <div class="limit max">{{ state.userInfo.allstar_coin }}</div>
            </div>
            <button class="button submit-guess" :disabled="guessCount === 0" @click="oddsClick">{{ $t('all_star_guess_bet_button') }}</button>
            <div class="tips">{{ $t('all_star_guess_bet_attention') }}</div>
          </div>
          <!-- 已截止 -->
          <div class="guess-wrap" v-if="state.currentActInfo.status >= 2">
            <div class="tips">{{ $t('all_star_guess_result_3') }}</div>
          </div>
        </template>
        <!-- 已投注 -->
        <template v-if="state.currentActInfo.betting_status === true">
          <!-- 结果未出 -->
          <div class="guess-wrap guess-txt" v-if="state.currentActInfo.status <= 2">
            <div
              class="guess-betting"
              v-html="$t(
                'all_star_guess_result_0',
                [
                  formatDate(state.userBettingInfo.ts),
                  $t(`all_star_team_${crtTeamKey(state.currentActInfo.user_betting.team_id) + 1}_name`),
                  state.currentActInfo.user_betting.bet_nums,
                  state.currentActInfo.user_betting.odds,
                  state.currentActInfo.user_betting.send_nums
                ]
              )"
            ></div>
            <div>{{ $t('all_star_guess_fail_tips', [state.currentActInfo.user_betting.fail_send_nums]) }}</div>
          </div>
          <!-- 结果已处 -->
          <template v-if="state.currentActInfo.status === 3">
            <!-- 赢了 -->
            <div class="guess-wrap guess-txt" v-if="state.currentActInfo.is_winner">
              <div
                class="guess-win"
                v-html="$t(
                  'all_star_guess_result_1',
                  [
                    $t(`all_star_team_${crtTeamKey(state.currentActInfo.user_betting.team_id) + 1}_name`),
                    $t(`all_star_${state.winTeam}_name`),
                    state.currentActInfo.user_betting.send_nums
                  ]
                )"
              ></div>
            </div>
            <!-- 输了 -->
            <div class="guess-wrap guess-txt" v-if="!state.currentActInfo.is_winner">
              <div
                class="guess-loss"
                v-html="$t(
                  'all_star_guess_result_2',
                  [
                    $t(`all_star_${state.winTeam}_name`),
                    $t(`all_star_team_${crtTeamKey(state.currentActInfo.user_betting.team_id) + 1}_name`),
                    state.currentActInfo.user_betting.fail_send_nums
                  ]
                )"
              ></div>
            </div>
          </template>
        </template>

        <div class="line van-hairline--top"></div>

        <div class="teams-list">
          <div
            v-for="(team, i) in state.currentActInfo.team_infos"
            :key="i"
            :class="['team-item', `team-item-${team.order}`, { active: i === crtTeamIndex }]"
            @click="changeTeam(i)"
          >
            <div class="winner" v-if="team.is_winner > 0"></div>
            <div class="same-server" v-if="team.same_kingdom"></div>
            <div class="team-info">
              <div class="same-alliance" v-if="team.same_alliance"></div>
              <div class="numericals">
                <div>{{ $t('all_star_guess_team_power', [formatNum(team.total_power || 0)]) }}</div>
                <div>{{ $t('all_star_guess_team_points', [formatNum(team.total_score)]) }}</div>
              </div>
              <div class="l-line"></div>
              <div class="team-name">{{ $t(`all_star_team_${team.order}_name`) }}</div>
              <div class="s-line"></div>
            </div>
          </div>
        </div>
      </div>

      <TeamUsers
        show-title
        :crt-team-index="crtTeamIndex"
        :team-name="$t(`all_star_team_${state.currentActInfo.team_infos[crtTeamIndex].order}_name`)"
        :data="state.currentActInfo.team_infos[crtTeamIndex].users"
        :columns="[
          { lang: 'all_star_guess_team_member_name', prop: 'nick_name' },
          { lang: 'all_star_guess_team_member_sever', prop: 'king', width: '150px' },
          { lang: 'all_star_guess_team_member_points', prop: 'score', width: '135px', isNumber: true },
          { lang: 'all_star_guess_team_member_power', prop: 'power', width: '135px', isNumber: true }
        ]"
      >
      </TeamUsers>
    </div>

    <div class="section betting" v-if="state.currentActInfo.status === 0">
      <div class="guess guess-unopen">
        <div class="guess-wrap">
          <div class="tips">{{ $t('all_star_guess_no_team') }}</div>
        </div>
      </div>
    </div>

    <RulesComp lang-key="all_star_guess_rule_detail_new" class="section" :lang-slot="[props.betTime, props.totalBetTimes]" />
    <BetComp
      v-if="state.currentActInfo.status > 0"
      @betted="bettedFn"
      ref="betComp"
      :team-id="state.currentActInfo.team_infos[crtTeamIndex].team_id"
      :team-key="`team_${crtTeamIndex + 1}`"
      :coin="guessCount"
      :odds="state.currentActInfo.team_infos[crtTeamIndex].odds"
      :activity-id="state.activeInfo[crtActiveIndex].activity_id"
      :group="state.activeInfo[crtActiveIndex].group"
    />
    <CoinComp ref="coinComp" lang="all_star_guess_coin_detail" :imgs="[getImgUrl('swiper1'), getImgUrl('swiper2')]" />
  </div>
  <TipsComp></TipsComp>
  <!-- 投注胜利弹窗 -->
  <van-overlay :show="isShowFirstBet">
    <div class="wrapper">
      <div class="body">
        <div class="close" @click="isShowFirstBet = false"></div>
        <div class="title">{{ $t('alliance_guess_bet_success_pop_title') }}</div>
        <div class="line"></div>
        <div class="content first-bet">
          <div class="txt">{{ $t('alliance_guess_bet_success_pop_slogan') }}</div>
          <div class="diamond-icon"></div>
          <div class="btn" @click="goStore">{{ $t('alliance_guess_bet_success_pop_button') }}</div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/home.scss');
@import url('@/assets/styles/dialog.scss');
</style>
