<script setup lang="ts">
import { ref } from 'vue';
import RulesComp from '@/components/common/RulesComp.vue';
import i18n from '@/assets/lang';
import TipsComp from '@/components/common/TipsComp.vue';
const actMap = [
  {
    start_day: '12/05',
    start_time: '12/05 20:00',
    end_time: '12/05 19:59'
  },
  {
    start_day: '12/05',
    start_time: '12/05 20:00',
    end_time: '12/05 19:59'
  },
  {
    start_day: '12/05',
    start_time: '12/05 20:00',
    end_time: '12/05 19:59'
  },
  {
    start_day: '12/05',
    start_time: '12/05 20:00',
    end_time: '12/05 19:59'
  },
  {
    start_day: '12/07',
    start_time: '12/07 20:00',
    end_time: '12/07 19:59'
  },
  {
    start_day: '12/07',
    start_time: '12/07 20:00',
    end_time: '12/07 19:59'
  },
  {
    start_day: '12/09',
    start_time: '12/09 20:00',
    end_time: '12/09 19:59'
  }
]
const crtActIndex = ref(0)
</script>

<template>
  <div class="main-page landing">
    <div class="top">
      <div class="logo"></div>
      <div class="act-title" :class="i18n.global.locale"></div>
      <div class="act-time">
        <div class="t">{{ $t('all_star_guess_game_time', [actMap[crtActIndex].start_time]) }}</div>
        <div class="b">{{ $t('all_star_guess_end_time', [actMap[crtActIndex].end_time]) }}</div>
      </div>
    </div>
    <div class="act-list">
      <div class="act-item" :class="{ active: crtActIndex === index }" v-for="(item, index) in actMap" :key="index" @click="crtActIndex = index">
        <div class="time">{{ item.start_day }}</div>
        <div class="title">{{ $t(`all_star_guess_game_name_${index + 1}`) }}</div>
        <div class="status status-2">{{ $t('all_star_guess_state_0') }}</div>
      </div>
    </div>

    <div class="section betting">
      <div class="guess guess-unopen">
        <div class="guess-wrap">
          <div class="tips">{{ $t('all_star_guess_no_team') }}</div>
        </div>
      </div>
    </div>
    <RulesComp lang-key="all_star_guess_rule_detail_new" class="section" :lang-slot="[1, 1]" />
  </div>
  <TipsComp></TipsComp>
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/home.scss');
.logo {
  position: absolute;
  width: 199px;
  height: 69px;
  top: 10px;
  left: 26px;
  @include backgroundSec('logo.png');
}
.act-time {
  margin-top: 3px;
  width: 500px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0));
  font-size: 16px;
  font-weight: 400;
  color: #FEF3CF;
  line-height: 1;
  text-align: center;
  padding: 10px 0;
  white-space: nowrap;
  .b {
    color: #D96E52;
    margin-top: 5px;
  }
}
</style>