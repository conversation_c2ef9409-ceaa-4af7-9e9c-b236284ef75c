<script setup lang="ts">
import HomePage from '@/views/allStar/HomePage.vue'
import LandingPage from '@/views/allStar/LandingPage.vue'
import { getCurrentInstance, ref, onMounted } from 'vue';
import { init } from '@/api';
import { useStore } from '@/stores'
import InvalidUser from '@/components/common/InvalidUser.vue';
import { load } from '@/utils/loading';

const urlParams = JSON.parse(window.sessionStorage.getItem('query') || '{}')

const isActivity = ref(false)
const isInvalidUser = ref(false)
const betTime = ref(0)
const totalBetTimes = ref(1)

onMounted(() => {
  const { ctx } = getCurrentInstance() as any
  document.getElementsByTagName('title')[0].innerText = ctx.$t('all_star_guess_main_title')
})

const store = useStore()
const { setActiveInfo, setUserInfo } = store
const initActive = () => {
  load.show()
  init().then((res: any) => {
    load.hide()
    isActivity.value = true
    isInvalidUser.value = false
    setActiveInfo(res.activity_info)
    setUserInfo(res.user_info)
    betTime.value = res.bet_countdown
    totalBetTimes.value = res.total_bet_nums
  }).catch(err => {
    load.hide()
    if (err.errCode === 7010) {
      isActivity.value = false
      isInvalidUser.value = false
    } else if (err.errCode === 7000) {
      isInvalidUser.value = true
      isActivity.value = false
    }
  })
}

// 有 openid 请求数据
// 无 openid 展示提示页面
if (urlParams.openid) {
  initActive()
} else {
  isInvalidUser.value = true
  isActivity.value = false
}

const showLading = () => {
  isInvalidUser.value = false
  isActivity.value = false
}
</script>

<template>
  <InvalidUser v-if="isInvalidUser" @back="showLading" />
  <template v-else>
    <HomePage v-if="isActivity" :bet-time="betTime" :total-bet-times="totalBetTimes"/>
    <LandingPage v-else />
  </template>
</template>

<style scoped lang="scss">
</style>
