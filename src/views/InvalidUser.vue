<script setup lang="ts">
const emit = defineEmits(['back'])
</script>

<template>
  <div class="invalid">
    <div class="back">
      <img src="@/assets/img/invalid-back.png" alt="" @click="emit('back')">
    </div>
    <div class="line"></div>
    <div class="body">
      <div class="txt">{{ $t('all_star_guess_roadmap') }}</div>
      <img class="tips-img" src="@/assets/img/invalid_user.jpg" alt="">
    </div>
  </div>
</template>

<style lang="scss" scoped>
.invalid {
  height: 100%;
  overflow-y: auto;
  .back {
    width: 100%;
    height: 74px;
    img {
      height: 100%;
    }
  }
  .line {
    width: 100%;
    height: 2px;
    @include backgroundSec('invalid-line.png');
  }
  .body {
    padding: 30px 45px;
  }
  .txt {
    font-size: 24px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #CFC09A;
    line-height: 1.4;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
  }
  .tips-img {
    width: 650px;
    display: block;
    margin: 20px auto;
  }
}
</style>