import { createRouter, createWebHistory } from "vue-router"

const routes = [
  {
    path: '/',
    component: () => import(/* webpackChunkName: 'allstar' */ '@/views/allStar/AllStar.vue')
  },
  {
    path: '/chalons',
    component: () => import(/* webpackChunkName: 'alliance' */ '@/views/allianceStar/AllianceStar.vue')
  },
]

// 3. Create the router instance and pass the `routes` option
// You can pass in additional options here, but let's
// keep it simple for now.
const router = createRouter({
  // 4. Provide the history implementation to use. We are using the hash history for simplicity here.
  history: createWebHistory('/'),
  routes, // short for `routes: routes`
})

export default router