import { useGtag } from "vue-gtag-next"
import languageManage from "./languageManage"

const updateQueryStringParameter = (key: string, value: any): string => {
  const uri = window.location.href
  if(!value) {
    return uri;
  }
  const re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
  const separator = uri.indexOf('?') !== -1 ? "&" : "?";
  if (uri.match(re)) {
    return uri.replace(re, '$1' + key + "=" + value + '$2');
  }
  else {
    return uri + separator + key + "=" + value;
  }
}

const updateUrl = (key: string, value: any) => {
  const newurl = updateQueryStringParameter(key, value)
  //向当前url添加参数，没有历史记录
  window.history.replaceState({
    path: newurl
  }, '', newurl);
}

export const urlHelper = (search?: string) => {
  search = search || window.location.search.slice(1)
  const urlPrams = {} as { [key: string]: string }
  const searchArr = search.split('&')
  for (const value of searchArr.values()) {
    const index = value.indexOf('=')
    const k = value.slice(0, index)
    urlPrams[k] = decodeURIComponent(value.slice(index + 1))
  }
  if (urlPrams.openid) {
    urlPrams.openid = decodeURIComponent(urlPrams.openid)
    setTimeout(() => {
      updateUrl('openid', encodeURIComponent(decodeURIComponent(urlPrams.openid)))
    }, 1000);
  }
  urlPrams.lang = urlPrams.lang || 'en'
  return urlPrams
}

export const getUrlParams = (function () {
  let urlPrams: Record<string, string> = {}
  return function () {
    if (JSON.stringify(urlPrams) === '{}') {
      const tempUrl = urlHelper()
      const localParams = JSON.parse(sessionStorage.getItem('query') || '{}')
      urlPrams = Object.assign({}, localParams, tempUrl)
      if (urlPrams.lang) {
        urlPrams.lang = urlPrams.lang.replace('_', '-')
      }
      sessionStorage.setItem('query', JSON.stringify(urlPrams))
    }
    return urlPrams
  }
}())

export const formatDate = (timestemp: number, format = 'MM/DD HH:mm'): string => {
  if (!timestemp) return ''

  if (timestemp.toString().length === 10) {
    timestemp *= 1e3
  }

  const date = new Date(timestemp)
  const dateStr: { [key: string | number]: string } = {
    //日期
    DD: String(date.getUTCDate()).padStart(2, '0'), // 获取日
    MM: String(date.getUTCMonth() + 1).padStart(2, '0'),
    YYYY: date.getUTCFullYear().toString(), // 获取年
  
    // 时间
    HH:  String(date.getUTCHours()).padStart(2, '0'),       //获取当前小时数(0-23)
    mm: String(date.getUTCMinutes()).padStart(2, '0'),     //获取当前分钟数(0-59)
    ss: String(date.getUTCSeconds()).padStart(2, '0')     //获取当前秒数(0-59)
  }
  for (const item in dateStr) {
    if (new RegExp('(' + item + ')', 'i').test(format)) {
      format = format.replace(
        RegExp.$1,
        dateStr[item].toString().length < 2 ? '0' + dateStr[item] : dateStr[item]
      )
    }
  }

  return format
}

export const formatNum = (number: number | string): number | string => {
  if (typeof number === 'string') {
    number = parseInt(number)
  }
  if (isNaN(number)) return '-'

  const scale = Math.pow(10, 2)

  if (number >= 999999999500) {
    return (Math.round(number / 1000000000000 * scale) / scale).toFixed(2) + 'T'
  } else if (number >= 999999500) {
    return (Math.round(number / 1000000000 * scale) / scale).toFixed(2) + 'B'
  } else if (number >= 999500) {
    return (Math.round(number / 1000000 * scale) / scale).toFixed(2) + 'M'
  } else if (number > 999) {
    return (Math.round(number / 1000 * scale) / scale).toFixed(2) + 'K'
  } else {
    return number
  }
}

export const gtagEvent = (eventName: string, params: object) => {
  const { event } = useGtag()
  event(eventName, params)
}

export const buildURL = (url: string, params: Record<string, string>): string => {
  if (JSON.stringify(params) === '{}') return url
  
  const arr = []
  for (const key in params) {
    const v = decodeURIComponent(params[key])
    arr.push(`${key}=${encodeURIComponent(v)}`)
  }
  return `${url}?${arr.join('&')}`
}

export const loadLanguage = () => {
  const localParams = JSON.parse(sessionStorage.getItem('query') || '{}')
  const lang = localParams.lang.toLowerCase()
  languageManage.fetchLang(lang)
}

export const getImgUrl = (name: string) => {
  return new URL(`../assets/img/${name}.png`, import.meta.url).href
}

export const goStore = () => {
  const openid = JSON.parse(window.sessionStorage.getItem('query') || '{}').openid
  let str = 'utm_campaign=all_star_guess'
  if (openid) {
    str += '&openid=' + encodeURIComponent(openid)
  }
  window.open(import.meta.env.VITE_THIRD_PAY_URL + '?' + str)
}

export const goEventWeb = (path: string = '', extraParams: Record<string, any> | undefined = undefined, blank: boolean = true) => {
  const query = JSON.parse(window.sessionStorage.getItem('query') || '{}')
  const parmasArr: string[] = []
  for (const key in query) {
    if (!key) continue
    if (key === 'openid') {
      parmasArr.push(`${key}=${encodeURIComponent(query[key])}`)
    } else {
      parmasArr.push(`${key}=${query[key]}`)
    }
  }
  if (extraParams) {
    for (const key in extraParams) {
      parmasArr.push(`${key}=${extraParams[key]}`)
    }
  }
  blank
    ? window.open(import.meta.env.VITE_EVENT_TOPICS_URL + path + '?' + parmasArr.join('&'))
    : window.location.href = import.meta.env.VITE_EVENT_TOPICS_URL + path + '?' + parmasArr.join('&')
}

export default {
  urlHelper,
  getUrlParams,
  formatDate,
  formatNum,
  gtagEvent,
  buildURL
}
