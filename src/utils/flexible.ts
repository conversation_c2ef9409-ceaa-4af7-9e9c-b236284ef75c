(function flexible (window, document) {
  const docEl = document.documentElement
  const dpr = window.devicePixelRatio || 1
  function isMobile () {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  // adjust body font size
  function setBodyFontSize () {
    if (document.body) {
      document.body.style.fontSize = (12 * dpr) + 'px'
    } else {
      document.addEventListener('DOMContentLoaded', setBodyFontSize)
    }
  }
  setBodyFontSize()

  // set 1rem = viewWidth / 10
  function setRemUnit () {
    const rem = docEl.clientWidth / 10
    docEl.style.fontSize = rem + 'px'
    const ratio = docEl.getBoundingClientRect().width / docEl.getBoundingClientRect().height
    if (ratio > 1.8) {
      docEl.style.fontSize = (rem * (1.8 / ratio)) + 'px'
    }
    if (window.innerWidth > 576 && !isMobile()) {
      docEl.style.fontSize = '57.6px'
    }
    window.rootFontSize = parseFloat(docEl.style.fontSize)
  }

  setRemUnit()

  // reset rem unit on page resize
  window.addEventListener('resize', setRemUnit)
  window.addEventListener('pageshow', function (e) {
    if (e.persisted) {
      setRemUnit()
    }
  })

  // detect 0.5px supports
  if (dpr >= 2) {
    const fakeBody = document.createElement('body')
    const testElement = document.createElement('div')
    testElement.style.border = '.5px solid transparent'
    fakeBody.appendChild(testElement)
    docEl.appendChild(fakeBody)
    if (testElement.offsetHeight === 1) {
      docEl.classList.add('hairlines')
    }
    docEl.removeChild(fakeBody)
  }

  // 计算最终html font-size
  function modifileRootRem () {
    const root = window.document.documentElement
    const fontSize = parseFloat(root.style.fontSize)
    const finalFontSize = parseFloat(window.getComputedStyle(root).getPropertyValue('font-size'))
    if (finalFontSize === fontSize) return
    root.style.fontSize = fontSize + (fontSize - finalFontSize) + 'px'
  }
  if (typeof window.onload === 'function') {
    const oldFun = window.onload as any
    window.onload = function () {
      oldFun()
      modifileRootRem()
    }
  } else {
    window.onload = modifileRootRem
  }
}(window, document))
