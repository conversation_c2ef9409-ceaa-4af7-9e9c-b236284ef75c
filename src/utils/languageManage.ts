import { loadLang } from "@/api"
import i18n from "@/assets/lang"

export type TLangKey = 'zh-cn' | 'en' | 'zh-tw' | 'fr' | 'de' | 'es' | 'it' | 'pl' | 'ru' | 'nl' | 'id' | 'ja' | 'ko' | 'pt' | 'sv' | 'th' | 'tr' | 'ar' | 'vi'

class LanguageManage {
  version: string
  readonly LOCALSTORAGE_KEY: string = '_ST_LANGUAGE_'
  langConf: {
    version: string
    conf: Record<TLangKey, Record<string, string>>
  }

  constructor(version: string) {
    this.version = version
    const localLang = JSON.parse(window.localStorage.getItem(this.LOCALSTORAGE_KEY) || '{}')
    if (localLang.version !== this.version || !localLang.version) {
      localLang.version = this.version
      localLang.conf = {}
    }
    this.langConf = localLang
  }

  setLocalLang (lang: TLangKey, conf: Record<string, string>) {
    this.langConf.conf[lang] = conf
    i18n.global.setLocaleMessage(lang, conf)
    window.localStorage.setItem(this.LOCALSTORAGE_KEY, JSON.stringify(this.langConf))
  }

  fetchLang (lang: TLangKey) {
    if (this.langConf.conf[lang]) {
      this.setLocalLang(lang, this.langConf.conf[lang])
    } else {
      loadLang(lang).then(res => {
        if (typeof res === 'object') {
          this.setLocalLang(lang, res)
          i18n.global.setLocaleMessage(lang, res)
        }
      })
    }
  }
}

export default new LanguageManage(import.meta.env.VITE_VERSION)
