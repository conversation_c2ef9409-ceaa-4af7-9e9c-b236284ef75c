import { createApp, reactive } from "vue";
import CustomLoading from '@/components/loading/CustomLoading.vue'

const conf = reactive({
  show: false
})
let timer = null as any

const $loading = createApp(CustomLoading, {conf}).mount(document.createElement('div'))
export const load = {
  show () {
    clearTimeout(timer)
    timer = null
    if (conf.show) return
    conf.show = true
    document.body.appendChild($loading.$el)
  },
  hide () {
    if (timer) { return }
    timer = setTimeout(() => {
      conf.show = false
    }, 100)
  }
}
