import { setLog } from '@/api'
import { useAllianceStore } from '@/stores'
import { storeToRefs } from 'pinia'
export const pushLog = (logInfo: Object) => {
  const store = useAllianceStore()
  const { state } = storeToRefs(store)
  const urlParams = JSON.parse(window.sessionStorage.getItem('query') || '{}')

  const info: Record<string, string | Object> = {
    data_version: '3.0',

    fpid: state.value.userInfo.fpid,
    pkg_channel: urlParams.pkg,
    game_uid: state.value.userInfo.uid,
    gameserver_id: state.value.userInfo.kingdom,
    app_id: urlParams.gameid
  }
  info.properties = {
    pkg_channel: urlParams.pkg,
    game_uid: state.value.userInfo.uid,
    gameserver_id: state.value.userInfo.kingdom,
    action_name: 'chalons',
    ...logInfo
  }
  const params = {
    log: JSON.stringify(info)
  }
  setLog(params)
}