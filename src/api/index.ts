import request from "@/server";

// 埋点
export const setLog = (params: {}) => request.post('/api/activity/logger', params)

// 初始化活动列表框
export const init = () => request.get('/api/activity/quiz/getActInfo')

// 获取当前活动信息
export const getTeamInfo = (params?: {}) => request.post('/api/activity/quiz/getTeamInfo', params)

// 获取参赛队伍信息
export const getTeamUser = (params: {}) => request.post('/api/activity/quiz/getTeamUser', params)

// 获取赔率
export const getTeamOdds = (params: {}) => request.post('/api/activity/quiz/getTeamOdds', params)

// 投注
export const betting = (params: {}) => request.post('/api/activity/quiz/betting', params)

export const loadLang = (lang: string) => request.get(`${import.meta.env.VITE_CDN_URL}lang/${lang}.json`)


/** !联盟赛事相关 */
// 初始化活动列表框
export const allianceInit = () => request.get('/api/activity/allianceQuiz/getActInfo')
// export const allianceInit = () => new Promise((resolve) => {
//   resolve(JSON.parse('{"bet_countdown":1,"total_bet_nums":500,"return_ratio":30,"user_info":{"uid":"100005926","fpid":************,"name":"100005926","icon":"************_1690539175.jpg","portrait":10001,"kingdom":10001,"vip_level":2,"total_pay":1014.88,"city_level":21,"last_login":1695713693,"alliance_id":100000706,"zone_id":10001,"allstar_coin":0,"chalons_coin":1000000},"activity_info":[{"id":23,"activity_id":"7|10001","group":"group_a","game_time":1711571829,"result_time":1711579029,"sync_status":0,"is_send":0,"bet_end_time":1711571769,"status":0,"team_info":[{"id":122,"activity_id":"7|10001","group":"group_a","team_id":"57","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"57","order":1},{"id":123,"activity_id":"7|10001","group":"group_a","team_id":"58","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"58","order":2},{"id":124,"activity_id":"7|10001","group":"group_a","team_id":"59","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"59","order":3},{"id":125,"activity_id":"7|10001","group":"group_a","team_id":"60","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"60","order":4}],"topic_info":["topic_1","topic_2","topic_3","topic_4"],"topic_nums":4,"total_bet_nums":500,"has_bet_nums":0,"can_bet_nums":500,"season":"7"},{"id":24,"activity_id":"7|10001","group":"group_b","game_time":1711571829,"result_time":1711579029,"sync_status":1,"is_send":0,"bet_end_time":1711571769,"status":1,"team_info":[{"id":126,"activity_id":"7|10001","group":"group_b","team_id":"61","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"61","order":1},{"id":127,"activity_id":"7|10001","group":"group_b","team_id":"62","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"62","order":2},{"id":128,"activity_id":"7|10001","group":"group_b","team_id":"63","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"63","order":3},{"id":129,"activity_id":"7|10001","group":"group_b","team_id":"64","king":"0","name":"联盟名称","acronym":"PKG","alliance_id":"64","order":4}],"topic_info":["topic_1","topic_2","topic_3","topic_4"],"topic_nums":4,"total_bet_nums":500,"has_bet_nums":10,"can_bet_nums":490,"season":"7"},{"id":25,"activity_id":"7|10001","group":"group_c","game_time":1711744629,"result_time":1711751829,"sync_status":0,"is_send":0,"bet_end_time":1711744569,"status":0,"team_info":[],"topic_info":[],"topic_nums":0,"total_bet_nums":500,"has_bet_nums":0,"can_bet_nums":500,"season":"7"}]}'))
// })

// 联盟队伍成员信息
export const getAllianceTeamInfo = (params?: {}) => request.post('/api/activity/allianceQuiz/getTeamInfo', params)
// export const getAllianceTeamInfo = () => new Promise(resolve => resolve(JSON.parse('[{"id":126,"activity_id":"7|10001","group":"group_b","team_id":"61","king":"0","alliance_id":"100002248","order":1,"total_power":46324614853,"total_score":0,"users":[{"id":3213,"activity_id":"7|10001","group":"group_b","team_id":"61","uid":14,"nick_name":"robot_110408","power":477573349,"title":"4","score":0},{"id":3214,"activity_id":"7|10001","group":"group_b","team_id":"61","uid":18,"nick_name":"robot_110412","power":477573349,"title":"4","score":0}]},{"id":127,"activity_id":"7|10001","group":"group_b","team_id":"62","king":"0","alliance_id":"100002248","order":2,"total_power":0,"total_score":0,"users":[]},{"id":128,"activity_id":"7|10001","group":"group_b","team_id":"63","king":"0","alliance_id":"100002248","order":3,"total_power":46324614853,"total_score":0,"users":[{"id":3310,"activity_id":"7|10001","group":"group_b","team_id":"63","uid":18,"nick_name":"robot_110412","power":477573349,"title":"4","score":0},{"id":3311,"activity_id":"7|10001","group":"group_b","team_id":"63","uid":19,"nick_name":"robot_110413","power":477573349,"title":"4","score":0}]},{"id":129,"activity_id":"7|10001","group":"group_b","team_id":"64","king":"0","alliance_id":"100002248","order":4,"total_power":43936748108,"total_score":0,"users":[{"id":3407,"activity_id":"7|10001","group":"group_b","team_id":"64","uid":14,"nick_name":"robot_110408","power":477573349,"title":"4","score":0},{"id":3408,"activity_id":"7|10001","group":"group_b","team_id":"64","uid":15,"nick_name":"robot_110409","power":477573349,"title":"4","score":0},{"id":3409,"activity_id":"7|10001","group":"group_b","team_id":"64","uid":19,"nick_name":"robot_110413","power":477573349,"title":"4","score":0}]}]')))

// 获取赔率
export const getAllianceTeamOdds = (params: {}) => request.post('/api/activity/allianceQuiz/getTeamOdds', params)

// 投注
export const allianceBetting = (params: {}) => request.post('/api/activity/allianceQuiz/betting', params)

// 投注记录
export const allianceBettingLog = (params: {}) => request.post('/api/activity/allianceQuiz/bettingLog', params)