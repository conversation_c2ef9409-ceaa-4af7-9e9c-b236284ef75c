*{
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  // 禁止长按选择
  -webkit-touch-callout:none;
  -webkit-user-select:none;
  -khtml-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
}
input {
  -webkit-user-select: auto !important;
}
html, body{ width: 100%; height: 100%; overflow: hidden; position: relative; background: transparent; font-family: "Helvetica Neue",Helvetica,Arial,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;}
body, h1, h2, h3, h4, h5, h6, hr, p, blockquote, dl, dt, dd, ul, ol, li, pre, form, fieldset, legend, button, input, textarea, th, td { margin:0; padding:0; }
body, button, input, select, textarea { font:12px/1.5tahoma, arial, \5b8b\4f53; }
h1, h2, h3, h4, h5, h6{ font-size:100%; }
address, cite, dfn, em, var { font-style:normal; }
code, kbd, pre, samp { font-family:couriernew, courier, monospace; }
small{ font-size:12px; }
ul, ol { list-style:none; }
a { text-decoration:none; outline: none; -webkit-tap-highlight-color: transparent; }
button { outline: none; border: 0; }
a:hover { text-decoration: none; }
sup { vertical-align:text-top; }
sub{ vertical-align:text-bottom; }
legend { color:#000; }
fieldset, img { border:0; }
button, input, select, textarea { font-size:100%; }
button, html [type="button"], [type="reset"], [type="submit"]{-webkit-appearance: none;}
ul{margin: 0;padding: 0;}
li{list-style-type: none;}
p{margin: 0;}
i{font-style: normal;}

body {
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.button{
  text-align: center;
  display: block;
  // document.body.addEventListener('touchstart', function () {})
  transition: transform .1s linear;
  cursor: pointer;
  &:active{
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
    transform: scale(0.9);
  }
  font-size: .213333rem /* 16/75 */;
  color: #eee;
  text-align: center;
  &:disabled {
    opacity: .5;
    cursor: not-allowed;
    &:active {
      transform: none;
      filter: none;
    }
  }
}
.scrollBox{
  @include scrollbox();
}
.scrollBoxX{
  @include scrollboxX();
}

// 道具品质
.quality-1 { color: #f9f9f4 !important; }
.quality-2 { color: #4aa06b !important; }
.quality-3 { color: #6081c4 !important; }
.quality-4 { color: #a160d3 !important; }
.quality-5 { color: #d56625 !important; }

// 弹窗信息
.popupWrap{
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 11;
  background: rgba(0, 0, 0, .5);
  .content{
    max-height: 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 11;
    overflow: hidden;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    .title{
      width: 2.88rem /* 216/75 */;
      height: .32rem /* 24/75 */;
      margin-left: -.066667rem /* 5/75 */;
      line-height: .32rem /* 24/75 */;
      text-align: center;
      position: absolute;
      top: .106667rem /* 8/75 */;
      left: 50%;
      // background: linear-gradient(to bottom, #c8943a,#e4c361);
      // -webkit-text-fill-color: transparent;
      // -webkit-background-clip: text;
      color: #d7ad4e;
      font-weight: normal;
      font-size: .213333rem /* 16/75 */;
      -webkit-transform: translateX(-50%);
      transform: translateX(-50%);
      @include textShadow();
    }
    .close{
      width: .533333rem /* 40/75 */;
      height: .533333rem /* 40/75 */;
      position: absolute;
      top: .08rem /* 6/75 */;
      right: 0;
    }
  }
}

.gradient{
  color: #E7C883;
  text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.68);
  &::after{
    content: attr(text);
    width: 100%;
    height: 100%;
    position: absolute;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    z-index: 3;
    color: #F1E7C4;
    -webkit-mask-image: linear-gradient(transparent 30%, #F1E7C4);
    // background: linear-gradient(0deg, #F1E7C4 0%, #E7C883 100%);
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
    left: 0;
    top: 0;
  }
}

@keyframes loading {
  0%{
    transform: rotateY(0);
  }
  100%{
    transform: rotateY(360deg);
  }
}

.imageTpl{
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

.bg-lazy {
  &[lazy=loading] {
    background-size: 76px 82px;
    opacity: 0.1;
  }
}

.btn{
  &:not(.disable) {
    cursor: pointer;
  }
  &:not(.disable):active{
    transform: scale(0.95);
    -webkit-filter: brightness(.85);
    filter: brightness(.85);
  }
  &.disable{
    filter: grayscale(1);
  }
}

#app {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #171612;
  max-width: 750px;
  color: #fff;
}

.my-coin { position: relative; z-index: 2; }
.section {
  .section-title {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 55px 0;
    position: relative;
    &::before {
      pointer-events: none;
      position: absolute;
      width: 750px;
      height: 463px;
      content: "";
      top: calc(50% + 5px);
      left: 50%;
      transform: translate(-50%, -50%);
      @include backgroundSec('title-cycle.png');
    }
    div {
      font-size: 40px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #F6E2B1;
      line-height: 1.1;
    
      background: linear-gradient(0deg, #E3A04D 0%, #FFF9D7 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;
      &::after, &::before {
        position: absolute;
        width: 163px;
        height: 22px;
        content: "";
        top: 50%;
        transform: translateY(-50%);
      }
      &::after {
        @include backgroundSec('bg-title.png');
        left: -175px;
      }
      &::before {
        @include backgroundSec('bg-title-2.png');
        right: -175px;
      }
    }
  }
}
.alliance-section {
  .section-title {
    padding-left: 44px;
    height: 30px;
    margin-top: 50px;
    margin-bottom: 15px;
    div {
      font-size: 26px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      line-height: 30px;
      color: #EFDEA4;
      padding-left: 30px;
      position: relative;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 26px;
        height: 30px;
        background-color: red;
      }
      &::before {
        @include backgroundSec('alliance/icon-section.png');
      }
    }
  }
  .rule {
    color: #B7B19F !important;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.8) !important;
  }
}

.table-wrap {
  width: 703px;
  height: 808px;
  margin: 0 auto;
  padding: 3px 12px 5px;
  @include backgroundSec('bg-table.png');
  .t-row {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 70px;
    padding: 0 4px;
    position: relative;
    &+.t-row {
      margin-top: 8px;
    }
    &.odd { @include backgroundSec('bg-trow-odd.png'); }
    &.even { @include backgroundSec('bg-trow-even.png'); }
    .same-alliance {
      width: 85px;
      height: 18px;
      position: absolute;
      z-index: 1;
      left: 0;
      top: 0;
      @include backgroundSec('bg-alliance.png');
    }
  }
  .t-cell {
    position: relative;
    padding: 0 4px;
    text-align: center;
    &.t-cell-nick_name { flex: 1; }
    &.t-cell-king { width: 150px; }
    &.t-cell-score { width: 135px; }
    &.t-cell-power { width: 135px; }
  }
  .score {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .integral-tips {
    position: relative;
    margin-left: 4px;
    .integral-icon {
      width: 26px;
      height: 26px;
      @include backgroundSec('icon-tips.png')
    }
    .integral-wrap {
      position: absolute;
      top: 37px;
      z-index: 1;
      left: -40px;
      padding: 9px 10px;
      font-size: 20px;
      line-height: 1.3;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #E3D09A;
      background: rgba(0, 0,0,0.7);
      border: 1px solid rgba(227, 208, 154, 0.3);
      border-radius: 3px;
      text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
      min-width: 200px;
      &::before {
        content: "";
        position: absolute;
        width: 13px;
        height: 10px;
        @include backgroundSec('bg-tips-arr.png');
        top: -8px;
        left: 46px;
      }
    }
  }
  .t-head {
    font-size: 22px;
    line-height: 1;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #E3D09A;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
  }
  .t-body {
    font-size: 18px;
    font-family: Flareserif821 BT;
    font-weight: normal;
    color: #D3CBB4;
    line-height: 1.3;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
    max-height: 725px;
    overflow-y: auto;
    .t-cell-1 {
      text-align: left;
      overflow: hidden;
      @include twoLinetextOverflow();
    }
  }
}

.d-f { display: flex; }
.d-f-c { flex-direction: column; }
.d-f-center {
  justify-content: center;
  align-items: center;
}