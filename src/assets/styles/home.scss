.main-page {
  min-height: 100%;
  @include backgroundSec('bg.jpg', center, center, no-repeat, 100%, auto);
}
.top {
  @include backgroundSec('bg-top.jpg', top, center, no-repeat, 100%, auto);
  min-height: 400px;
  position: relative;
  padding-top: 256px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  .logo {
    position: absolute;
    width: 199px;
    height: 69px;
    top: 10px;
    left: 26px;
    @include backgroundSec('logo.png');
  }
  @mixin textStroke($w, $h, $l) {
    width: $w + px;
    height: $h + px;
    @include backgroundSec('title/' + $l + '.png');
  }
  .act-title {
    position: absolute;
    bottom: 147px;
    left: 50%;
    transform: translateX(-50%);
    &.zh-cn { @include textStroke(587, 133, 'zh-cn'); }
    &.zh-tw { @include textStroke(587, 135, 'zh-tw'); }
    &.en { @include textStroke(688, 95, 'en'); }
    &.ja { @include textStroke(709, 114, 'ja'); }
    &.ko { @include textStroke(564, 114, 'ko'); }
    &.tr { @include textStroke(646, 122, 'tr'); }
    &.ar { @include textStroke(526, 121, 'ar'); }
    &.vi { @include textStroke(706, 133, 'vi'); }
    &.fr { @include textStroke(596, 161, 'fr'); }
    &.de { @include textStroke(574, 162, 'de'); }
    &.es { @include textStroke(668, 170, 'es'); }
    &.it { @include textStroke(728, 162, 'it'); }
    &.pl { @include textStroke(677, 162, 'pl'); }
    &.ru { @include textStroke(689, 156, 'ru'); }
    &.nl { @include textStroke(530, 163, 'nl'); }
    &.id { @include textStroke(680, 162, 'id'); }
    &.pt { @include textStroke(637, 164, 'pt'); }
    &.sv { @include textStroke(681, 163, 'sv'); }
    &.th { @include textStroke(699, 120, 'th'); }
  }
  .act-time {
    margin-top: 3px;
    width: 500px;
    background: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0));
    font-size: 16px;
    font-weight: 400;
    color: #FEF3CF;
    line-height: 1;
    text-align: center;
    padding: 10px 0;
    white-space: nowrap;
    .b {
      color: #D96E52;
      margin-top: 5px;
    }
  }
}
.act-list {
  margin: -27px 25px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow-x: auto;
  .act-item {
    flex-shrink: 0;
    width: 244px;
    height: 257px;
    padding-top: 8px;
    @include backgroundSec('bg-contest.png');
    position: relative;
    transition: transform .1s linear;
    cursor: pointer;
    &+.act-item {
      margin-left: 10px;
    }
    &:active{
      transform: scale(0.97);
    }
    &.active {
      @include backgroundSec('bg-contest-active.png');
      .time {
        color: #CBB08B;
      }
      .title {
        color: #FFEA95;
        @include textStroke(#3B2F21);
      }
    }
    .time {
      width: 100%;
      height: 42.9px;
      text-align: center;
      line-height: 42.9px;
      font-size: 20px;
      font-weight: 400;
      color: #85755F;
    }
    .title {
      height: 137.5px;
      padding: 0 10px;
      font-size: 22px;
      font-family: Source Han Serif CN;
      font-weight: bold;
      color: #B59379;
      line-height: 26px;
      text-align: center;
      @include textStroke(#3B2F21);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .status {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #D96E52;
      line-height: 1;
      position: absolute;
      height: 72.6px;
      display: flex;
      align-items: center;
      justify-content: center;
      bottom: 0;
      width: 100%;
      text-align: center;
      &.status-0 { color: #D96E52; }
      &.status-1 { color: #75D952; }
      &.status-2 { color: #D96E52; }
      &.status-3 { color: #6F5D4F; }
    }
  }
}
.my-coin {
  height: 88px;
  @include backgroundSec('bg-count.png');
  margin: 45px 26px 0;
  display: flex;
  padding: 0 28px;
  align-items: center;
  justify-content: space-between;
  .l {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex: 1;
    margin-right: 5px;
  }
  .txt {
    font-size: 24px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #F1E7C4;
    margin-right: 5px;

    background: linear-gradient(0deg, #F3DEAF 0%, #FFD066 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon {
    width: 38px;
    height: 44px;
    @include backgroundSec('icon-gold.png');
  }
  .count {
    font-size: 30px;
    font-family: Calisto MT;
    font-weight: 400;
    color: #F6E2B1;
    line-height: 12px;
    text-shadow: 0px 2px 0px rgba(0,0,0,0.5);
    margin-left: 4px;
  }
  .r {
    width: 39px;
    height: 39px;
    @include backgroundSec('icon-q.png');
    cursor: pointer;
  }
}
.section.betting {
  .betting-list {
    display: flex;
    padding: 0 27px;
    align-items: center;
    justify-content: space-between;
  }
  .betting-item {
    width: 165px;
    height: 169px;
    position: relative;
    @include backgroundSec('bg-odds.png');
    z-index: 0;
    transition: transform .1s linear;
    cursor: pointer;
    &:active{
      transform: scale(0.97);
    }
    &.active::before {
      content: "";
      position: absolute;
      width: 174px;
      height: 179px;
      top: -5px;
      left: -5px;
      @include backgroundSec('bg-odds-border.png');
      pointer-events: none;
    }
  }
  .title {
    position: absolute;
    left: 14px;
    right: 14px;
    top: 10px;
    font-size: 18px;
    font-family: PingFang SC;
    font-weight: 400;
    color: #B59379;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.7);
  }
  .odds {
    width: 137px;
    height: 118px;
    position: absolute;
    left: 14px;
    top: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    font-family: Arial;
    font-weight: 400;
    color: #F6E2B1;
    line-height: 1;
    text-shadow: 0px 2px 0px rgba(0,0,0,0.5);
    div.high {
      position: relative;
      font-size: 30px;
      font-family: Arial;
      font-weight: 600;
      color: transparent;
      text-shadow: none;
      background: linear-gradient(0deg, #F8B514 0%, #FFE87B 100%);
      -webkit-background-clip: text;
      &::before {
        content: attr(text);
        position: absolute;
        z-index: -1;
        @include textStroke(#3B2F21);
        text-shadow: 0 3px 0px black;
      }
    }
  }
  .team {
    width: 154px;
    height: 44px;
    position: absolute;
    left: 5px;
    bottom: 4px;
    right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #FDF3CE;
    line-height: 1;
    text-align: center;
    @include textStroke(#000000);
  }

  .guess {
    width: 697px;
    min-height: 688px;
    @include backgroundSec('bg-2.png');
    margin: 40px auto 0;
    position: relative;
    // 比赛未开始
    &.guess-unopen {
      min-height: 334px;
      height: 334px;
      .guess-wrap {
        height: 100%;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .tips {
          margin: 0;
        }
      }
    }
    .guess-wrap {
      padding-top: 93px;
      min-height: 265px;
      // 已投注未出结果
      &.guess-txt {
        padding: 0;
        line-height: 1.4;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: center;
        font-size: 20px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #D3CBB4;
        padding: 0 28px;
        // @include textStroke(#000000);
        .guess-betting {
          ::v-deep(i:nth-of-type(1)) {
            color: #CFC09A;
            margin-bottom: 10px;
            line-height: 1;
            display: inline-block;
          }
          ::v-deep(i:nth-of-type(3)) {
            color: #77D92E;
            text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
          }
          ::v-deep(i:nth-of-type(4)) {
            display: inline-block;
            color: transparent;
  
            background: linear-gradient(0deg, #F8B514 0%, #FFE87B 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          ::v-deep(i:nth-of-type(5)) {
            color: #F6E2B1;
            @include textStroke(rgba(0, 0, 0, 0.5));
          }
        }
        .guess-win {
          ::v-deep(i:nth-of-type(3)) {
            color: #77D92E;
          }
          ::v-deep(i:nth-of-type(4)) {
            font-weight: bolder;
            color: #F6E2B1;
            @include textStroke(rgba(0, 0, 0, 0.5));
          }
        }
        .guess-loss {
          ::v-deep(i:nth-of-type(3)) {
            color: #D9423B;
          }
        }
      }
      .tips-wrap {
        font-size: 20px;
        position: absolute;
        top: 24px;
        left: 28px;
        right: 28px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #CFC09A;
        line-height: 1.1;
        @include textStroke(#000000);
      }
      input {
        display: inline-block;
        min-width: 130px;
        width: auto;
        height: 35px;
        background-color: none;
        @include backgroundSec('bg-input.png');
        border: none;
        font-size: 20px;
        font-family: Flareserif821 BT;
        font-weight: normal;
        color: #77D92E;
        line-height: 35px;
        text-align: center;
        -moz-appearance:textfield;
        &::-webkit-inner-spin-button, 
        &::-webkit-outer-spin-button { 
          -webkit-appearance: none;
          margin: 0; 
        }
      }
      .slider {
        margin: 0 30px;
        position: relative;
        .limit {
          position: absolute;
          font-size: 18px;
          font-family: Flareserif821 BT;
          font-weight: normal;
          color: #E2D3A4;
          line-height: 1;
          @include textStroke(#000000);
          bottom: -35px;
        }
        .min {
          left: 0;
        }
        .max {
          right: 0;
        }
      }
      .submit-guess {
        width: 214px;
        height: 58px;
        margin: 40px auto 0;
        @include backgroundSec('bg-btn.png');
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #FEFFF5;
      }
      .tips {
        text-align: center;
        font-size: 18px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #D9423B;
        line-height: 1;
        margin-top: 10px;
      }
    }
    .line {
      margin: 0 35px;
      &.van-hairline--top:after {
        border-color: #62593C;
      }
    }
    .teams-list {
      margin: 25px 25px 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .team-item {
        width: 156px;
        height: 352px;
        position: relative;
        padding-bottom: 63px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        cursor: pointer;
        &.active::before {
          content: "";
          position: absolute;
          width: 176px;
          height: 371px;
          left: -11px;
          top: -15px;
          @include backgroundSec('bg-team-active.png');
          z-index: 1;
        }
        &.team-item-1 { @include backgroundSec('bg-team-1.png'); }
        &.team-item-2 { @include backgroundSec('bg-team-2.png'); }
        &.team-item-3 { @include backgroundSec('bg-team-3.png'); }
        &.team-item-4 { @include backgroundSec('bg-team-4.png'); }
      }
      .winner {
        width: 176px;
        height: 37px;
        @include backgroundSec('icon-win.png');
        position: absolute;
        top: 15px;
        left: -7px;
        z-index: 2;
      }
      .same-server {
        width: 92px;
        height: 30px;
        position: absolute;
        top: 47px;
        left: 1px;
        @include backgroundSec('bg-server.png');
      }
      .team-info {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
      }
      .same-alliance {
        width: 85px;
        height: 18px;
        position: absolute;
        left: 50%;
        top: -27px;
        transform: translateX(-50%);
        @include backgroundSec('bg-alliance.png');
      }
      .numericals {
        font-size: 16px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #D3CBB4;
        text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
        text-align: center;
        white-space: nowrap;
      }
      .l-line {
        width: 110px;
        height: 12px;
        @include backgroundSec('line-l.png');
      }
      .team-name {
        white-space: nowrap;
        font-size: 18px;
        line-height: 1;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #E3D09A;
        text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
      }
      .s-line {
        width: 52px;
        height: 10px;
        @include backgroundSec('line-s.png');
      }
    }
  }
}