.main-page {
  min-height: 100%;
  @include backgroundSec('alliance/bg.jpg', center, center, no-repeat, 100%, 100%);
}
.header {
  height: 68px;
  background-color: #101315;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .logo {
    width: 149px;
    height: 52px;
    margin-left: 36px;
    @include backgroundSec('logo.png');
  }
  .event-center {
    width: 212px;
    height: 68px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 25px;
    line-height: 1.1;
    padding: 0 10px 0 20px;
    cursor: pointer;
    @include backgroundSec('alliance/bg-top-entre.png', center, center, no-repeat, 100%, 100%);
  }
}
.top {
  @include backgroundSec('alliance/bg-top.png', top, center, no-repeat, 100%, auto);
  height: 416px;
  position: relative;
  padding-top: 38px;
  padding-left: 34px;
  @mixin textStroke($w, $h, $l) {
    width: $w + px;
    height: $h + px;
    @include backgroundSec('alliance/title2/' + $l + '.png');
  }
  .act-title {
    &.zh-cn { @include textStroke(368, 67, 'zh-cn'); }
    &.en { @include textStroke(602, 59, 'en'); }
    &.zh-tw { @include textStroke(368, 66, 'zh-tw'); }
    &.fr { @include textStroke(452, 109, 'fr'); }
    &.de { @include textStroke(449, 109, 'de'); }
    &.es { @include textStroke(498, 109, 'es'); }
    &.it { @include textStroke(583, 99, 'it'); }
    &.pl { @include textStroke(597, 55, 'pl'); }
    &.ru { @include textStroke(625, 57, 'ru'); }
    &.nl { @include textStroke(497, 107, 'nl'); }
    &.id { @include textStroke(620, 62, 'id'); }
    &.ja { @include textStroke(577, 57, 'ja'); }
    &.ko { @include textStroke(581, 58, 'ko'); }
    &.pt { @include textStroke(461, 109, 'pt'); }
    &.sv { @include textStroke(573, 59, 'sv'); }
    &.th { @include textStroke(571, 49, 'th'); }
    &.tr { @include textStroke(554, 62, 'tr'); }
    &.ar { @include textStroke(554, 56, 'ar'); }
    &.vi { @include textStroke(477, 53, 'vi'); }
  }
  .act-time {
    margin-top: 11px;
    margin-left: 4px;
    width: 387px;
    height: 53px;
    font-size: 16px;
    font-weight: bold;
    color: #FEF3CF;
    line-height: 1;
    padding-left: 14px;
    white-space: nowrap;
    @include backgroundSec('alliance/bg-time.png', center, center, no-repeat, 100%, 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    .b {
      color: #D96E52;
      margin-top: 5px;
    }
  }
}
.alliance-section.act-list {
  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 25px;
    overflow-x: auto;
    overflow-y: hidden;
    position: relative;
    padding-bottom: 20px;
  }
  .act-item {
    width: 311px;
    height: 308px;
    flex-shrink: 0;
    padding-top: 7px;
    @include backgroundSec('alliance/bg-act.png');
    position: relative;
    transition: transform .1s linear;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding-bottom: 18px;
    & + .act-item {
      margin-left: 20px;
    }
    &:active{
      transform: scale(0.97);
    }
    &.active {
      .bg-active {
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        @include backgroundSec('alliance/bg-act-active.png');
      }
    }
    .bg-active {
      display: none;
    }
    .time {
      width: 100%;
      height: 32px;
      background: linear-gradient(90deg, rgba(32,40,46,0) 0%, rgba(19,22,25,0.96) 48%, rgba(32,33,35,0) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      font-size: 20px;
      font-weight: 400;
      color: #D4CEBC;
      .icon {
        width: 20px;
        height: 20px;
        margin-right: 6px;
        @include backgroundSec('alliance/icon-time.png');
      }
    }
    .title {
      height: 40px;
      flex-shrink: 0;
      padding: 0 15px;
      font-size: 18px;
      line-height: 1.1;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: #E2C37F;
      @include textStroke(#0E141E);
    }
    .line {
      width: 297px;
      height: 5px;
      margin: 0 auto;
      @include backgroundSec('alliance/line-act-title.png');
    }
    .body {
      flex: 1;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .empty {
        font-size: 18px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #B7B19F;
        @include textStroke(#0E141E);
        padding: 0 10px;
        text-align: center;
      }
      .alliance-list {
        padding: 0 17px;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: space-around;
      }
      .alliance-item {
        width: 100%;
        height: 34px;
        background-color: rgba(0, 0, 0, 0.3);
        font-size: 18px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #B7B19F;
        @include textStroke(#0E141E);
        line-height: 34px;
        padding: 0 9px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .status {
      font-size: 18px;
      font-family: PingFang SC;
      font-weight: 400;
      color: #D96E52;
      line-height: 1;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      bottom: 0;
      min-width: 120px;
      height: 36px;
      text-align: center;
      bottom: -16px;
      left: 50%;
      transform: translate(-50%);
      padding: 0 10px;
      &.status-0 { color: #D96E52; @include backgroundSec('alliance/bg-act-status-0.png') }
      &.status-1 { color: #75D952; @include backgroundSec('alliance/bg-act-status-1.png') }
      &.status-2 { color: #D96E52; @include backgroundSec('alliance/bg-act-status-0.png') }
      &.status-3 { color: #6F5D4F; @include backgroundSec('alliance/bg-act-status-3.png') }
    }
  }
}
.alliance-section.betting {
  position: relative;
  .bet-tips {
    padding: 0 38px;
    margin: 28px 0;
    font-size: 18px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #B7B19F;
    line-height: 1.4;
  }
  
  .topic-list {
    margin: 0 26px;
    overflow-y: hidden;
    overflow-x: auto;
    margin-bottom: 23px;
    .scroll {
      display: flex;
      justify-content: space-between;
    }
    .topic-item {
      cursor: pointer;
      white-space: nowrap;
      flex-shrink: 0;
      padding: 0 10px;
      height: 42px;
      min-width: 170px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #A39E8F;
      line-height: 1;
      position: relative;
      @include backgroundSec('alliance/bg-topic.png');
      &+.topic-item {
        margin-left: 6px;
      }
      &.active {
        color: #1B1818;
        @include backgroundSec('alliance/bg-topic-active.png');
      }
    }
  }

  .guess {
    width: 697px;
    min-height: 628px;
    @include backgroundSec('bg-2.png');
    margin: 40px auto 0;
    position: relative;

    .guess-top {
      padding: 25px 17px;
      position: relative;
    }
    .record-btn {
      position: absolute;
      right: 17px;
      top: 19px;
      padding: 0 10px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      font-size: 20px;
      min-width: 171px;
      color: #FFF9F9;
      @include backgroundSec('bg-btn-radius.png');
    }
    .topic-num {
      padding: 0 15px;
      width: fit-content;
      min-width: 112px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid #746744;
      border-radius: 5px;
      font-size: 22px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #E2C37F;
      line-height: 1;
      white-space: nowrap;
    }
    .topic-name {
      margin-top: 24px;
      font-size: 20px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #E2C37F;
      line-height: 1.2;
    }
    .betting-list {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      .betting-item {
        width: 155px;
        height: 178px;
        position: relative;
        @include backgroundSec('alliance/bg-topic-item.png');
        z-index: 0;
        transition: transform .1s linear;
        cursor: pointer;
        &:active{
          transform: scale(0.97);
        }
        &.active::before {
          content: "";
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          @include backgroundSec('alliance/bg-topic-item-active.png');
          pointer-events: none;
        }
      }
      .title {
        position: absolute;
        left: 9px;
        right: 9px;
        top: 7px;
        font-size: 18px;
        font-family: PingFang SC;
        font-weight: 400;
        color: #B7B19F;
        line-height: 1;
        text-shadow: 0px 1px 0px rgba(0,0,0,0.7);
      }
      .allance-flag {
        width: 68px;
        height: 79px;
        position: absolute;
        left: 44px;
        top: 22px;
        &.flag-1 { @include backgroundSec('alliance/team/team_1.png'); }
        &.flag-2 { @include backgroundSec('alliance/team/team_2.png'); }
        &.flag-3 { @include backgroundSec('alliance/team/team_3.png'); }
        &.flag-4 { @include backgroundSec('alliance/team/team_4.png'); }
      }
      .odds {
        width: 100%;
        height: 49px;
        position: absolute;
        left: 0;
        bottom: 7px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 26px;
        font-family: Arial;
        font-weight: 400;
        color: #F6E2B1;
        line-height: 1;
        div.high {
          position: relative;
          font-size: 30px;
          font-family: Arial;
          font-weight: 600;
          color: transparent;
          text-shadow: none;
          background: linear-gradient(0deg, #F8B514 0%, #FFE87B 100%);
          -webkit-background-clip: text;
          &::before {
            content: attr(text);
            position: absolute;
            z-index: -1;
            @include textStroke(#3B2F21);
            text-shadow: 0 3px 0px black;
          }
        }
      }
      .team {
        width: 100%;
        position: absolute;
        left: 0;
        bottom: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #9DA5AC;
        line-height: 1;
        text-align: center;
        @include textStroke(#000000);
      }
    }
    .guess-bottom {
      height: 299px;
      position: relative;
      padding: 23px;
      display: flex;
      align-items: center;
      justify-content: center;
      .tips {
        font-size: 20px;
        text-align: center;
        font-family: Adobe Heiti Std;
        font-weight: normal;
        color: #C9664C;
        line-height: 1.4;
        @include textStroke(#000000);
      }
    }

    // 比赛未开始
    &.guess-unopen {
      @include backgroundSec('alliance/bg-alliance.png');
      min-height: 334px;
      height: 334px;
      .guess-wrap {
        height: 100%;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .tips {
          margin: 0;
          font-size: 20px;
          text-align: center;
          font-family: Adobe Heiti Std;
          font-weight: normal;
          color: #C9664C;
          line-height: 1.4;
          @include textStroke(#000000);
        }
      }
    }
    .line-divider {
      margin: 0 6px;
      height: 2px;
      background: rgba(101,97,73,0.39);
      position: relative;
      &::before {
        content: "";
        width: 0;
        height: 0;
        position: absolute;
        left: 50%;
        bottom: -6px;
        margin-left: -7px;
        border-top: 6px solid rgba(101,97,73,0.39);
        border-right: 7px solid transparent;
        border-left: 7px solid transparent;
      }
    }
  }
}