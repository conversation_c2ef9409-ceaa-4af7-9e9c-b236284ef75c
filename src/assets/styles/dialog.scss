.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
.body {
  width: 643px;
  @include backgroundSec('bg-dialog.png');
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  padding: 8px 25px;
}
.close {
  width: 35px;
  height: 36px;
  position: absolute;
  top: 19px;
  right: 19px;
  @include backgroundSec('icon-close.png');
  cursor: pointer;
}
.title {
  height: 57px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 28px;
  font-family: Source Han Serif CN;
  font-weight: 600;
  color: #402B13;
}
.line {
  height: 9px;
  width: 100%;
  @include backgroundSec('bg-dialog-line.png');
}
.content.bet {
  padding: 15px 9px;
  .txt {
    font-size: 22px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #402B13;
    line-height: 1.3;
    ::v-deep(i:nth-of-type(2)) {
      color: #386814;
    }
  }
  .odds {
    font-size: 22px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #402B13;
    text-align: center;
    margin: 60px 0;
    ::v-deep(i:nth-of-type(1)) {
      color: #9A2520;
      font-size: 36px;
      font-weight: bolder;
      display: inline-block;
      margin-left: 3px;
    }
  }
  .btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .btn {
      width: 220px;
      height: 65px;
      text-align: center;
      line-height: 30px;
      font-size: 24px;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      color: #FEFFF5;
      text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
      &.cancel {
        @include backgroundSec('bg-btn-blue.png');
      }
      &.submit {
        @include backgroundSec('bg-btn-yellow.png');
      }
    }
  }
  .tips {
    margin-top: 10px;
    text-align: center;
    font-size: 20px;
    color: #9A2520;
  }
}
.content.coin {
  padding: 15px 9px;
  overflow: hidden;
  .txt {
    font-size: 22px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #402B13;
    line-height: 1.3;
  }
  .swiper-wrap {
    width: 657px;
    margin: 15px auto;
    position: relative;
    font-size: 0;
    padding-bottom: 20px;
    .swiper-pagination {
      position: absolute;
      z-index: 10;
      display: flex;
      align-items: center;
      bottom: -10px;
      left: 50%;
      transform: translate(-50%, 0);
  
      .pagination-dot {
        width: 16px;
        height: 16px;
        margin: 0 3px;
        @include backgroundSec('swiper_2.png');
      }
  
      .pagination-dot_active {
        @include backgroundSec('swiper_1.png');
      }
    }
  }

}
.content.log {
  padding: 0 10px;
  width: calc(100% + 20px);
  max-height: 628px;
  overflow-y: auto;
  margin: 20px -10px;
  .log-item {
    display: flex;
    align-items: stretch;
    justify-content: center;
    min-height: 196px;
    background: rgba(123,110,90,0.4);
    box-shadow: inset 0px 0px 4px 0px rgba(5,5,5,0.28);
    &+.log-item {
      margin-top: 18px;
    }
  }
  .flex-c {
    display: flex;
    flex-direction: column;
  }
  .l { width: 463px; flex-shrink: 0; }
  .r { flex-grow: 1; }
  .t { height: 47px; flex-shrink: 0; }
  .b { flex-grow: 1; }

  .guess-title {
    padding: 0 10px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    .guess-t {
      font-size: 22px;
      color: #402B13;
    }
    .guess-time {
      padding: 0 15px;
      height: 28px;
      background: linear-gradient(90deg, rgba(32,40,46,0) 0%, rgba(19,22,25,0.4) 30%, rgba(19,22,25,0.4) 70%, rgba(32,33,35,0) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
      font-size: 18px;
      font-weight: 400;
      color: #D4CEBC;
      .icon {
        width: 18px;
        height: 18px;
        margin-right: 6px;
        @include backgroundSec('alliance/icon-time.png');
      }
    }
    &::before {
      content: " ";
      position: absolute;
      bottom: 0;
      left: 0;
      height: 2px;
      width: 100%;
      background: linear-gradient(90deg, rgba(5,5,5,0.28) 0%, rgba(5,5,5,0) 100%);
    }
  }
  .guess-content {
    padding: 10px;
    font-size: 20px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #584631;
    line-height: 1.4;
    ::v-deep(i:nth-of-type(2)) {
      font-size: 22px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #77D92E;
      text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
    }
    ::v-deep(i:nth-of-type(3)) {
      font-size: 20px;
      font-family: Flareserif821 BT;
      font-weight: normal;
      color: #FFFFFF;
      
      background: linear-gradient(0deg, #F8B514 0%, #FFE87B 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
  .guess-result-t {
    background: rgba(0,0,0,0.18);
    font-size: 22px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #402B13;
    line-height: 1;
    text-align: center;
  }
  .guess-result {
    background: rgba(123,110,90,0.5);
    .label {
      height: 38px;
      padding: 0 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      font-size: 22px;
      line-height: 1;
      border: 2px solid;
      border-radius: 19px;
      &.success {
        color: rgba(85, 164, 78, 1);
        border-color: rgba(85, 164, 78, 1);
      }
      &.failed {
        color: rgba(165, 42, 26, 1);
        border-color: rgba(165, 42, 26, 1);
      }
      &.unlock {
        color: rgba(87, 80, 73, 1);
        border-color: rgba(87, 80, 73, 1);
      }
    }
  }
}
.content.first-bet {
  padding: 15px 0;
  .txt {
    font-size: 22px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #402B13;
    line-height: 1.4;
    text-align: center;
  }
  .diamond-icon {
    width: 305px;
    height: 197px;
    margin: 10px auto;
    @include backgroundSec('alliance/icon-diamond.png');
  }
  .btn {
    width: 220px;
    height: 65px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #FEFFF5;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
    @include backgroundSec('bg-btn-blue.png');
    margin: 0 auto;
  }
}