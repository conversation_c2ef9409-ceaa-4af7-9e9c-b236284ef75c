// 背景图片
@mixin backgroundSec($name, $posirionX: center, $posirionY: center, $repeat: no-repeat, $sizeX: 100%, $sizeY: 100%){
  background: url('@/assets/img/' + $name) $posirionX $posirionY $repeat;
  -webkit-background-size: $sizeX $sizeY;
  background-size: $sizeX $sizeY;
}
// 文字 缩放
@mixin textScale($size: 20, $sizeDefautl: 24, $origin: center center, $transform: ''){
  display: block;
  transform: scale(calc($size / $sizeDefautl)) #{$transform};
  transform-origin: $origin;
}
// 文字 1px 黑色描边
@mixin textStroke($color: #000){
  text-shadow: -.008803rem /* 1/113.6 */ .008803rem /* 1/113.6 */ 0px $color, .008803rem /* 1/113.6 */ .008803rem /* 1/113.6 */ 0px $color, .008803rem /* 1/113.6 */ -.008803rem /* 1/113.6 */ 0px $color, -.008803rem /* 1/113.6 */ -.008803rem /* 1/113.6 */ 0px $color;
}
// 文字 1px 黑色阴影
@mixin textShadow($color: #000){
  text-shadow: -.008803rem /* 1/113.6 */ .008803rem /* 1/113.6 */ 0px $color, .008803rem /* 1/113.6 */ .008803rem /* 1/113.6 */ 0px $color, 0 0 0px $color, 0 0 0px $color;
}
// 单行文字超出显示省略号
@mixin textOverflow(){
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 两行文字超出显示省略号
@mixin twoLinetextOverflow(){
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
// 容器内容超出显示滚动条
@mixin scrollbox($height: 100%){
  height: $height;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  &::-webkit-scrollbar {
    display: none;
    opacity: 0;
  }
}
// 容器内容超出显示滚动条
@mixin scrollboxX($width: 100%){
  width: $width;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
  &::-webkit-scrollbar {
    display: none;
    opacity: 0;
  }
}
