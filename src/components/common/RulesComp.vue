<script setup lang="ts" name="RulesComp">
const props = defineProps({
  langKey: {
    type: String,
    default: 'all_star_guess_rule_detail'
  },
  langSlot: {
    type: Array,
    default: () => []
  }
})
</script>

<template>
  <div class="rules">
    <div class="section-title"><div>{{ $t('all_star_guess_rule_title') }}</div></div>
    <div class="rule" v-html="$t(props.langKey, props.langSlot)"></div>
  </div>
</template>

<style lang="scss" scoped>
.rule {
  padding: 0 30px 30px;
  font-size: 24px;
  font-family: Adobe Heiti Std;
  font-weight: normal;
  color: #CFC09A;
  line-height: 1.6;
  text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
  ::v-deep(a) {
    text-decoration: underline;
    color: #83734d;
  }
}
</style>