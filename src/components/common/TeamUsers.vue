<script setup lang="ts">
import { useStore } from '@/stores';
import { storeToRefs } from 'pinia';
import TeamUsersHCell from './TeamUsersHCell.vue';
import type { IColumn } from './types'
import TeamUsersCell from './TeamUsersCell.vue';
import type { TeamUserInfoType } from '@/stores/types';

const props = withDefaults(defineProps<{
  data: Array<TeamUserInfoType>,
  teamName?: string,
  crtTeamIndex: number,
  columns: Array<IColumn>
  showAllianceIcon?: boolean
  showTitle: boolean
}>(), {
  showAllianceIcon: false,
  showTitle: true
})
const store = useStore()
const { state } = storeToRefs(store)

</script>

<template>
  <div class="team-user">
    <i18n-t v-if="showTitle" keypath="all_star_guess_team_member_title" tag="div" class="t-u-title">
      <span></span>
      <span>[{{ teamName }}]</span>
    </i18n-t>
    <div class="table-wrap">
      <div class="t-head">
        <div class="t-row">
          <TeamUsersHCell v-for="(col, i) in props.columns" :key="i" :column="col" :index="i"></TeamUsersHCell>
        </div>
      </div>
      <div class="t-body">
        <div
          v-for="(user, i) in data"
          :key="i"
          :class="['t-row', i % 2 ? 'even' : 'odd']"
        >
          <div class="same-alliance" v-if="showAllianceIcon && state.userInfo.alliance_id !== 0 && user.alliance === state.userInfo.alliance_id"></div>
          <TeamUsersCell v-for="(col, i) in props.columns" :key="i" :column="col" :index="i" :row="user"></TeamUsersCell>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.team-user {
  position: relative;
  z-index: 2;
  .t-u-title {
    margin: 20px auto;
    width: 100%;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    font-size: 24px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #CFC09A;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.8);
    @include backgroundSec('bg-table-title.png', center, center, no-repeat, auto, 100%);
    ::v-deep(span) {
      font-size: 22px;
      font-weight: bold;
      color: #FFEA95;
      -webkit-text-stroke: 1px #3B2F21;
      text-stroke: 1px #3B2F21;
      text-shadow: none;
    }
  }
}
</style>