<script setup lang="ts">
import { ref } from 'vue';
// require styles
import 'swiper/css'

import { Swiper, SwiperSlide } from 'swiper/vue';

const isShow = ref(false)
const show = () => isShow.value = true

const swiperIndex = ref(0)
const onSlideChange = (swiper: any) => {
  swiperIndex.value = swiper.activeIndex
}

const props = defineProps({
  imgs: {
    type: Array,
    default: () => []
  },
  lang: {
    type: String,
    default: ''
  }
})

defineExpose({ show })
</script>

<template>
  <van-overlay :show="isShow">
    <div class="wrapper">
      <div class="body">
        <div class="close" @click="isShow = false"></div>
        <div class="title">{{ $t('all_star_guess_coin_detail_title') }}</div>
        <div class="line"></div>
        <div class="content coin">
          <div class="txt" v-html="$t(props.lang)"></div>
          
          <div class="swiper-wrap">
            <Swiper
              class="my-swiper-wrapper"
              @slideChange="onSlideChange"
            >
              <template v-for="item in props.imgs" :key="item">
                <SwiperSlide><img :src="(item as any)" alt=""></SwiperSlide>
              </template>
  
            </Swiper>
            <div class="swiper-pagination" v-if="props.imgs.length > 1">
              <div :class="['pagination-dot', {'pagination-dot_active': swiperIndex === index}]" :key="index" v-for="(_, index) in props.imgs"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-overlay>
</template>

<style lang="scss" scoped>
@import url('@/assets/styles/dialog.scss');
.body {
  width: 720px;
}
.my-swiper-wrapper {
  img {
    width: 657px;
  }
}
</style>