<script setup lang="ts">
import { computed } from 'vue';
import type { IColumn } from './types'
import { formatNum } from '@/utils/utils';
import type { TeamUserInfoType } from '@/stores/types';

const props = defineProps<{
  column: IColumn
  index: number
  row: TeamUserInfoType
}>()

const computedStyle = computed(() => {
  const style: Record<string, string | number> = {}
  if (props.column.width) {
    style.width = (parseInt(props.column.width) / 75).toFixed(5) + 'rem'
  } else {
    style.flex = '1'
  }

  return style
})
</script>

<template>
  <div :class="['t-cell', `t-cell-${index + 1}`, `t-cell-${props.column.prop}`]" :style="computedStyle">
    <slot>
      <template v-if="props.column.isNumber">
        {{ formatNum(row[props.column.prop]) }}
      </template>
      <template v-else-if="props.column.prop === 'title'">
        <div :class="['icon-r', `icon-${row[props.column.prop]}`]"></div>
      </template>
      <template v-else>{{ row[props.column.prop] }}</template>
    </slot>
  </div>
</template>

<style lang="scss" scoped>
.icon-r {
  width: 37px;
  height: 50px;
  margin: auto;
  background-size: 100% 100%;
  &.icon-0 { @include backgroundSec('alliance/icon-r0.png') }
  &.icon-1 { @include backgroundSec('alliance/icon-r1.png') }
  &.icon-2 { @include backgroundSec('alliance/icon-r2.png') }
  &.icon-3 { @include backgroundSec('alliance/icon-r3.png') }
  &.icon-4 { @include backgroundSec('alliance/icon-r4.png') }
  &.icon-5 { @include backgroundSec('alliance/icon-r5.png') }
  &.icon-6 { @include backgroundSec('alliance/icon-r5.png') }
}
</style>