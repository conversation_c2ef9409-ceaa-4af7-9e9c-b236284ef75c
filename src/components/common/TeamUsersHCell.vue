<script setup lang="ts">
import { useClickAway } from '@vant/use';
import { computed } from 'vue';
import { ref } from 'vue';
import type { IColumn } from './types'

const props = defineProps<{
  column: IColumn
}>()

const isShowIntegral = ref(false)
const integralWrap = ref()
useClickAway(integralWrap, () => {
  isShowIntegral.value = false
})

const computedStyle = computed(() => {
  const style: Record<string, string | number> = {}
  if (props.column.width) {
    style.width = (parseInt(props.column.width) / 75).toFixed(5) + 'rem'
  } else {
    style.flex = '1'
  }

  return style
})
</script>

<template>
  <div :class="['t-cell', 't-cell-1', `t-cell-${props.column.prop}`]" :style="computedStyle">
    <slot>
      <template v-if="props.column.lang === 'all_star_guess_team_member_points' || props.column.lang === 'alliance_guess_member_info_2'">
        <div class="score">
          {{ $t('all_star_guess_team_member_points') }}
          <div class="integral-tips">
            <div class="integral-icon" @click.stop="isShowIntegral = !isShowIntegral"></div>
            <div class="integral-wrap" ref="integralWrap" v-if="isShowIntegral">{{ $t('all_star_guess_team_member_points_detail') }}</div>
          </div>
        </div>
      </template>
      <template v-else>{{ $t(props.column.lang) }}</template>
    </slot>
  </div>
</template>

<style lang="scss" scoped>

</style>