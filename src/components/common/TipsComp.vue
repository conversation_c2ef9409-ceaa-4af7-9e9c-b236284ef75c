<script setup lang="ts">
import { goStore } from '@/utils/utils';

const emit = defineEmits(['jump'])

const goStoreClick = () => {
  emit('jump')
  goStore()
}
</script>

<template>
  <div class="store-icon" @click="goStoreClick">
    <div class="toast">
      Discount <br>5%～10%
    </div>
    <div class="gift"></div>
    <div class="txt"><span>{{ $t('all_star_guess_jump_button') }}</span> <div class="arr"></div></div>
  </div>
</template>

<style lang="scss" scoped>
.store-icon {
  cursor: pointer;
  z-index: 2;
  position: fixed;
  min-width: 130px;
  height: 40px;
  right: 27px;
  bottom: 41px;
  .toast {
    width: 73px;
    height: 73px;
    position: absolute;
    right: -13px;
    top: -112px;
    z-index: 3;
    @include backgroundSec('tips/tips-toast.png');
    font-size: 12px;
    font-family: Arial;
    font-weight: 400;
    color: #00000F;
    text-shadow: 0px 1px 0px rgba(255, 229, 176, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .gift {
    width: 110px;
    height: 113px;
    position: absolute;
    bottom: 23px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    @include backgroundSec('tips/tips-gift.png');
  }
  .txt {
    z-index: 3;
    position: absolute;
    padding: 5px;
    left: 50%;
    transform: translateX(-50%);
    top: 0;
    min-width: 100%;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    font-size: 16px;
    font-family: Adobe Heiti Std;
    font-weight: normal;
    color: #FFE1A1;
    text-shadow: 0px 1px 0px rgba(0,0,0,0.5);
    @include backgroundSec('tips/tips-bg.png');
    span {
      text-align: center;
    }
    .arr {
      @include backgroundSec('tips/tips-arr.png');
      width: 7px;
      height: 9px;
      margin-left: 8px;
    }
  }
}
</style>