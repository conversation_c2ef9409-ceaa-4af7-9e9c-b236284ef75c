import '@assets/styles/style.scss'
import '@/utils/flexible'

import { getUrlParams, loadLanguage } from '@/utils/utils'
getUrlParams()
loadLanguage()
import { createApp } from 'vue'
import i18n from '@/assets/lang'
import '@vant/touch-emulator';
import '@/utils/vantUI'
import VueGtag from 'vue-gtag-next'

import App from './App.vue'
import store from './stores'
import router from './router'

const app = createApp(App)

app.use(i18n)
app.use(store)
app.use(router)
app.use(VueGtag, {
  property: { id: 'G-2QKYH8LMRX' }
})

app.mount('#app')
