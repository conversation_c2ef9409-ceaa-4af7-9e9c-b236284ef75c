export interface ActiveType {
  id: number
  activity_id: string
  game_time: number
  result_time: number
  group: string
  sync_status: number
  bet_end_time: number
  status: number
  season: string
  isSend: number
  team_info: TeamInfoType[]
  topic_info: string[]
  topic_nums: number
  total_bet_nums: number
  has_bet_nums: number
  can_bet_nums: number
  round: number
}

export const defaultActive: ActiveType = {
  id: 0,
  activity_id: '',
  game_time: 0,
  result_time: 0,
  group: '',
  sync_status: 0,
  bet_end_time: 0,
  status: 0,
  season: '',
  isSend: 0,
  team_info: [],
  topic_info: [],
  topic_nums: 0,
  total_bet_nums: 0,
  has_bet_nums: 0,
  can_bet_nums: 0,
  round: 0
}

export interface UserInfoType {
  uid: number
  fpid: number
  name: string
  icon: number
  portrait: number
  kingdom: number
  vip_level: number
  total_pay: number
  city_level: number
  last_login: number
  zone_id: number
  allstar_coin: number
  chalons_coin: number
  alliance_id: number
}

export const defaultUserInfo: UserInfoType = {
  uid: 1,
  fpid: 1,
  name: "",
  icon: 1,
  portrait: 1,
  kingdom: 0,
  vip_level: 1,
  total_pay: 0,
  city_level: 1,
  last_login: 1691020800,
  zone_id: 123,
  allstar_coin: 0,
  chalons_coin: 0,
  alliance_id: 0
}

// 用户投注信息
export interface UserBettingType {
  id: number
  uid: number
  activity_id: string
  group: string
  team_id: string
  bet_nums: number
  ts: number
  odds: null
  send_nums: null | number
  fail_send_nums: number
}

export interface TeamUserInfoType {
  id: number
  activity_id: string
  group: string
  team_id: string
  uid: number
  nick_name: string
  power: number
  king: number
  alliance: number
  score: number
  title: number
}

export interface TeamInfoType {
  id: number
  name: string
  acronym: string
  activity_id: string
  group: string
  team_id: string
  order: number
  is_winner: number
  odds: string
  king: string
  total_score: number
  total_power: number
  alliance_id: string
  same_alliance: boolean
  same_kingdom: boolean
  users: TeamUserInfoType[]
}
