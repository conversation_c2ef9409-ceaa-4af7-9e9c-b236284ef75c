import { defineStore } from "pinia";
import { computed, reactive } from "vue";
import { defaultActive, type ActiveType, type UserInfoType, type TeamInfoType } from "./types";

export const useAllianceStore = defineStore('allianceStore', () => {
  const state = reactive({
    activeInfo: [defaultActive, defaultActive, defaultActive] as ActiveType[],
    userInfo: {} as UserInfoType,

    crtActIndex: 0,

    // 用户投注信息和活动队伍信息 - 切换比赛时需要更新
    currentActInfo: {} as ActiveType,
    teamInfos: {} as Record<string, TeamInfoType[]>,
    teamOddsInfo: {} as Record<string, Record<string, string>>
  })

  const setActiveInfo = (dataArr: ActiveType[]) => {
    console.log('setActiveInfo dataArr', dataArr)
    state.activeInfo = dataArr
  }

  const setUserInfo = (info: UserInfoType) => {
    state.userInfo = info
  }

  const updateUserBettind = (count: number) => {
    state.userInfo.chalons_coin = count
    state.activeInfo[state.crtActIndex].can_bet_nums -= 1
    state.activeInfo[state.crtActIndex].has_bet_nums += 1
  }

  const setCrtActIndex = (index: number) => {
    state.crtActIndex = index
  }

  const updateTeamOdds = (params: any, data: any) => {
    const key = params.activity_id + '_' + params.group + '_' + params.topic
    state.teamOddsInfo[key] = data
    const crtAct = state.activeInfo[state.crtActIndex].team_info
    for (let i = 0; i < crtAct.length; i++) {
      const team = crtAct[i]
      team.odds = data[team.team_id]
    }
  }

  const setTeamInfo = (key: string, data: TeamInfoType[]) => {
    state.teamInfos[key] = data
  }

  const computedCrtAct = computed(() => {
    return state.activeInfo[state.crtActIndex]
  })

  const computedCrtTeamInfos = computed(() => {
    const crtAct = state.activeInfo[state.crtActIndex]
    const k = `${crtAct.activity_id}_${crtAct.group}`
    return state.teamInfos[k] || []
  })

  return {
    state,
    setActiveInfo,
    setUserInfo,
    updateUserBettind,
    updateTeamOdds,
    setCrtActIndex,
    computedCrtAct,
    computedCrtTeamInfos,
    setTeamInfo
  }
})
