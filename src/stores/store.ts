import { defineStore } from "pinia";
import { computed, reactive } from "vue";
import { defaultActive, type ActiveType, type UserInfoType, type UserBettingType, type TeamInfoType } from "./types";

interface actInfoType {
  status: number
  betting_status: boolean
  is_winner: boolean
  team_infos: TeamInfoType[]
  user_betting: UserBettingType
}

export const useStore = defineStore('store', () => {
  const state = reactive({
    activeInfo: [defaultActive, defaultActive, defaultActive] as ActiveType[],
    userInfo: {} as UserInfoType,

    // 用户投注信息和活动队伍信息 - 切换比赛时需要更新
    userBettingInfo: {} as UserBettingType,
    currentActInfo: {} as actInfoType,
    winTeam: ''
  })

  const setActiveInfo = (dataArr: ActiveType[]) => {
    state.activeInfo = dataArr
  }

  const setUserInfo = (info: UserInfoType) => {
    state.userInfo = info
  }

  const updateUserCoin = (count: number) => {
    state.userInfo.allstar_coin = count
  }

  const setActInfos = (data: actInfoType) => {
    state.userBettingInfo = data.user_betting
    state.currentActInfo = data
    if (data.team_infos) {
      data.team_infos.forEach((item, index) => {
        // 确定胜利队伍
        if (item.is_winner > 0) state.winTeam = `team_${index+1}`
        const users = item.users || []
        for (let i = 0; i < users.length; i++) {
          const user = users[i];
          if (user.king === state.userInfo.kingdom) {
            item.same_kingdom = true
          }
          if (state.userInfo.alliance_id !== 0 && user.alliance === state.userInfo.alliance_id) {
            item.same_alliance = true
          }
        }
      })
    }
  }
  const crtTeamKey = computed(() => {
    return (id: string) => {
      let i = -1
      state.currentActInfo.team_infos.forEach((item, index) => {
        if (item.team_id === id) i = index
      })
      return i
    }
  })

  const updateTeamOdds = (data: any) => {
    for (let i = 0; i < state.currentActInfo.team_infos.length; i++) {
      const team = state.currentActInfo.team_infos[i]
      team.odds = data[team.team_id]
    }
  }

  return {
    state,
    setActiveInfo,
    setUserInfo,
    updateUserCoin,
    setActInfos,
    updateTeamOdds,
    crtTeamKey
  }
})
