import type { AxiosInstance } from 'axios'
import axios from 'axios'
import i18n from '@/assets/lang'
import { showToast } from 'vant'

const service: AxiosInstance = axios.create({
  timeout: 25000
})

service.interceptors.request.use(
  config => {
    return config
  },
  error => {
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  response => {
    if (response.config.url === '/api/activity/logger') return
    if ((response.config.url?.indexOf('/lang/') as number) >= 0) {
      return Promise.resolve(response.data)
    }
    const res = response.data
    if (res.errCode === 7000 || res.errCode === 7010) {
      return Promise.reject(res)
    } else if (res.errCode === 7021) {
      showToast(i18n.global.t('alliance_guess_error_1'))
      return Promise.reject(res)
    } else if (res.errCode === 7022) {
      showToast(i18n.global.t('alliance_guess_error_2'))
      return Promise.reject(res)
    } else if (res.errCode !== 0) {
      showToast(i18n.global.t('toast_retry_abnormal'))
      return Promise.reject(res)
    } 
    window.timeNow = parseInt(res.time || 0)
    return res.data
  },
  error => {
    console.log('err' + error) // for debug
    return Promise.reject(error)
  }
)

export default service
