import type { AxiosError } from 'axios'
import service from './interceptor'

const PLATFORM_MAP: any = {
  win64: 'windows'
}

const PKG_MAP: any = {
  GooglePlaySG: 'google_play_sg',
  GooglePlaySG_MJ: 'st_googleplay_frontline'
}

const request = {
  get<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('GET', url, params)
  },
  post<T = any>(url: string, params: any = {}): Promise<T> {
    return request.request('POST', url, params)
  },
  request<T = any>(method = 'GET', url: string, params: any = {}): Promise<T> {
    const urlParams = JSON.parse(window.sessionStorage.getItem('query') || '{}')
    params = {
      ...params,
      gameid: urlParams.gameid,
      openid: decodeURIComponent(urlParams.openid || ''),
      platform: PLATFORM_MAP[urlParams.platform] || urlParams.platform,
      pkg: PKG_MAP[urlParams.pkg] || urlParams.pkg,
      lang: urlParams.lang
    }
    
    return new Promise((resolve, reject) => {
      let data = {}
      // get
      if (method === 'GET') {
        data = { params }
      } else if (method === 'POST') {
        // post
        data = { data: params }
      }
      service({ method, url, ...data })
        .then((res) => {
          resolve(res as unknown as Promise<T>)
        })
        .catch((e: Error | AxiosError) => {
          reject(e)
        })
    })
  }
}

export default request
