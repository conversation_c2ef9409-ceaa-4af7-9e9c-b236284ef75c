<script setup lang="ts">
import i18n from '@/assets/lang';

// 多语言
const urlParams = JSON.parse(window.sessionStorage.getItem('query') || '{}')
if (urlParams.lang) {
  i18n.global.locale = urlParams.lang.toLowerCase()
} else {
  const broswerL = navigator.language
  let broswerLF = ''
  if (broswerL.toLowerCase() === 'zh-tw') broswerLF = 'zh_tw'
  else if (broswerL.startsWith('zh')) broswerLF = 'zh_cn'
  else broswerLF = broswerL.split('-')[0]
  i18n.global.locale = broswerLF.replace('_', '-') || 'en'
}
</script>

<template>
  <router-view></router-view>
</template>

<style scoped lang="scss">
</style>
