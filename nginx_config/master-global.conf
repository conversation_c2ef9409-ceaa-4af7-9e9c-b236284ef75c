server {
  listen 80;
  server_name st-all-star.kingsgroupgames.com;
  resolver ******* valid=60s;
  resolver_timeout 3s;
  set $proxy_url "st-events.kingsgroupgames.com";

  # compression-webpack-plugin 配置
  gzip on;
  gzip_min_length 1k;
  gzip_comp_level 9;
  gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png;
  gzip_vary on;
  # 配置禁用 gzip 条件，支持正则，此处表示 ie6 及以下不启用 gzip（因为ie低版本不支持）
  gzip_disable "MSIE [1-6]\.";

  add_header Access-Control-Allow-Origin *;
  add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
  add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';

  location / {
    root  /data/nginx/dist;
    index  index.php index.html index.htm;
    try_files $uri $uri/ /index.html;
  }

  location ^~ /api/{
    proxy_pass https://$proxy_url;
    proxy_set_header Host $proxy_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location = /50x.html {
      root   html;
  }
}
