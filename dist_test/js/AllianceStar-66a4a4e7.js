import{k as O,s as fe,l as $e,a as te,h as be,f as F,m as ye,e as ke,d as se,i as ae,n as Ie,o as we,p as X,q as Ce}from"./index-dd96cb3d.js";import{_ as P,l as T,b as Se,a as Te,R as oe,T as ie,I as Ae}from"./InvalidUser-204184d6.js";import{v as R,d as B,m as b,o as c,z as _,E as y,w as K,n as e,t as a,F as w,O as ne,p as H,q as x,f as le,J as j,C as U,B as o,D as L,x as Le,r as Be,G as ce,H as Ne,S as Me,A as C,K as qe,g as Ue,y as _e,b as V,I as Oe}from"./vendor-20bb6b23.js";const E=n=>{const r=O(),{state:l}=R(r),t=JSON.parse(window.sessionStorage.getItem("query")||"{}"),s={data_version:"3.0",fpid:l.value.userInfo.fpid,pkg_channel:t.pkg,game_uid:l.value.userInfo.uid,gameserver_id:l.value.userInfo.kingdom,app_id:t.gameid};s.properties={pkg_channel:t.pkg,game_uid:l.value.userInfo.uid,gameserver_id:l.value.userInfo.kingdom,action_name:"chalons",...n};const d={log:JSON.stringify(s)};fe(d)},Y=n=>(H("data-v-09ad1d42"),n=n(),x(),n),Pe={class:"wrapper"},He={class:"body"},xe={class:"title"},Ee=Y(()=>e("div",{class:"line"},null,-1)),ze={class:"content bet"},Ve=["innerHTML"],Re=["innerHTML"],De={class:"btns"},Je={class:"tips"},Fe={class:"wrapper"},Ke={class:"body"},je={class:"title"},Ge=Y(()=>e("div",{class:"line"},null,-1)),We={class:"content first-bet"},Qe={class:"txt"},Xe=Y(()=>e("div",{class:"diamond-icon"},null,-1)),Ye=B({__name:"BetComp",props:{topicId:{type:String,default:""},teamId:{type:String,default:""},teamName:{type:String,default:""},coin:{type:Number,default:0},odds:{type:[String,Number],default:"0"},activityId:{type:String,default:""},group:{type:String,default:""}},emits:["betted"],setup(n,{expose:r,emit:l}){const t=n,s=O(),{computedCrtAct:d,state:u}=R(s),{updateUserBettind:v}=s,m=b(!1);r({show:()=>{m.value=!0}});const p=b(!1),N=()=>{p.value||(p.value=!0,$e({activity_id:t.activityId,group:t.group,team_id:t.teamId,bet_nums:t.coin,topic:t.topicId}).then(f=>{te("bet",{activity_id:t.activityId,group:t.group,team_id:t.teamId,bet_nums:t.coin,topic:t.topicId}),v(f.chalons_coin),M.value=f.is_first,l("betted"),m.value=!1}).catch(f=>{setTimeout(()=>{f&&(f.errCode===7021||f.errCode===7022)||window.location.reload()},300)}).finally(()=>{p.value=!1}))},M=b(!1),I=()=>{E({context:{season_id:d.value.season,zone_pk:u.value.userInfo.zone_id,round:d.value.round,match_id:d.value.group},action:"tp_click"}),be()};return(f,$)=>{const J=ne;return c(),_(w,null,[y(J,{show:m.value},{default:K(()=>[e("div",Pe,[e("div",He,[e("div",{class:"close",onClick:$[0]||($[0]=z=>m.value=!1)}),e("div",xe,a(f.$t("all_star_guess_bet_title")),1),Ee,e("div",ze,[e("div",{class:"txt",innerHTML:f.$t("alliance_guess_bet_infomation_1",[t.teamName,t.coin])},null,8,Ve),e("div",{class:"odds",innerHTML:f.$t("alliance_guess_bet_infomation_2",[,t.odds])},null,8,Re),e("div",De,[e("button",{class:"btn cancel",onClick:$[1]||($[1]=z=>m.value=!1)},a(f.$t("alliance_guess_bet_pop_button_1")),1),e("button",{class:"btn submit",onClick:N},a(f.$t("alliance_guess_bet_pop_button_2")),1)]),e("div",Je,a(f.$t("alliance_guess_bet_infomation_3")),1)])])])]),_:1},8,["show"]),y(J,{show:M.value},{default:K(()=>[e("div",Fe,[e("div",Ke,[e("div",{class:"close",onClick:$[2]||($[2]=z=>M.value=!1)}),e("div",je,a(f.$t("alliance_guess_bet_success_pop_title")),1),Ge,e("div",We,[e("div",Qe,a(f.$t("alliance_guess_bet_success_pop_slogan")),1),Xe,e("div",{class:"btn",onClick:I},a(f.$t("alliance_guess_bet_success_pop_button")),1)])])])]),_:1},8,["show"])],64)}}});const Ze=P(Ye,[["__scopeId","data-v-09ad1d42"]]),de={topic_1:"alliance_guess_question_name_1",topic_2:"alliance_guess_question_name_3",topic_3:"alliance_guess_question_name_4",topic_4:"alliance_guess_question_name_5"},Z=n=>(H("data-v-334b733f"),n=n(),x(),n),es=Z(()=>e("div",{class:"icon"},null,-1)),ss={class:"wrapper"},ts={class:"body"},as={class:"title"},os=Z(()=>e("div",{class:"line"},null,-1)),is={class:"content log"},ns={class:"flex-c l"},ls={class:"t guess-title"},cs={class:"guess-t"},_s={class:"guess-time"},ds=Z(()=>e("div",{class:"icon"},null,-1)),us=["innerHTML"],rs={class:"flex-c r"},vs={class:"t d-f d-f-center guess-result-t"},ms={class:"b d-f d-f-c d-f-center guess-result"},ps={key:0,class:"label unlock"},gs={key:1,class:"label success"},hs={key:2,class:"label failed"},fs={key:0,class:"empty-list"},$s=B({__name:"BetLogs",props:{activityId:String,group:String},setup(n){const r=n,l=le({isShow:!1,logList:[]}),t=()=>{const d={activity_id:r.activityId,group:r.group};T.show(),ye(d).then(u=>{l.logList=u,l.isShow=!0}).finally(()=>T.hide())},s=()=>{l.isShow=!1,setTimeout(()=>{l.logList=[]},300)};return(d,u)=>{const v=ne;return c(),_(w,null,[e("div",{class:"logs",onClick:t},[es,j(a(d.$t("alliance_guess_logs_button")),1)]),y(v,{show:l.isShow,"lock-scroll":!1},{default:K(()=>[e("div",ss,[e("div",ts,[e("div",{class:"close",onClick:s}),e("div",as,a(d.$t("alliance_guess_logs_title")),1),os,e("div",is,[(c(!0),_(w,null,U(l.logList,(m,k)=>(c(),_("div",{class:"log-item",key:m.id},[e("div",ns,[e("div",ls,[e("div",cs,a(d.$t("alliance_guess_logs_subtitle",[k+1])),1),e("div",_s,[ds,j(" UTC "+a(o(F)(m.ts)),1)])]),e("div",{class:"b guess-content",innerHTML:d.$t("alliance_guess_logs_detail",[,d.$t(o(de)[m.topic]),m.name,m.bet_nums,m.odds,m.suc_send_nums,m.fail_send_nums])},null,8,us)]),e("div",rs,[e("div",vs,a(d.$t("alliance_guess_logs_result")),1),e("div",ms,[m.result_status?m.is_win?(c(),_("div",gs,a(d.$t("alliance_guess_logs_result_1")),1)):(c(),_("div",hs,a(d.$t("alliance_guess_logs_result_2")),1)):(c(),_("div",ps,a(d.$t("alliance_guess_logs_result_3")),1))])])]))),128)),l.logList.length===0?(c(),_("div",fs,a(d.$t("nothingHere")),1)):L("",!0)])])])]),_:1},8,["show"])],64)}}});const bs=P($s,[["__scopeId","data-v-334b733f"]]),ys=n=>(H("data-v-e15f6026"),n=n(),x(),n),ks={key:0,class:"guess-wrap"},Is=["max"],ws={class:"slider"},Cs=ys(()=>e("div",{class:"limit min"},"0",-1)),Ss={class:"limit max"},Ts=["disabled"],As=B({__name:"BetSlider",props:{crtTeamIndex:{type:Number,default:0}},emits:["bet"],setup(n,{expose:r,emit:l}){const t=n,{state:s,computedCrtAct:d}=R(O()),u=b(0);Le(u,k=>{k>=s.value.userInfo.chalons_coin&&(u.value=s.value.userInfo.chalons_coin),k<=0&&(u.value=0)});const v=()=>{l("bet",u.value)};return r({reset:()=>u.value=0}),(k,p)=>{const N=Be("i18n-t"),M=Me;return o(d).status===1?(c(),_("div",ks,[y(N,{keypath:"alliance_guess_bet_infomation_1",tag:"div",class:"tips-wrap"},{default:K(()=>[e("span",null,a(o(d).team_info[t.crtTeamIndex].name),1),ce(e("input",{type:"number",onkeyup:"value=value.replace(/^(0+)|[^\\d]+/g,'')","onUpdate:modelValue":p[0]||(p[0]=I=>u.value=I),min:"0",max:o(s).userInfo.chalons_coin},null,8,Is),[[Ne,u.value]])]),_:1}),e("div",ws,[y(M,{modelValue:u.value,"onUpdate:modelValue":p[1]||(p[1]=I=>u.value=I),max:o(s).userInfo.chalons_coin},null,8,["modelValue","max"]),Cs,e("div",Ss,a(o(s).userInfo.chalons_coin),1)]),e("button",{class:"button submit-guess",disabled:u.value===0||o(d).can_bet_nums<=0,onClick:v},a(k.$t("alliance_guess_bet_button")),9,Ts)])):L("",!0)}}});const Ls=P(As,[["__scopeId","data-v-e15f6026"]]),Bs=n=>(H("data-v-b4a7f8fd"),n=n(),x(),n),Ns={key:0,class:"my-coin"},Ms={class:"l"},qs={class:"txt"},Us=Bs(()=>e("div",{class:"icon"},null,-1)),Os={class:"count"},Ps=B({__name:"MyCoin",setup(n){const r=b(null),l=()=>{r.value.show()},{state:t,computedCrtAct:s}=R(O());return(d,u)=>{const v=Se;return c(),_(w,null,[o(s).status>0?(c(),_("div",Ns,[e("div",Ms,[e("div",qs,a(d.$t("all_star_guess_coin")),1),Us,e("div",Os,a(o(t).userInfo.chalons_coin),1)]),e("div",{class:"r",onClick:l})])):L("",!0),y(v,{ref_key:"coinComp",ref:r,lang:"alliance_guess_detail",imgs:[o(ke)("swiper3")]},null,8,["imgs"])],64)}}});const Hs=P(Ps,[["__scopeId","data-v-b4a7f8fd"]]),ue=n=>(H("data-v-82ac983b"),n=n(),x(),n),xs={key:0,class:"alliance-section alliance-list"},Es={class:"section-title"},zs={class:"teams-list"},Vs=["onClick"],Rs={class:"flag-active"},Ds=ue(()=>e("div",{class:"team-logo"},null,-1)),Js={key:0,class:"same-server"},Fs={class:"team-info"},Ks={class:"team-name"},js=ue(()=>e("div",{class:"divider"},null,-1)),Gs={class:"numericals"},Ws=B({__name:"AllianceUsers",setup(n){const r=O(),l=b(0);return(t,s)=>{const d=Te;return o(r).computedCrtTeamInfos.length?(c(),_("div",xs,[e("div",Es,[e("div",null,a(t.$t("alliance_guess_subtitle_3")),1)]),e("div",zs,[(c(!0),_(w,null,U(o(r).computedCrtTeamInfos,(u,v)=>(c(),_("div",{key:v,class:C(["team-item",{active:v===l.value}]),onClick:m=>l.value=v},[e("div",{class:C(["flag","flag-".concat(u.order),{active:v===l.value}])},[ce(e("div",Rs,null,512),[[qe,v===l.value]]),Ds],2),u.same_kingdom?(c(),_("div",Js)):L("",!0),e("div",Fs,[e("div",Ks,a(u.name),1),js,e("div",Gs,[e("div",null,a(t.$t("alliance_guess_alliance_info_2",[o(se)(u.total_power||0)])),1),e("div",null,a(t.$t("alliance_guess_alliance_info_3",[o(se)(u.total_score)])),1)])])],10,Vs))),128))]),y(d,{"show-title":!1,"crt-team-index":l.value,data:o(r).computedCrtTeamInfos[l.value].users,columns:[{lang:"alliance_guess_member_info_1",prop:"nick_name"},{lang:"alliance_guess_member_info_3",prop:"power",width:"135px",isNumber:!0},{lang:"alliance_guess_member_info_2",prop:"score",width:"135px",isNumber:!0},{lang:"alliance_guess_member_info_4",prop:"title",width:"150px"}]},null,8,["crt-team-index","data"])])):L("",!0)}}});const Qs=P(Ws,[["__scopeId","data-v-82ac983b"]]),D=n=>(H("data-v-e96bbfe2"),n=n(),x(),n),Xs={class:"main-page"},Ys={class:"header"},Zs=D(()=>e("div",{class:"logo"},null,-1)),et={class:"top"},st={class:"act-time"},tt={class:"t"},at={class:"b"},ot={class:"alliance-section act-list"},it={class:"section-title"},nt=["onClick"],lt=D(()=>e("div",{class:"bg-active"},null,-1)),ct={class:"time"},_t=D(()=>e("div",{class:"icon"},null,-1)),dt={class:"title"},ut=D(()=>e("div",{class:"line"},null,-1)),rt={class:"body"},vt={key:0,class:"empty"},mt={key:1,class:"alliance-list"},pt={key:0,class:"alliance-section betting"},gt={class:"section-title"},ht={class:"bet-tips"},ft={class:"txt-line"},$t={class:"txt-line"},bt={class:"topic-list"},yt={class:"scroll"},kt=["onClick"],It={class:"guess"},wt={class:"guess-top"},Ct={class:"topic-num"},St={class:"topic-name"},Tt={class:"betting-list"},At=["onClick"],Lt={class:"title"},Bt={class:"team"},Nt={class:"odds"},Mt=["text"],qt=D(()=>e("div",{class:"line-divider"},null,-1)),Ut={class:"guess-bottom"},Ot={key:1,class:"tips"},Pt={key:1,class:"alliance-section betting"},Ht={class:"guess guess-unopen"},xt={class:"guess-wrap"},Et={class:"tips"},zt=B({__name:"HomePage",props:{rulesState:{type:Object,default:()=>({betTime:0,returnRatio:0,betLimit:0})}},setup(n){const r=n,l=O(),{state:t,computedCrtAct:s,computedCrtTeamInfos:d}=R(l),{setTeamInfo:u,setCrtActIndex:v,updateTeamOdds:m}=l,k=b(0),p=b(null),N=b(null),M=async i=>{T.show(),await Q(I.value,!1),T.hide(),k.value=i,N.value.show()},I=b(0),f=Ue(()=>{const i=s.value.activity_id+"_"+s.value.group+"_"+s.value.topic_info[I.value];return t.value.teamOddsInfo[i]||{}}),$=b(0),J=i=>{$.value=i};let z;const W=(i,A=!1)=>{!A&&t.value.crtActIndex===i||(v(i),te("change_active",{activity_id:s.value.activity_id,group:s.value.group}),k.value=0,$.value=0,re(i),Q(0),clearTimeout(z),E({context:{season_id:s.value.season,zone_pk:t.value.userInfo.zone_id,round:s.value.round,match_id:s.value.group},action:"match_click"}))},re=i=>{const A=t.value.activeInfo[i];T.show(),Ie({activity_id:A.activity_id,group:A.group}).then(g=>{u("".concat(A.activity_id,"_").concat(A.group),g)}).finally(()=>{T.hide();const g=[t.value.activeInfo[i].bet_end_time,t.value.activeInfo[i].result_time].filter(h=>h>window.timeNow).sort();if(g.length){const h=g[0]-window.timeNow;h<86400&&(z=setTimeout(()=>{W(i,!0)},h*1e3))}})},Q=async(i,A=!0)=>{E({context:{season_id:s.value.season,zone_pk:t.value.userInfo.zone_id,round:s.value.round,match_id:s.value.group},action:"question_click",question_id:s.value.topic_info[i]}),I.value=i;const g={activity_id:s.value.activity_id,group:s.value.group,topic:s.value.topic_info[i]};T.show();try{const h=await we(g);if(m(g,h),!A)return;let S="";for(const q in h)S=S?h[S]<h[q]?q:S:q;s.value.team_info.forEach((q,he)=>{q.team_id===S&&($.value=he)})}catch(h){console.log(h)}T.hide()},ee=b();_e(()=>{for(let i=0;i<t.value.activeInfo.length;i++){if(t.value.activeInfo[i].status===1){v(i);break}v(0)}W(t.value.crtActIndex,!0),t.value.crtActIndex>1&&(ee.value.scrollLeft=window.rootFontSize*320*t.value.crtActIndex/75)});const ve=()=>{p.value.reset()},me=()=>{E({context:{season_id:s.value.season},action:"bubble_click"})},pe=()=>{E({context:{season_id:s.value.season},action:"button_click"}),X()},ge=()=>{X("/record",{topic_data:JSON.stringify(d.value.map(i=>({id:Number(i.alliance_id),king:i.king,order:i.order,power:i.total_power,name:i.acronym})))},!1)};return(i,A)=>(c(),_(w,null,[e("div",Xs,[e("div",Ys,[Zs,e("div",{class:"event-center",onClick:pe},a(i.$t("alliance_guess_top_button")),1)]),e("div",et,[e("div",{class:C(["act-title",o(ae).global.locale])},null,2),e("div",st,[e("div",tt,a(i.$t("alliance_guess_start_time",[o(F)(o(t).activeInfo[o(t).crtActIndex].game_time)])),1),e("div",at,a(i.$t("alliance_guess_end_time",[o(F)(o(t).activeInfo[o(t).crtActIndex].bet_end_time)])),1)])]),e("div",ot,[e("div",it,[e("div",null,a(i.$t("alliance_guess_subtitle_1")),1)]),e("div",{class:"content",ref_key:"actEle",ref:ee},[(c(!0),_(w,null,U(o(t).activeInfo,(g,h)=>(c(),_("div",{key:g.activity_id,class:C(["act-item",{active:h===o(t).crtActIndex}]),onClick:S=>W(h)},[lt,e("div",ct,[_t,j("UTC "+a(o(F)(g.game_time,"MM/DD hh:mm")),1)]),e("div",dt,a(i.$t("alliance_guess_game_name")),1),ut,e("div",rt,[g.status===0?(c(),_("div",vt,a(i.$t("alliance_guess_game_name_none")),1)):(c(),_("div",mt,[(c(!0),_(w,null,U(g.team_info,(S,q)=>(c(),_("div",{class:"alliance-item",key:q},"[K"+a(S.king)+"] "+a(S.acronym),1))),128))]))]),e("div",{class:C(["status","status-".concat(g.status)])},a(i.$t("all_star_guess_state_".concat(g.status))),3)],10,nt))),128))],512)]),o(s).status>0?(c(),_("div",pt,[e("div",gt,[e("div",null,a(i.$t("alliance_guess_subtitle_2")),1)]),y(bs,{"activity-id":o(t).activeInfo[o(t).crtActIndex].activity_id,group:o(t).activeInfo[o(t).crtActIndex].group},null,8,["activity-id","group"]),y(Hs),e("div",ht,[e("div",ft,a(i.$t("alliance_guess_question_detail_0",[o(s).topic_nums,o(s).total_bet_nums])),1),e("div",$t,a(i.$t("alliance_guess_question_detail_1",[o(s).has_bet_nums,o(s).can_bet_nums])),1)]),e("div",bt,[e("div",yt,[(c(!0),_(w,null,U(o(s).topic_info,(g,h)=>(c(),_("div",{key:o(s).activity_id+o(s).group+g,class:C(["topic-item",{active:I.value===h}]),onClick:S=>Q(h)},a(i.$t("alliance_guess_question_title",[h+1])),11,kt))),128))])]),e("div",It,[e("div",wt,[e("div",{class:"btn record-btn",onClick:ge},a(i.$t("alliance_btn_record")),1),e("div",Ct,a(i.$t("alliance_guess_question_title",[I.value+1])),1),e("div",St,a(i.$t(o(de)[o(s).topic_info[I.value]])),1),e("div",Tt,[(c(!0),_(w,null,U(o(s).team_info,(g,h)=>(c(),_("div",{key:g.team_id,class:C(["betting-item",{active:$.value===h}]),onClick:S=>J(h)},[e("div",Lt,a(i.$t("all_star_guess_odds_subtitle")),1),e("div",{class:C(["allance-flag","flag-".concat(g.order)])},null,2),e("div",Bt,a(g.name),1),e("div",Nt,[e("div",{text:f.value[g.team_id],class:C({high:Number(f.value[g.team_id])>=4})},a(f.value[g.team_id]),11,Mt)])],10,At))),128))])]),qt,e("div",Ut,[o(s).status===1?(c(),V(Ls,{key:0,ref_key:"betSlider",ref:p,onBet:M,"crt-team-index":$.value},null,8,["crt-team-index"])):L("",!0),o(s).status>=2?(c(),_("div",Ot,a(i.$t("all_star_guess_result_3")),1)):L("",!0)])])])):L("",!0),o(s).status===0?(c(),_("div",Pt,[e("div",Ht,[e("div",xt,[e("div",Et,a(i.$t("all_star_guess_no_team")),1)])])])):L("",!0),y(Qs),y(oe,{"lang-key":"alliance_guess_rule_detail",class:"alliance-section","lang-slot":[r.rulesState.betTime,r.rulesState.betLimit,r.rulesState.returnRatio]},null,8,["lang-slot"]),o(s).status>0?(c(),V(Ze,{key:2,onBetted:ve,ref_key:"betComp",ref:N,"team-id":o(s).team_info[$.value].team_id,"team-name":o(s).team_info[$.value].name,coin:k.value,odds:o(s).team_info[$.value].odds,"activity-id":o(t).activeInfo[o(t).crtActIndex].activity_id,"topic-id":o(s).topic_info[I.value],group:o(t).activeInfo[o(t).crtActIndex].group},null,8,["team-id","team-name","coin","odds","activity-id","topic-id","group"])):L("",!0)]),y(ie,{onJump:me})],64))}});const Vt=P(zt,[["__scopeId","data-v-e96bbfe2"]]),G=n=>(H("data-v-6b5da024"),n=n(),x(),n),Rt={class:"main-page landing"},Dt={class:"header"},Jt=G(()=>e("div",{class:"logo"},null,-1)),Ft={class:"top"},Kt={class:"act-time"},jt={class:"t"},Gt={class:"b"},Wt={class:"alliance-section act-list"},Qt={class:"section-title"},Xt={class:"content"},Yt=["onClick"],Zt=G(()=>e("div",{class:"bg-active"},null,-1)),ea={class:"time"},sa=G(()=>e("div",{class:"icon"},null,-1)),ta={class:"title"},aa=G(()=>e("div",{class:"line"},null,-1)),oa={class:"body"},ia={class:"empty"},na={class:C(["status","status-0"])},la={class:"alliance-section betting"},ca={class:"guess guess-unopen"},_a={class:"guess-wrap"},da={class:"tips"},ua=B({__name:"LandingPage",setup(n){const r=[{start_day:"04/11",start_time:"04/11 20:00",end_time:"04/11 19:59"},{start_day:"04/11",start_time:"04/11 20:00",end_time:"04/11 19:59"},{start_day:"04/11",start_time:"04/11 20:00",end_time:"04/11 19:59"},{start_day:"04/11",start_time:"04/11 20:00",end_time:"04/11 19:59"},{start_day:"04/13",start_time:"04/13 20:00",end_time:"04/13 19:59"}],l=b(0),t=()=>{E({context:{season_id:"none"},action:"button_click"}),X()};return(s,d)=>(c(),_(w,null,[e("div",Rt,[e("div",Dt,[Jt,e("div",{class:"event-center",onClick:t},a(s.$t("alliance_guess_top_button")),1)]),e("div",Ft,[e("div",{class:C(["act-title",o(ae).global.locale])},null,2),e("div",Kt,[e("div",jt,a(s.$t("all_star_guess_game_time",[r[l.value].start_time])),1),e("div",Gt,a(s.$t("all_star_guess_end_time",[r[l.value].end_time])),1)])]),e("div",Wt,[e("div",Qt,[e("div",null,a(s.$t("alliance_guess_subtitle_1")),1)]),e("div",Xt,[(c(),_(w,null,U(r,(u,v)=>e("div",{key:v,class:C(["act-item",{active:v===l.value}]),onClick:m=>l.value=v},[Zt,e("div",ea,[sa,j(" UTC "+a(u.start_time),1)]),e("div",ta,a(s.$t("alliance_guess_game_name")),1),aa,e("div",oa,[e("div",ia,a(s.$t("alliance_guess_game_name_none")),1)]),e("div",na,a(s.$t("all_star_guess_state_0")),1)],10,Yt)),64))])]),e("div",la,[e("div",ca,[e("div",_a,[e("div",da,a(s.$t("all_star_guess_no_team")),1)])])]),y(oe,{"lang-key":"alliance_guess_rule_detail",class:"alliance-section","lang-slot":[1,5,30]})]),y(ie)],64))}});const ra=P(ua,[["__scopeId","data-v-6b5da024"]]),ga=B({__name:"AllianceStar",setup(n){const r=JSON.parse(window.sessionStorage.getItem("query")||"{}"),l=b(!1),t=b(!1),s=le({betTime:0,returnRatio:0,betLimit:0});_e(()=>{const{ctx:p}=Oe();document.getElementsByTagName("title")[0].innerText=p.$t("alliance_guess_main_title")});const d=O(),{setActiveInfo:u,setUserInfo:v}=d,m=()=>{T.show(),Ce().then(p=>{T.hide(),l.value=!0,t.value=!1,u(p.activity_info),v(p.user_info),s.betTime=p.bet_countdown,s.returnRatio=p.return_ratio,s.betLimit=p.total_bet_nums}).catch(p=>{T.hide(),p.errCode===7010?(l.value=!1,t.value=!1):p.errCode===7e3&&(t.value=!0,l.value=!1)})};r.openid?m():(t.value=!0,l.value=!1);const k=()=>{t.value=!1,l.value=!1};return(p,N)=>t.value?(c(),V(Ae,{key:0,onBack:k,"act-type":"allianceStar"})):(c(),_(w,{key:1},[l.value?(c(),V(Vt,{key:0,"rules-state":s},null,8,["rules-state"])):(c(),V(ra,{key:1}))],64))}});export{ga as default};
