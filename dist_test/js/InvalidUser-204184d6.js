import{d as p,z as l,n as s,t as r,o,b as h,w as $,O as A,E as B,L as H,f as F,k as V,J as v,p as T,q as x,m as b,M as z,g as M,N as R,A as k,P as j,Q as D,D as y,F as u,B as C,v as E,r as O,C as w,R as K,T as W}from"./vendor-20bb6b23.js";import{h as q,d as J,u as P}from"./index-dd96cb3d.js";const Q={class:"rules"},G={class:"section-title"},X=["innerHTML"],Y=p({__name:"RulesComp",props:{langKey:{type:String,default:"all_star_guess_rule_detail"},langSlot:{type:Array,default:()=>[]}},setup(e){const t=e;return(a,n)=>(o(),l("div",Q,[s("div",G,[s("div",null,r(a.$t("all_star_guess_rule_title")),1)]),s("div",{class:"rule",innerHTML:a.$t(t.lang<PERSON><PERSON>,t.langSlot)},null,8,X)]))}});const f=(e,t)=>{const a=e.__vccOpts||e;for(const[n,c]of t)a[n]=c;return a},Je=f(Y,[["__scopeId","data-v-761328e4"]]),Z={class:"wrapper"},ee=p({__name:"CustomLoading",props:["conf"],setup(e){const t=e;return(a,n)=>{const c=H,_=A;return o(),h(_,{show:t.conf.show},{default:$(()=>[s("div",Z,[B(c,{type:"spinner"})])]),_:1},8,["show"])}}});const se=f(ee,[["__scopeId","data-v-de0cd924"]]),I=F({show:!1});let S=null;const te=V(se,{conf:I}).mount(document.createElement("div")),Pe={show(){clearTimeout(S),S=null,!I.show&&(I.show=!0,document.body.appendChild(te.$el))},hide(){S||(S=setTimeout(()=>{I.show=!1},100))}},N=e=>(T("data-v-6a77d8a6"),e=e(),x(),e),oe=N(()=>s("div",{class:"toast"},[v(" Discount "),s("br"),v("5%～10% ")],-1)),ne=N(()=>s("div",{class:"gift"},null,-1)),ae={class:"txt"},le=N(()=>s("div",{class:"arr"},null,-1)),ce=p({__name:"TipsComp",emits:["jump"],setup(e,{emit:t}){const a=()=>{t("jump"),q()};return(n,c)=>(o(),l("div",{class:"store-icon",onClick:a},[oe,ne,s("div",ae,[s("span",null,r(n.$t("all_star_guess_jump_button")),1),v(),le])]))}});const Qe=f(ce,[["__scopeId","data-v-6a77d8a6"]]),_e={key:0,class:"score"},ie={class:"integral-tips"},re=p({__name:"TeamUsersHCell",props:{column:{}},setup(e){const t=e,a=b(!1),n=b();z(n,()=>{a.value=!1});const c=M(()=>{const _={};return t.column.width?_.width=(parseInt(t.column.width)/75).toFixed(5)+"rem":_.flex="1",_});return(_,g)=>(o(),l("div",{class:k(["t-cell","t-cell-1","t-cell-".concat(t.column.prop)]),style:j(c.value)},[R(_.$slots,"default",{},()=>[t.column.lang==="all_star_guess_team_member_points"||t.column.lang==="alliance_guess_member_info_2"?(o(),l("div",_e,[v(r(_.$t("all_star_guess_team_member_points"))+" ",1),s("div",ie,[s("div",{class:"integral-icon",onClick:g[0]||(g[0]=D(i=>a.value=!a.value,["stop"]))}),a.value?(o(),l("div",{key:0,class:"integral-wrap",ref_key:"integralWrap",ref:n},r(_.$t("all_star_guess_team_member_points_detail")),513)):y("",!0)])])):(o(),l(u,{key:1},[v(r(_.$t(t.column.lang)),1)],64))])],6))}}),de=p({__name:"TeamUsersCell",props:{column:{},index:{},row:{}},setup(e){const t=e,a=M(()=>{const n={};return t.column.width?n.width=(parseInt(t.column.width)/75).toFixed(5)+"rem":n.flex="1",n});return(n,c)=>(o(),l("div",{class:k(["t-cell","t-cell-".concat(n.index+1),"t-cell-".concat(t.column.prop)]),style:j(a.value)},[R(n.$slots,"default",{},()=>[t.column.isNumber?(o(),l(u,{key:0},[v(r(C(J)(n.row[t.column.prop])),1)],64)):t.column.prop==="title"?(o(),l("div",{key:1,class:k(["icon-r","icon-".concat(n.row[t.column.prop])])},null,2)):(o(),l(u,{key:2},[v(r(n.row[t.column.prop]),1)],64))],!0)],6))}});const ue=f(de,[["__scopeId","data-v-ffe12054"]]),pe=e=>(T("data-v-c6aeff1b"),e=e(),x(),e),me={class:"team-user"},ve=pe(()=>s("span",null,null,-1)),fe={class:"table-wrap"},ge={class:"t-head"},he={class:"t-row"},ye={class:"t-body"},$e={key:0,class:"same-alliance"},we=p({__name:"TeamUsers",props:{data:{},teamName:{},crtTeamIndex:{},columns:{},showAllianceIcon:{type:Boolean,default:!1},showTitle:{type:Boolean,default:!0}},setup(e){const t=e,a=P(),{state:n}=E(a);return(c,_)=>{const g=O("i18n-t");return o(),l("div",me,[c.showTitle?(o(),h(g,{key:0,keypath:"all_star_guess_team_member_title",tag:"div",class:"t-u-title"},{default:$(()=>[ve,s("span",null,"["+r(c.teamName)+"]",1)]),_:1})):y("",!0),s("div",fe,[s("div",ge,[s("div",he,[(o(!0),l(u,null,w(t.columns,(i,d)=>(o(),h(re,{key:d,column:i,index:d},null,8,["column","index"]))),128))])]),s("div",ye,[(o(!0),l(u,null,w(c.data,(i,d)=>(o(),l("div",{key:d,class:k(["t-row",d%2?"even":"odd"])},[c.showAllianceIcon&&C(n).userInfo.alliance_id!==0&&i.alliance===C(n).userInfo.alliance_id?(o(),l("div",$e)):y("",!0),(o(!0),l(u,null,w(t.columns,(L,m)=>(o(),h(ue,{key:m,column:L,index:m,row:i},null,8,["column","index","row"]))),128))],2))),128))])])])}}});const Ge=f(we,[["__scopeId","data-v-c6aeff1b"]]),ke=e=>(T("data-v-63833743"),e=e(),x(),e),Ce={class:"wrapper"},Se={class:"body"},Ie={class:"title"},be=ke(()=>s("div",{class:"line"},null,-1)),Te={class:"content coin"},xe=["innerHTML"],Le={class:"swiper-wrap"},Ne=["src"],Ue={key:0,class:"swiper-pagination"},Ae=p({__name:"CoinComp",props:{imgs:{type:Array,default:()=>[]},lang:{type:String,default:""}},setup(e,{expose:t}){const a=e,n=b(!1),c=()=>n.value=!0,_=b(0),g=i=>{_.value=i.activeIndex};return t({show:c}),(i,d)=>{const L=A;return o(),h(L,{show:n.value},{default:$(()=>[s("div",Ce,[s("div",Se,[s("div",{class:"close",onClick:d[0]||(d[0]=m=>n.value=!1)}),s("div",Ie,r(i.$t("all_star_guess_coin_detail_title")),1),be,s("div",Te,[s("div",{class:"txt",innerHTML:i.$t(a.lang)},null,8,xe),s("div",Le,[B(C(W),{class:"my-swiper-wrapper",onSlideChange:g},{default:$(()=>[(o(!0),l(u,null,w(a.imgs,m=>(o(),h(C(K),{key:m},{default:$(()=>[s("img",{src:m,alt:""},null,8,Ne)]),_:2},1024))),128))]),_:1}),a.imgs.length>1?(o(),l("div",Ue,[(o(!0),l(u,null,w(a.imgs,(m,U)=>(o(),l("div",{class:k(["pagination-dot",{"pagination-dot_active":_.value===U}]),key:U},null,2))),128))])):y("",!0)])])])])]),_:1},8,["show"])}}});const Xe=f(Ae,[["__scopeId","data-v-63833743"]]),Be="/png/invalid-back-d659d3d7.png",Me="/jpg/invalid_user-1b59c907.jpg",Re="/png/invalid_user-2beb886f.png",je=e=>(T("data-v-cf1189f5"),e=e(),x(),e),He={class:"invalid"},Fe={class:"back"},Ve=je(()=>s("div",{class:"line"},null,-1)),ze={class:"body"},De={class:"txt"},Ee={key:0,class:"tips-img",src:Me,alt:""},Oe={key:1,class:"tips-img",src:Re,alt:""},Ke=p({__name:"InvalidUser",props:{actType:{type:String,default:"allStar"}},emits:["back"],setup(e,{emit:t}){const a=e;return(n,c)=>(o(),l("div",He,[s("div",Fe,[s("img",{src:Be,alt:"",onClick:c[0]||(c[0]=_=>t("back"))})]),Ve,s("div",ze,[s("div",De,r(n.$t("all_star_guess_roadmap")),1),a.actType==="allStar"?(o(),l("img",Ee)):y("",!0),a.actType==="allianceStar"?(o(),l("img",Oe)):y("",!0)])]))}});const Ye=f(Ke,[["__scopeId","data-v-cf1189f5"]]);export{Ye as I,Je as R,Qe as T,f as _,Ge as a,Xe as b,Pe as l};
