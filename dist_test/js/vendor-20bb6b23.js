function Gi(e,t){const n=Object.create(null),r=e.split(",");for(let s=0;s<r.length;s++)n[r[s]]=!0;return t?s=>!!n[s.toLowerCase()]:s=>!!n[s]}const _e={},Ln=[],ct=()=>{},Cf=()=>!1,Of=/^on[^a-z]/,is=e=>Of.test(e),Ki=e=>e.startsWith("onUpdate:"),Ie=Object.assign,Yi=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},xf=Object.prototype.hasOwnProperty,de=(e,t)=>xf.call(e,t),Z=Array.isArray,An=e=>os(e)==="[object Map]",Gl=e=>os(e)==="[object Set]",se=e=>typeof e=="function",Pe=e=>typeof e=="string",qi=e=>typeof e=="symbol",ye=e=>e!==null&&typeof e=="object",Kl=e=>ye(e)&&se(e.then)&&se(e.catch),Yl=Object.prototype.toString,os=e=>Yl.call(e),Pf=e=>os(e).slice(8,-1),ql=e=>os(e)==="[object Object]",Xi=e=>Pe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Hr=Gi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),as=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Lf=/-(\w)/g,Et=as(e=>e.replace(Lf,(t,n)=>n?n.toUpperCase():"")),Af=/\B([A-Z])/g,Bn=as(e=>e.replace(Af,"-$1").toLowerCase()),ls=as(e=>e.charAt(0).toUpperCase()+e.slice(1)),ks=as(e=>e?"on".concat(ls(e)):""),ur=(e,t)=>!Object.is(e,t),Ur=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},qr=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},ai=e=>{const t=parseFloat(e);return isNaN(t)?e:t},If=e=>{const t=Pe(e)?Number(e):NaN;return isNaN(t)?e:t};let Bo;const li=()=>Bo||(Bo=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ji(e){if(Z(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],s=Pe(r)?Ff(r):Ji(r);if(s)for(const i in s)t[i]=s[i]}return t}else{if(Pe(e))return e;if(ye(e))return e}}const Nf=/;(?![^(]*\))/g,Rf=/:([^]+)/,Mf=/\/\*[^]*?\*\//g;function Ff(e){const t={};return e.replace(Mf,"").split(Nf).forEach(n=>{if(n){const r=n.split(Rf);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function Qi(e){let t="";if(Pe(e))t=e;else if(Z(e))for(let n=0;n<e.length;n++){const r=Qi(e[n]);r&&(t+=r+" ")}else if(ye(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const kf="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Df=Gi(kf);function Xl(e){return!!e||e===""}const cE=e=>Pe(e)?e:e==null?"":Z(e)||ye(e)&&(e.toString===Yl||!se(e.toString))?JSON.stringify(e,Jl,2):String(e),Jl=(e,t)=>t&&t.__v_isRef?Jl(e,t.value):An(t)?{["Map(".concat(t.size,")")]:[...t.entries()].reduce((n,[r,s])=>(n["".concat(r," =>")]=s,n),{})}:Gl(t)?{["Set(".concat(t.size,")")]:[...t.values()]}:ye(t)&&!Z(t)&&!ql(t)?String(t):t;let Je;class Ql{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Je,!t&&Je&&(this.index=(Je.scopes||(Je.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=Je;try{return Je=this,t()}finally{Je=n}}}on(){Je=this}off(){Je=this.parent}stop(t){if(this._active){let n,r;for(n=0,r=this.effects.length;n<r;n++)this.effects[n].stop();for(n=0,r=this.cleanups.length;n<r;n++)this.cleanups[n]();if(this.scopes)for(n=0,r=this.scopes.length;n<r;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const s=this.parent.scopes.pop();s&&s!==this&&(this.parent.scopes[this.index]=s,s.index=this.index)}this.parent=void 0,this._active=!1}}}function Zi(e){return new Ql(e)}function Bf(e,t=Je){t&&t.active&&t.effects.push(e)}function Zl(){return Je}function $f(e){Je&&Je.cleanups.push(e)}const eo=e=>{const t=new Set(e);return t.w=0,t.n=0,t},ec=e=>(e.w&Gt)>0,tc=e=>(e.n&Gt)>0,jf=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=Gt},Hf=e=>{const{deps:t}=e;if(t.length){let n=0;for(let r=0;r<t.length;r++){const s=t[r];ec(s)&&!tc(s)?s.delete(e):t[n++]=s,s.w&=~Gt,s.n&=~Gt}t.length=n}},Xr=new WeakMap;let Qn=0,Gt=1;const ci=30;let at;const dn=Symbol(""),ui=Symbol("");class to{constructor(t,n=null,r){this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,Bf(this,r)}run(){if(!this.active)return this.fn();let t=at,n=Wt;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=at,at=this,Wt=!0,Gt=1<<++Qn,Qn<=ci?jf(this):$o(this),this.fn()}finally{Qn<=ci&&Hf(this),Gt=1<<--Qn,at=this.parent,Wt=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){at===this?this.deferStop=!0:this.active&&($o(this),this.onStop&&this.onStop(),this.active=!1)}}function $o(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Wt=!0;const nc=[];function $n(){nc.push(Wt),Wt=!1}function jn(){const e=nc.pop();Wt=e===void 0?!0:e}function Ke(e,t,n){if(Wt&&at){let r=Xr.get(e);r||Xr.set(e,r=new Map);let s=r.get(n);s||r.set(n,s=eo()),rc(s)}}function rc(e,t){let n=!1;Qn<=ci?tc(e)||(e.n|=Gt,n=!ec(e)):n=!e.has(at),n&&(e.add(at),at.deps.push(e))}function At(e,t,n,r,s,i){const o=Xr.get(e);if(!o)return;let l=[];if(t==="clear")l=[...o.values()];else if(n==="length"&&Z(e)){const a=Number(r);o.forEach((c,u)=>{(u==="length"||u>=a)&&l.push(c)})}else switch(n!==void 0&&l.push(o.get(n)),t){case"add":Z(e)?Xi(n)&&l.push(o.get("length")):(l.push(o.get(dn)),An(e)&&l.push(o.get(ui)));break;case"delete":Z(e)||(l.push(o.get(dn)),An(e)&&l.push(o.get(ui)));break;case"set":An(e)&&l.push(o.get(dn));break}if(l.length===1)l[0]&&fi(l[0]);else{const a=[];for(const c of l)c&&a.push(...c);fi(eo(a))}}function fi(e,t){const n=Z(e)?e:[...e];for(const r of n)r.computed&&jo(r);for(const r of n)r.computed||jo(r)}function jo(e,t){(e!==at||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}function Uf(e,t){var n;return(n=Xr.get(e))==null?void 0:n.get(t)}const Vf=Gi("__proto__,__v_isRef,__isVue"),sc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(qi)),Wf=no(),zf=no(!1,!0),Gf=no(!0),Ho=Kf();function Kf(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=ue(this);for(let i=0,o=this.length;i<o;i++)Ke(r,"get",i+"");const s=r[t](...n);return s===-1||s===!1?r[t](...n.map(ue)):s}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){$n();const r=ue(this)[t].apply(this,n);return jn(),r}}),e}function Yf(e){const t=ue(this);return Ke(t,"has",e),t.hasOwnProperty(e)}function no(e=!1,t=!1){return function(r,s,i){if(s==="__v_isReactive")return!e;if(s==="__v_isReadonly")return e;if(s==="__v_isShallow")return t;if(s==="__v_raw"&&i===(e?t?ud:cc:t?lc:ac).get(r))return r;const o=Z(r);if(!e){if(o&&de(Ho,s))return Reflect.get(Ho,s,i);if(s==="hasOwnProperty")return Yf}const l=Reflect.get(r,s,i);return(qi(s)?sc.has(s):Vf(s))||(e||Ke(r,"get",s),t)?l:Ee(l)?o&&Xi(s)?l:l.value:ye(l)?e?fc(l):It(l):l}}const qf=ic(),Xf=ic(!0);function ic(e=!1){return function(n,r,s,i){let o=n[r];if(Nn(o)&&Ee(o)&&!Ee(s))return!1;if(!e&&(!Jr(s)&&!Nn(s)&&(o=ue(o),s=ue(s)),!Z(n)&&Ee(o)&&!Ee(s)))return o.value=s,!0;const l=Z(n)&&Xi(r)?Number(r)<n.length:de(n,r),a=Reflect.set(n,r,s,i);return n===ue(i)&&(l?ur(s,o)&&At(n,"set",r,s):At(n,"add",r,s)),a}}function Jf(e,t){const n=de(e,t);e[t];const r=Reflect.deleteProperty(e,t);return r&&n&&At(e,"delete",t,void 0),r}function Qf(e,t){const n=Reflect.has(e,t);return(!qi(t)||!sc.has(t))&&Ke(e,"has",t),n}function Zf(e){return Ke(e,"iterate",Z(e)?"length":dn),Reflect.ownKeys(e)}const oc={get:Wf,set:qf,deleteProperty:Jf,has:Qf,ownKeys:Zf},ed={get:Gf,set(e,t){return!0},deleteProperty(e,t){return!0}},td=Ie({},oc,{get:zf,set:Xf}),ro=e=>e,cs=e=>Reflect.getPrototypeOf(e);function Lr(e,t,n=!1,r=!1){e=e.__v_raw;const s=ue(e),i=ue(t);n||(t!==i&&Ke(s,"get",t),Ke(s,"get",i));const{has:o}=cs(s),l=r?ro:n?oo:fr;if(o.call(s,t))return l(e.get(t));if(o.call(s,i))return l(e.get(i));e!==s&&e.get(t)}function Ar(e,t=!1){const n=this.__v_raw,r=ue(n),s=ue(e);return t||(e!==s&&Ke(r,"has",e),Ke(r,"has",s)),e===s?n.has(e):n.has(e)||n.has(s)}function Ir(e,t=!1){return e=e.__v_raw,!t&&Ke(ue(e),"iterate",dn),Reflect.get(e,"size",e)}function Uo(e){e=ue(e);const t=ue(this);return cs(t).has.call(t,e)||(t.add(e),At(t,"add",e,e)),this}function Vo(e,t){t=ue(t);const n=ue(this),{has:r,get:s}=cs(n);let i=r.call(n,e);i||(e=ue(e),i=r.call(n,e));const o=s.call(n,e);return n.set(e,t),i?ur(t,o)&&At(n,"set",e,t):At(n,"add",e,t),this}function Wo(e){const t=ue(this),{has:n,get:r}=cs(t);let s=n.call(t,e);s||(e=ue(e),s=n.call(t,e)),r&&r.call(t,e);const i=t.delete(e);return s&&At(t,"delete",e,void 0),i}function zo(){const e=ue(this),t=e.size!==0,n=e.clear();return t&&At(e,"clear",void 0,void 0),n}function Nr(e,t){return function(r,s){const i=this,o=i.__v_raw,l=ue(o),a=t?ro:e?oo:fr;return!e&&Ke(l,"iterate",dn),o.forEach((c,u)=>r.call(s,a(c),a(u),i))}}function Rr(e,t,n){return function(...r){const s=this.__v_raw,i=ue(s),o=An(i),l=e==="entries"||e===Symbol.iterator&&o,a=e==="keys"&&o,c=s[e](...r),u=n?ro:t?oo:fr;return!t&&Ke(i,"iterate",a?ui:dn),{next(){const{value:f,done:d}=c.next();return d?{value:f,done:d}:{value:l?[u(f[0]),u(f[1])]:u(f),done:d}},[Symbol.iterator](){return this}}}}function Mt(e){return function(...t){return e==="delete"?!1:this}}function nd(){const e={get(i){return Lr(this,i)},get size(){return Ir(this)},has:Ar,add:Uo,set:Vo,delete:Wo,clear:zo,forEach:Nr(!1,!1)},t={get(i){return Lr(this,i,!1,!0)},get size(){return Ir(this)},has:Ar,add:Uo,set:Vo,delete:Wo,clear:zo,forEach:Nr(!1,!0)},n={get(i){return Lr(this,i,!0)},get size(){return Ir(this,!0)},has(i){return Ar.call(this,i,!0)},add:Mt("add"),set:Mt("set"),delete:Mt("delete"),clear:Mt("clear"),forEach:Nr(!0,!1)},r={get(i){return Lr(this,i,!0,!0)},get size(){return Ir(this,!0)},has(i){return Ar.call(this,i,!0)},add:Mt("add"),set:Mt("set"),delete:Mt("delete"),clear:Mt("clear"),forEach:Nr(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=Rr(i,!1,!1),n[i]=Rr(i,!0,!1),t[i]=Rr(i,!1,!0),r[i]=Rr(i,!0,!0)}),[e,n,t,r]}const[rd,sd,id,od]=nd();function so(e,t){const n=t?e?od:id:e?sd:rd;return(r,s,i)=>s==="__v_isReactive"?!e:s==="__v_isReadonly"?e:s==="__v_raw"?r:Reflect.get(de(n,s)&&s in r?n:r,s,i)}const ad={get:so(!1,!1)},ld={get:so(!1,!0)},cd={get:so(!0,!1)},ac=new WeakMap,lc=new WeakMap,cc=new WeakMap,ud=new WeakMap;function fd(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function dd(e){return e.__v_skip||!Object.isExtensible(e)?0:fd(Pf(e))}function It(e){return Nn(e)?e:io(e,!1,oc,ad,ac)}function uc(e){return io(e,!1,td,ld,lc)}function fc(e){return io(e,!0,ed,cd,cc)}function io(e,t,n,r,s){if(!ye(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=s.get(e);if(i)return i;const o=dd(e);if(o===0)return e;const l=new Proxy(e,o===2?r:n);return s.set(e,l),l}function Pt(e){return Nn(e)?Pt(e.__v_raw):!!(e&&e.__v_isReactive)}function Nn(e){return!!(e&&e.__v_isReadonly)}function Jr(e){return!!(e&&e.__v_isShallow)}function dc(e){return Pt(e)||Nn(e)}function ue(e){const t=e&&e.__v_raw;return t?ue(t):e}function us(e){return qr(e,"__v_skip",!0),e}const fr=e=>ye(e)?It(e):e,oo=e=>ye(e)?fc(e):e;function pc(e){Wt&&at&&(e=ue(e),rc(e.dep||(e.dep=eo())))}function mc(e,t){e=ue(e);const n=e.dep;n&&fi(n)}function Ee(e){return!!(e&&e.__v_isRef===!0)}function Q(e){return gc(e,!1)}function hc(e){return gc(e,!0)}function gc(e,t){return Ee(e)?e:new pd(e,t)}class pd{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:ue(t),this._value=n?t:fr(t)}get value(){return pc(this),this._value}set value(t){const n=this.__v_isShallow||Jr(t)||Nn(t);t=n?t:ue(t),ur(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:fr(t),mc(this))}}function yt(e){return Ee(e)?e.value:e}const md={get:(e,t,n)=>yt(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const s=e[t];return Ee(s)&&!Ee(n)?(s.value=n,!0):Reflect.set(e,t,n,r)}};function vc(e){return Pt(e)?e:new Proxy(e,md)}function bc(e){const t=Z(e)?new Array(e.length):{};for(const n in e)t[n]=yc(e,n);return t}class hd{constructor(t,n,r){this._object=t,this._key=n,this._defaultValue=r,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Uf(ue(this._object),this._key)}}class gd{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function vd(e,t,n){return Ee(e)?e:se(e)?new gd(e):ye(e)&&arguments.length>1?yc(e,t,n):Q(e)}function yc(e,t,n){const r=e[t];return Ee(r)?r:new hd(e,t,n)}class bd{constructor(t,n,r,s){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new to(t,()=>{this._dirty||(this._dirty=!0,mc(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!s,this.__v_isReadonly=r}get value(){const t=ue(this);return pc(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function yd(e,t,n=!1){let r,s;const i=se(e);return i?(r=e,s=ct):(r=e.get,s=e.set),new bd(r,s,i||!s,n)}function zt(e,t,n,r){let s;try{s=r?e(...r):e()}catch(i){fs(i,t,n)}return s}function st(e,t,n,r){if(se(e)){const i=zt(e,t,n,r);return i&&Kl(i)&&i.catch(o=>{fs(o,t,n)}),i}const s=[];for(let i=0;i<e.length;i++)s.push(st(e[i],t,n,r));return s}function fs(e,t,n,r=!0){const s=t?t.vnode:null;if(t){let i=t.parent;const o=t.proxy,l=n;for(;i;){const c=i.ec;if(c){for(let u=0;u<c.length;u++)if(c[u](e,o,l)===!1)return}i=i.parent}const a=t.appContext.config.errorHandler;if(a){zt(a,null,10,[e,o,l]);return}}_d(e,n,s,r)}function _d(e,t,n,r=!0){console.error(e)}let dr=!1,di=!1;const je=[];let vt=0;const In=[];let Ot=null,an=0;const _c=Promise.resolve();let ao=null;function Hn(e){const t=ao||_c;return e?t.then(this?e.bind(this):e):t}function Ed(e){let t=vt+1,n=je.length;for(;t<n;){const r=t+n>>>1;pr(je[r])<e?t=r+1:n=r}return t}function lo(e){(!je.length||!je.includes(e,dr&&e.allowRecurse?vt+1:vt))&&(e.id==null?je.push(e):je.splice(Ed(e.id),0,e),Ec())}function Ec(){!dr&&!di&&(di=!0,ao=_c.then(Sc))}function wd(e){const t=je.indexOf(e);t>vt&&je.splice(t,1)}function Sd(e){Z(e)?In.push(...e):(!Ot||!Ot.includes(e,e.allowRecurse?an+1:an))&&In.push(e),Ec()}function Go(e,t=dr?vt+1:0){for(;t<je.length;t++){const n=je[t];n&&n.pre&&(je.splice(t,1),t--,n())}}function wc(e){if(In.length){const t=[...new Set(In)];if(In.length=0,Ot){Ot.push(...t);return}for(Ot=t,Ot.sort((n,r)=>pr(n)-pr(r)),an=0;an<Ot.length;an++)Ot[an]();Ot=null,an=0}}const pr=e=>e.id==null?1/0:e.id,Td=(e,t)=>{const n=pr(e)-pr(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Sc(e){di=!1,dr=!0,je.sort(Td);const t=ct;try{for(vt=0;vt<je.length;vt++){const n=je[vt];n&&n.active!==!1&&zt(n,null,14)}}finally{vt=0,je.length=0,wc(),dr=!1,ao=null,(je.length||In.length)&&Sc()}}function Cd(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||_e;let s=n;const i=t.startsWith("update:"),o=i&&t.slice(7);if(o&&o in r){const u="".concat(o==="modelValue"?"model":o,"Modifiers"),{number:f,trim:d}=r[u]||_e;d&&(s=n.map(h=>Pe(h)?h.trim():h)),f&&(s=n.map(ai))}let l,a=r[l=ks(t)]||r[l=ks(Et(t))];!a&&i&&(a=r[l=ks(Bn(t))]),a&&st(a,e,6,s);const c=r[l+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,st(c,e,6,s)}}function Tc(e,t,n=!1){const r=t.emitsCache,s=r.get(e);if(s!==void 0)return s;const i=e.emits;let o={},l=!1;if(!se(e)){const a=c=>{const u=Tc(c,t,!0);u&&(l=!0,Ie(o,u))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!i&&!l?(ye(e)&&r.set(e,null),null):(Z(i)?i.forEach(a=>o[a]=null):Ie(o,i),ye(e)&&r.set(e,o),o)}function ds(e,t){return!e||!is(t)?!1:(t=t.slice(2).replace(/Once$/,""),de(e,t[0].toLowerCase()+t.slice(1))||de(e,Bn(t))||de(e,t))}let ke=null,ps=null;function Qr(e){const t=ke;return ke=e,ps=e&&e.type.__scopeId||null,t}function uE(e){ps=e}function fE(){ps=null}function Od(e,t=ke,n){if(!t||e._n)return e;const r=(...s)=>{r._d&&ia(-1);const i=Qr(t);let o;try{o=e(...s)}finally{Qr(i),r._d&&ia(1)}return o};return r._n=!0,r._c=!0,r._d=!0,r}function Ds(e){const{type:t,vnode:n,proxy:r,withProxy:s,props:i,propsOptions:[o],slots:l,attrs:a,emit:c,render:u,renderCache:f,data:d,setupState:h,ctx:v,inheritAttrs:b}=e;let C,g;const w=Qr(e);try{if(n.shapeFlag&4){const E=s||r;C=ht(u.call(E,E,f,i,h,d,v)),g=a}else{const E=t;C=ht(E.length>1?E(i,{attrs:a,slots:l,emit:c}):E(i,null)),g=t.props?a:xd(a)}}catch(E){rr.length=0,fs(E,e,1),C=ee(it)}let T=C;if(g&&b!==!1){const E=Object.keys(g),{shapeFlag:S}=T;E.length&&S&7&&(o&&E.some(Ki)&&(g=Pd(g,o)),T=Kt(T,g))}return n.dirs&&(T=Kt(T),T.dirs=T.dirs?T.dirs.concat(n.dirs):n.dirs),n.transition&&(T.transition=n.transition),C=T,Qr(w),C}const xd=e=>{let t;for(const n in e)(n==="class"||n==="style"||is(n))&&((t||(t={}))[n]=e[n]);return t},Pd=(e,t)=>{const n={};for(const r in e)(!Ki(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function Ld(e,t,n){const{props:r,children:s,component:i}=e,{props:o,children:l,patchFlag:a}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return r?Ko(r,o,c):!!o;if(a&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const d=u[f];if(o[d]!==r[d]&&!ds(c,d))return!0}}}else return(s||l)&&(!l||!l.$stable)?!0:r===o?!1:r?o?Ko(r,o,c):!0:!!o;return!1}function Ko(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let s=0;s<r.length;s++){const i=r[s];if(t[i]!==e[i]&&!ds(n,i))return!0}return!1}function Ad({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}const Id=e=>e.__isSuspense;function Nd(e,t){t&&t.pendingBranch?Z(e)?t.effects.push(...e):t.effects.push(e):Sd(e)}const Mr={};function De(e,t,n){return Cc(e,t,n)}function Cc(e,t,{immediate:n,deep:r,flush:s,onTrack:i,onTrigger:o}=_e){var l;const a=Zl()===((l=Ne)==null?void 0:l.scope)?Ne:null;let c,u=!1,f=!1;if(Ee(e)?(c=()=>e.value,u=Jr(e)):Pt(e)?(c=()=>e,r=!0):Z(e)?(f=!0,u=e.some(E=>Pt(E)||Jr(E)),c=()=>e.map(E=>{if(Ee(E))return E.value;if(Pt(E))return un(E);if(se(E))return zt(E,a,2)})):se(e)?t?c=()=>zt(e,a,2):c=()=>{if(!(a&&a.isUnmounted))return d&&d(),st(e,a,3,[h])}:c=ct,t&&r){const E=c;c=()=>un(E())}let d,h=E=>{d=w.onStop=()=>{zt(E,a,4)}},v;if(gr)if(h=ct,t?n&&st(t,a,3,[c(),f?[]:void 0,h]):c(),s==="sync"){const E=Cp();v=E.__watcherHandles||(E.__watcherHandles=[])}else return ct;let b=f?new Array(e.length).fill(Mr):Mr;const C=()=>{if(w.active)if(t){const E=w.run();(r||u||(f?E.some((S,N)=>ur(S,b[N])):ur(E,b)))&&(d&&d(),st(t,a,3,[E,b===Mr?void 0:f&&b[0]===Mr?[]:b,h]),b=E)}else w.run()};C.allowRecurse=!!t;let g;s==="sync"?g=C:s==="post"?g=()=>ze(C,a&&a.suspense):(C.pre=!0,a&&(C.id=a.uid),g=()=>lo(C));const w=new to(c,g);t?n?C():b=w.run():s==="post"?ze(w.run.bind(w),a&&a.suspense):w.run();const T=()=>{w.stop(),a&&a.scope&&Yi(a.scope.effects,w)};return v&&v.push(T),T}function Rd(e,t,n){const r=this.proxy,s=Pe(e)?e.includes(".")?Oc(r,e):()=>r[e]:e.bind(r,r);let i;se(t)?i=t:(i=t.handler,n=t);const o=Ne;Rn(this);const l=Cc(s,i.bind(r),n);return o?Rn(o):mn(),l}function Oc(e,t){const n=t.split(".");return()=>{let r=e;for(let s=0;s<n.length&&r;s++)r=r[n[s]];return r}}function un(e,t){if(!ye(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),Ee(e))un(e.value,t);else if(Z(e))for(let n=0;n<e.length;n++)un(e[n],t);else if(Gl(e)||An(e))e.forEach(n=>{un(n,t)});else if(ql(e))for(const n in e)un(e[n],t);return e}function xc(e,t){const n=ke;if(n===null)return e;const r=bs(n)||n.proxy,s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,l,a,c=_e]=t[i];o&&(se(o)&&(o={mounted:o,updated:o}),o.deep&&un(l),s.push({dir:o,instance:r,value:l,oldValue:void 0,arg:a,modifiers:c}))}return e}function nn(e,t,n,r){const s=e.dirs,i=t&&t.dirs;for(let o=0;o<s.length;o++){const l=s[o];i&&(l.oldValue=i[o].value);let a=l.dir[r];a&&($n(),st(a,n,8,[e.el,l,e,t]),jn())}}function Md(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Jt(()=>{e.isMounted=!0}),Er(()=>{e.isUnmounting=!0}),e}const nt=[Function,Array],Pc={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:nt,onEnter:nt,onAfterEnter:nt,onEnterCancelled:nt,onBeforeLeave:nt,onLeave:nt,onAfterLeave:nt,onLeaveCancelled:nt,onBeforeAppear:nt,onAppear:nt,onAfterAppear:nt,onAppearCancelled:nt},Fd={name:"BaseTransition",props:Pc,setup(e,{slots:t}){const n=Yt(),r=Md();let s;return()=>{const i=t.default&&Ac(t.default(),!0);if(!i||!i.length)return;let o=i[0];if(i.length>1){for(const b of i)if(b.type!==it){o=b;break}}const l=ue(e),{mode:a}=l;if(r.isLeaving)return Bs(o);const c=Yo(o);if(!c)return Bs(o);const u=pi(c,l,r,n);mi(c,u);const f=n.subTree,d=f&&Yo(f);let h=!1;const{getTransitionKey:v}=c.type;if(v){const b=v();s===void 0?s=b:b!==s&&(s=b,h=!0)}if(d&&d.type!==it&&(!ln(c,d)||h)){const b=pi(d,l,r,n);if(mi(d,b),a==="out-in")return r.isLeaving=!0,b.afterLeave=()=>{r.isLeaving=!1,n.update.active!==!1&&n.update()},Bs(o);a==="in-out"&&c.type!==it&&(b.delayLeave=(C,g,w)=>{const T=Lc(r,d);T[String(d.key)]=d,C._leaveCb=()=>{g(),C._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=w})}return o}}},kd=Fd;function Lc(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function pi(e,t,n,r){const{appear:s,mode:i,persisted:o=!1,onBeforeEnter:l,onEnter:a,onAfterEnter:c,onEnterCancelled:u,onBeforeLeave:f,onLeave:d,onAfterLeave:h,onLeaveCancelled:v,onBeforeAppear:b,onAppear:C,onAfterAppear:g,onAppearCancelled:w}=t,T=String(e.key),E=Lc(n,e),S=(F,x)=>{F&&st(F,r,9,x)},N=(F,x)=>{const W=x[1];S(F,x),Z(F)?F.every(P=>P.length<=1)&&W():F.length<=1&&W()},M={mode:i,persisted:o,beforeEnter(F){let x=l;if(!n.isMounted)if(s)x=b||l;else return;F._leaveCb&&F._leaveCb(!0);const W=E[T];W&&ln(e,W)&&W.el._leaveCb&&W.el._leaveCb(),S(x,[F])},enter(F){let x=a,W=c,P=u;if(!n.isMounted)if(s)x=C||a,W=g||c,P=w||u;else return;let A=!1;const H=F._enterCb=J=>{A||(A=!0,J?S(P,[F]):S(W,[F]),M.delayedLeave&&M.delayedLeave(),F._enterCb=void 0)};x?N(x,[F,H]):H()},leave(F,x){const W=String(e.key);if(F._enterCb&&F._enterCb(!0),n.isUnmounting)return x();S(f,[F]);let P=!1;const A=F._leaveCb=H=>{P||(P=!0,x(),H?S(v,[F]):S(h,[F]),F._leaveCb=void 0,E[W]===e&&delete E[W])};E[W]=e,d?N(d,[F,A]):A()},clone(F){return pi(F,t,n,r)}};return M}function Bs(e){if(ms(e))return e=Kt(e),e.children=null,e}function Yo(e){return ms(e)?e.children?e.children[0]:void 0:e}function mi(e,t){e.shapeFlag&6&&e.component?mi(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Ac(e,t=!1,n){let r=[],s=0;for(let i=0;i<e.length;i++){let o=e[i];const l=n==null?o.key:String(n)+String(o.key!=null?o.key:i);o.type===Ge?(o.patchFlag&128&&s++,r=r.concat(Ac(o.children,t,l))):(t||o.type!==it)&&r.push(l!=null?Kt(o,{key:l}):o)}if(s>1)for(let i=0;i<r.length;i++)r[i].patchFlag=-2;return r}function Nt(e,t){return se(e)?(()=>Ie({name:e.name},t,{setup:e}))():e}const er=e=>!!e.type.__asyncLoader,ms=e=>e.type.__isKeepAlive;function co(e,t){Ic(e,"a",t)}function hs(e,t){Ic(e,"da",t)}function Ic(e,t,n=Ne){const r=e.__wdc||(e.__wdc=()=>{let s=n;for(;s;){if(s.isDeactivated)return;s=s.parent}return e()});if(gs(t,r,n),n){let s=n.parent;for(;s&&s.parent;)ms(s.parent.vnode)&&Dd(r,t,n,s),s=s.parent}}function Dd(e,t,n,r){const s=gs(t,e,r,!0);wr(()=>{Yi(r[t],s)},n)}function gs(e,t,n=Ne,r=!1){if(n){const s=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;$n(),Rn(n);const l=st(t,n,e,o);return mn(),jn(),l});return r?s.unshift(i):s.push(i),i}}const Rt=e=>(t,n=Ne)=>(!gr||e==="sp")&&gs(e,(...r)=>t(...r),n),Nc=Rt("bm"),Jt=Rt("m"),Rc=Rt("bu"),uo=Rt("u"),Er=Rt("bum"),wr=Rt("um"),Bd=Rt("sp"),$d=Rt("rtg"),jd=Rt("rtc");function Hd(e,t=Ne){gs("ec",e,t)}const Mc="components";function dE(e,t){return Vd(Mc,e,!0,t)||e}const Ud=Symbol.for("v-ndc");function Vd(e,t,n=!0,r=!1){const s=ke||Ne;if(s){const i=s.type;if(e===Mc){const l=wp(i,!1);if(l&&(l===t||l===Et(t)||l===ls(Et(t))))return i}const o=qo(s[e]||i[e],t)||qo(s.appContext[e],t);return!o&&r?i:o}}function qo(e,t){return e&&(e[t]||e[Et(t)]||e[ls(Et(t))])}function pE(e,t,n,r){let s;const i=n&&n[r];if(Z(e)||Pe(e)){s=new Array(e.length);for(let o=0,l=e.length;o<l;o++)s[o]=t(e[o],o,void 0,i&&i[o])}else if(typeof e=="number"){s=new Array(e);for(let o=0;o<e;o++)s[o]=t(o+1,o,void 0,i&&i[o])}else if(ye(e))if(e[Symbol.iterator])s=Array.from(e,(o,l)=>t(o,l,void 0,i&&i[l]));else{const o=Object.keys(e);s=new Array(o.length);for(let l=0,a=o.length;l<a;l++){const c=o[l];s[l]=t(e[c],c,l,i&&i[l])}}else s=[];return n&&(n[r]=s),s}function mE(e,t,n={},r,s){if(ke.isCE||ke.parent&&er(ke.parent)&&ke.parent.isCE)return t!=="default"&&(n.name=t),ee("slot",n,r&&r());let i=e[t];i&&i._c&&(i._d=!1),Wc();const o=i&&Fc(i(n)),l=Gc(Ge,{key:n.key||o&&o.key||"_".concat(t)},o||(r?r():[]),o&&e._===1?64:-2);return!s&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),i&&i._c&&(i._d=!0),l}function Fc(e){return e.some(t=>es(t)?!(t.type===it||t.type===Ge&&!Fc(t.children)):!0)?e:null}const hi=e=>e?qc(e)?bs(e)||e.proxy:hi(e.parent):null,tr=Ie(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>hi(e.parent),$root:e=>hi(e.root),$emit:e=>e.emit,$options:e=>fo(e),$forceUpdate:e=>e.f||(e.f=()=>lo(e.update)),$nextTick:e=>e.n||(e.n=Hn.bind(e.proxy)),$watch:e=>Rd.bind(e)}),$s=(e,t)=>e!==_e&&!e.__isScriptSetup&&de(e,t),Wd={get({_:e},t){const{ctx:n,setupState:r,data:s,props:i,accessCache:o,type:l,appContext:a}=e;let c;if(t[0]!=="$"){const h=o[t];if(h!==void 0)switch(h){case 1:return r[t];case 2:return s[t];case 4:return n[t];case 3:return i[t]}else{if($s(r,t))return o[t]=1,r[t];if(s!==_e&&de(s,t))return o[t]=2,s[t];if((c=e.propsOptions[0])&&de(c,t))return o[t]=3,i[t];if(n!==_e&&de(n,t))return o[t]=4,n[t];gi&&(o[t]=0)}}const u=tr[t];let f,d;if(u)return t==="$attrs"&&Ke(e,"get",t),u(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==_e&&de(n,t))return o[t]=4,n[t];if(d=a.config.globalProperties,de(d,t))return d[t]},set({_:e},t,n){const{data:r,setupState:s,ctx:i}=e;return $s(s,t)?(s[t]=n,!0):r!==_e&&de(r,t)?(r[t]=n,!0):de(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:s,propsOptions:i}},o){let l;return!!n[o]||e!==_e&&de(e,o)||$s(t,o)||(l=i[0])&&de(l,o)||de(r,o)||de(tr,o)||de(s.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:de(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Xo(e){return Z(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let gi=!0;function zd(e){const t=fo(e),n=e.proxy,r=e.ctx;gi=!1,t.beforeCreate&&Jo(t.beforeCreate,e,"bc");const{data:s,computed:i,methods:o,watch:l,provide:a,inject:c,created:u,beforeMount:f,mounted:d,beforeUpdate:h,updated:v,activated:b,deactivated:C,beforeDestroy:g,beforeUnmount:w,destroyed:T,unmounted:E,render:S,renderTracked:N,renderTriggered:M,errorCaptured:F,serverPrefetch:x,expose:W,inheritAttrs:P,components:A,directives:H,filters:J}=t;if(c&&Gd(c,r,null),o)for(const ne in o){const ie=o[ne];se(ie)&&(r[ne]=ie.bind(n))}if(s){const ne=s.call(n,n);ye(ne)&&(e.data=It(ne))}if(gi=!0,i)for(const ne in i){const ie=i[ne],we=se(ie)?ie.bind(n,n):se(ie.get)?ie.get.bind(n,n):ct,Le=!se(ie)&&se(ie.set)?ie.set.bind(n):ct,Te=fe({get:we,set:Le});Object.defineProperty(r,ne,{enumerable:!0,configurable:!0,get:()=>Te.value,set:Se=>Te.value=Se})}if(l)for(const ne in l)kc(l[ne],r,n,ne);if(a){const ne=se(a)?a.call(n):a;Reflect.ownKeys(ne).forEach(ie=>{pn(ie,ne[ie])})}u&&Jo(u,e,"c");function X(ne,ie){Z(ie)?ie.forEach(we=>ne(we.bind(n))):ie&&ne(ie.bind(n))}if(X(Nc,f),X(Jt,d),X(Rc,h),X(uo,v),X(co,b),X(hs,C),X(Hd,F),X(jd,N),X($d,M),X(Er,w),X(wr,E),X(Bd,x),Z(W))if(W.length){const ne=e.exposed||(e.exposed={});W.forEach(ie=>{Object.defineProperty(ne,ie,{get:()=>n[ie],set:we=>n[ie]=we})})}else e.exposed||(e.exposed={});S&&e.render===ct&&(e.render=S),P!=null&&(e.inheritAttrs=P),A&&(e.components=A),H&&(e.directives=H)}function Gd(e,t,n=ct){Z(e)&&(e=vi(e));for(const r in e){const s=e[r];let i;ye(s)?"default"in s?i=Ze(s.from||r,s.default,!0):i=Ze(s.from||r):i=Ze(s),Ee(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[r]=i}}function Jo(e,t,n){st(Z(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,n)}function kc(e,t,n,r){const s=r.includes(".")?Oc(n,r):()=>n[r];if(Pe(e)){const i=t[e];se(i)&&De(s,i)}else if(se(e))De(s,e.bind(n));else if(ye(e))if(Z(e))e.forEach(i=>kc(i,t,n,r));else{const i=se(e.handler)?e.handler.bind(n):t[e.handler];se(i)&&De(s,i,e)}}function fo(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:s,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let a;return l?a=l:!s.length&&!n&&!r?a=t:(a={},s.length&&s.forEach(c=>Zr(a,c,o,!0)),Zr(a,t,o)),ye(t)&&i.set(t,a),a}function Zr(e,t,n,r=!1){const{mixins:s,extends:i}=t;i&&Zr(e,i,n,!0),s&&s.forEach(o=>Zr(e,o,n,!0));for(const o in t)if(!(r&&o==="expose")){const l=Kd[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const Kd={data:Qo,props:Zo,emits:Zo,methods:Zn,computed:Zn,beforeCreate:He,created:He,beforeMount:He,mounted:He,beforeUpdate:He,updated:He,beforeDestroy:He,beforeUnmount:He,destroyed:He,unmounted:He,activated:He,deactivated:He,errorCaptured:He,serverPrefetch:He,components:Zn,directives:Zn,watch:qd,provide:Qo,inject:Yd};function Qo(e,t){return t?e?function(){return Ie(se(e)?e.call(this,this):e,se(t)?t.call(this,this):t)}:t:e}function Yd(e,t){return Zn(vi(e),vi(t))}function vi(e){if(Z(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function He(e,t){return e?[...new Set([].concat(e,t))]:t}function Zn(e,t){return e?Ie(Object.create(null),e,t):t}function Zo(e,t){return e?Z(e)&&Z(t)?[...new Set([...e,...t])]:Ie(Object.create(null),Xo(e),Xo(t!=null?t:{})):t}function qd(e,t){if(!e)return t;if(!t)return e;const n=Ie(Object.create(null),e);for(const r in t)n[r]=He(e[r],t[r]);return n}function Dc(){return{app:null,config:{isNativeTag:Cf,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Xd=0;function Jd(e,t){return function(r,s=null){se(r)||(r=Ie({},r)),s!=null&&!ye(s)&&(s=null);const i=Dc(),o=new Set;let l=!1;const a=i.app={_uid:Xd++,_component:r,_props:s,_container:null,_context:i,_instance:null,version:Op,get config(){return i.config},set config(c){},use(c,...u){return o.has(c)||(c&&se(c.install)?(o.add(c),c.install(a,...u)):se(c)&&(o.add(c),c(a,...u))),a},mixin(c){return i.mixins.includes(c)||i.mixins.push(c),a},component(c,u){return u?(i.components[c]=u,a):i.components[c]},directive(c,u){return u?(i.directives[c]=u,a):i.directives[c]},mount(c,u,f){if(!l){const d=ee(r,s);return d.appContext=i,u&&t?t(d,c):e(d,c,f),l=!0,a._container=c,c.__vue_app__=a,bs(d.component)||d.component.proxy}},unmount(){l&&(e(null,a._container),delete a._container.__vue_app__)},provide(c,u){return i.provides[c]=u,a},runWithContext(c){mr=a;try{return c()}finally{mr=null}}};return a}}let mr=null;function pn(e,t){if(Ne){let n=Ne.provides;const r=Ne.parent&&Ne.parent.provides;r===n&&(n=Ne.provides=Object.create(r)),n[e]=t}}function Ze(e,t,n=!1){const r=Ne||ke;if(r||mr){const s=r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:mr._context.provides;if(s&&e in s)return s[e];if(arguments.length>1)return n&&se(t)?t.call(r&&r.proxy):t}}function Qd(){return!!(Ne||ke||mr)}function Zd(e,t,n,r=!1){const s={},i={};qr(i,vs,1),e.propsDefaults=Object.create(null),Bc(e,t,s,i);for(const o in e.propsOptions[0])o in s||(s[o]=void 0);n?e.props=r?s:uc(s):e.type.props?e.props=s:e.props=i,e.attrs=i}function ep(e,t,n,r){const{props:s,attrs:i,vnode:{patchFlag:o}}=e,l=ue(s),[a]=e.propsOptions;let c=!1;if((r||o>0)&&!(o&16)){if(o&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let d=u[f];if(ds(e.emitsOptions,d))continue;const h=t[d];if(a)if(de(i,d))h!==i[d]&&(i[d]=h,c=!0);else{const v=Et(d);s[v]=bi(a,l,v,h,e,!1)}else h!==i[d]&&(i[d]=h,c=!0)}}}else{Bc(e,t,s,i)&&(c=!0);let u;for(const f in l)(!t||!de(t,f)&&((u=Bn(f))===f||!de(t,u)))&&(a?n&&(n[f]!==void 0||n[u]!==void 0)&&(s[f]=bi(a,l,f,void 0,e,!0)):delete s[f]);if(i!==l)for(const f in i)(!t||!de(t,f))&&(delete i[f],c=!0)}c&&At(e,"set","$attrs")}function Bc(e,t,n,r){const[s,i]=e.propsOptions;let o=!1,l;if(t)for(let a in t){if(Hr(a))continue;const c=t[a];let u;s&&de(s,u=Et(a))?!i||!i.includes(u)?n[u]=c:(l||(l={}))[u]=c:ds(e.emitsOptions,a)||(!(a in r)||c!==r[a])&&(r[a]=c,o=!0)}if(i){const a=ue(n),c=l||_e;for(let u=0;u<i.length;u++){const f=i[u];n[f]=bi(s,a,f,c[f],e,!de(c,f))}}return o}function bi(e,t,n,r,s,i){const o=e[n];if(o!=null){const l=de(o,"default");if(l&&r===void 0){const a=o.default;if(o.type!==Function&&!o.skipFactory&&se(a)){const{propsDefaults:c}=s;n in c?r=c[n]:(Rn(s),r=c[n]=a.call(null,t),mn())}else r=a}o[0]&&(i&&!l?r=!1:o[1]&&(r===""||r===Bn(n))&&(r=!0))}return r}function $c(e,t,n=!1){const r=t.propsCache,s=r.get(e);if(s)return s;const i=e.props,o={},l=[];let a=!1;if(!se(e)){const u=f=>{a=!0;const[d,h]=$c(f,t,!0);Ie(o,d),h&&l.push(...h)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!a)return ye(e)&&r.set(e,Ln),Ln;if(Z(i))for(let u=0;u<i.length;u++){const f=Et(i[u]);ea(f)&&(o[f]=_e)}else if(i)for(const u in i){const f=Et(u);if(ea(f)){const d=i[u],h=o[f]=Z(d)||se(d)?{type:d}:Ie({},d);if(h){const v=ra(Boolean,h.type),b=ra(String,h.type);h[0]=v>-1,h[1]=b<0||v<b,(v>-1||de(h,"default"))&&l.push(f)}}}const c=[o,l];return ye(e)&&r.set(e,c),c}function ea(e){return e[0]!=="$"}function ta(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:e===null?"null":""}function na(e,t){return ta(e)===ta(t)}function ra(e,t){return Z(t)?t.findIndex(n=>na(n,e)):se(t)&&na(t,e)?0:-1}const jc=e=>e[0]==="_"||e==="$stable",po=e=>Z(e)?e.map(ht):[ht(e)],tp=(e,t,n)=>{if(t._n)return t;const r=Od((...s)=>po(t(...s)),n);return r._c=!1,r},Hc=(e,t,n)=>{const r=e._ctx;for(const s in e){if(jc(s))continue;const i=e[s];if(se(i))t[s]=tp(s,i,r);else if(i!=null){const o=po(i);t[s]=()=>o}}},Uc=(e,t)=>{const n=po(t);e.slots.default=()=>n},np=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=ue(t),qr(t,"_",n)):Hc(t,e.slots={})}else e.slots={},t&&Uc(e,t);qr(e.slots,vs,1)},rp=(e,t,n)=>{const{vnode:r,slots:s}=e;let i=!0,o=_e;if(r.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:(Ie(s,t),!n&&l===1&&delete s._):(i=!t.$stable,Hc(t,s)),o=t}else t&&(Uc(e,t),o={default:1});if(i)for(const l in s)!jc(l)&&!(l in o)&&delete s[l]};function yi(e,t,n,r,s=!1){if(Z(e)){e.forEach((d,h)=>yi(d,t&&(Z(t)?t[h]:t),n,r,s));return}if(er(r)&&!s)return;const i=r.shapeFlag&4?bs(r.component)||r.component.proxy:r.el,o=s?null:i,{i:l,r:a}=e,c=t&&t.r,u=l.refs===_e?l.refs={}:l.refs,f=l.setupState;if(c!=null&&c!==a&&(Pe(c)?(u[c]=null,de(f,c)&&(f[c]=null)):Ee(c)&&(c.value=null)),se(a))zt(a,l,12,[o,u]);else{const d=Pe(a),h=Ee(a);if(d||h){const v=()=>{if(e.f){const b=d?de(f,a)?f[a]:u[a]:a.value;s?Z(b)&&Yi(b,i):Z(b)?b.includes(i)||b.push(i):d?(u[a]=[i],de(f,a)&&(f[a]=u[a])):(a.value=[i],e.k&&(u[e.k]=a.value))}else d?(u[a]=o,de(f,a)&&(f[a]=o)):h&&(a.value=o,e.k&&(u[e.k]=o))};o?(v.id=-1,ze(v,n)):v()}}}const ze=Nd;function sp(e){return ip(e)}function ip(e,t){const n=li();n.__VUE__=!0;const{insert:r,remove:s,patchProp:i,createElement:o,createText:l,createComment:a,setText:c,setElementText:u,parentNode:f,nextSibling:d,setScopeId:h=ct,insertStaticContent:v}=e,b=(_,p,m,y=null,O=null,R=null,$=!1,j=null,U=!!p.dynamicChildren)=>{if(_===p)return;_&&!ln(_,p)&&(y=k(_),Se(_,O,R,!0),_=null),p.patchFlag===-2&&(U=!1,p.dynamicChildren=null);const{type:B,ref:L,shapeFlag:I}=p;switch(B){case Sr:C(_,p,m,y);break;case it:g(_,p,m,y);break;case js:_==null&&w(p,m,y,$);break;case Ge:A(_,p,m,y,O,R,$,j,U);break;default:I&1?S(_,p,m,y,O,R,$,j,U):I&6?H(_,p,m,y,O,R,$,j,U):(I&64||I&128)&&B.process(_,p,m,y,O,R,$,j,U,V)}L!=null&&O&&yi(L,_&&_.ref,R,p||_,!p)},C=(_,p,m,y)=>{if(_==null)r(p.el=l(p.children),m,y);else{const O=p.el=_.el;p.children!==_.children&&c(O,p.children)}},g=(_,p,m,y)=>{_==null?r(p.el=a(p.children||""),m,y):p.el=_.el},w=(_,p,m,y)=>{[_.el,_.anchor]=v(_.children,p,m,y,_.el,_.anchor)},T=({el:_,anchor:p},m,y)=>{let O;for(;_&&_!==p;)O=d(_),r(_,m,y),_=O;r(p,m,y)},E=({el:_,anchor:p})=>{let m;for(;_&&_!==p;)m=d(_),s(_),_=m;s(p)},S=(_,p,m,y,O,R,$,j,U)=>{$=$||p.type==="svg",_==null?N(p,m,y,O,R,$,j,U):x(_,p,O,R,$,j,U)},N=(_,p,m,y,O,R,$,j)=>{let U,B;const{type:L,props:I,shapeFlag:Y,transition:q,dirs:oe}=_;if(U=_.el=o(_.type,R,I&&I.is,I),Y&8?u(U,_.children):Y&16&&F(_.children,U,null,y,O,R&&L!=="foreignObject",$,j),oe&&nn(_,null,y,"created"),M(U,_,_.scopeId,$,y),I){for(const le in I)le!=="value"&&!Hr(le)&&i(U,le,null,I[le],R,_.children,y,O,Ce);"value"in I&&i(U,"value",null,I.value),(B=I.onVnodeBeforeMount)&&pt(B,y,_)}oe&&nn(_,null,y,"beforeMount");const he=(!O||O&&!O.pendingBranch)&&q&&!q.persisted;he&&q.beforeEnter(U),r(U,p,m),((B=I&&I.onVnodeMounted)||he||oe)&&ze(()=>{B&&pt(B,y,_),he&&q.enter(U),oe&&nn(_,null,y,"mounted")},O)},M=(_,p,m,y,O)=>{if(m&&h(_,m),y)for(let R=0;R<y.length;R++)h(_,y[R]);if(O){let R=O.subTree;if(p===R){const $=O.vnode;M(_,$,$.scopeId,$.slotScopeIds,O.parent)}}},F=(_,p,m,y,O,R,$,j,U=0)=>{for(let B=U;B<_.length;B++){const L=_[B]=j?Ht(_[B]):ht(_[B]);b(null,L,p,m,y,O,R,$,j)}},x=(_,p,m,y,O,R,$)=>{const j=p.el=_.el;let{patchFlag:U,dynamicChildren:B,dirs:L}=p;U|=_.patchFlag&16;const I=_.props||_e,Y=p.props||_e;let q;m&&rn(m,!1),(q=Y.onVnodeBeforeUpdate)&&pt(q,m,p,_),L&&nn(p,_,m,"beforeUpdate"),m&&rn(m,!0);const oe=O&&p.type!=="foreignObject";if(B?W(_.dynamicChildren,B,j,m,y,oe,R):$||ie(_,p,j,null,m,y,oe,R,!1),U>0){if(U&16)P(j,p,I,Y,m,y,O);else if(U&2&&I.class!==Y.class&&i(j,"class",null,Y.class,O),U&4&&i(j,"style",I.style,Y.style,O),U&8){const he=p.dynamicProps;for(let le=0;le<he.length;le++){const Oe=he[le],tt=I[Oe],En=Y[Oe];(En!==tt||Oe==="value")&&i(j,Oe,tt,En,O,_.children,m,y,Ce)}}U&1&&_.children!==p.children&&u(j,p.children)}else!$&&B==null&&P(j,p,I,Y,m,y,O);((q=Y.onVnodeUpdated)||L)&&ze(()=>{q&&pt(q,m,p,_),L&&nn(p,_,m,"updated")},y)},W=(_,p,m,y,O,R,$)=>{for(let j=0;j<p.length;j++){const U=_[j],B=p[j],L=U.el&&(U.type===Ge||!ln(U,B)||U.shapeFlag&70)?f(U.el):m;b(U,B,L,null,y,O,R,$,!0)}},P=(_,p,m,y,O,R,$)=>{if(m!==y){if(m!==_e)for(const j in m)!Hr(j)&&!(j in y)&&i(_,j,m[j],null,$,p.children,O,R,Ce);for(const j in y){if(Hr(j))continue;const U=y[j],B=m[j];U!==B&&j!=="value"&&i(_,j,B,U,$,p.children,O,R,Ce)}"value"in y&&i(_,"value",m.value,y.value)}},A=(_,p,m,y,O,R,$,j,U)=>{const B=p.el=_?_.el:l(""),L=p.anchor=_?_.anchor:l("");let{patchFlag:I,dynamicChildren:Y,slotScopeIds:q}=p;q&&(j=j?j.concat(q):q),_==null?(r(B,m,y),r(L,m,y),F(p.children,m,L,O,R,$,j,U)):I>0&&I&64&&Y&&_.dynamicChildren?(W(_.dynamicChildren,Y,m,O,R,$,j),(p.key!=null||O&&p===O.subTree)&&mo(_,p,!0)):ie(_,p,m,L,O,R,$,j,U)},H=(_,p,m,y,O,R,$,j,U)=>{p.slotScopeIds=j,_==null?p.shapeFlag&512?O.ctx.activate(p,m,y,$,U):J(p,m,y,O,R,$,U):re(_,p,U)},J=(_,p,m,y,O,R,$)=>{const j=_.component=vp(_,y,O);if(ms(_)&&(j.ctx.renderer=V),bp(j),j.asyncDep){if(O&&O.registerDep(j,X),!_.el){const U=j.subTree=ee(it);g(null,U,p,m)}return}X(j,_,p,m,O,R,$)},re=(_,p,m)=>{const y=p.component=_.component;if(Ld(_,p,m))if(y.asyncDep&&!y.asyncResolved){ne(y,p,m);return}else y.next=p,wd(y.update),y.update();else p.el=_.el,y.vnode=p},X=(_,p,m,y,O,R,$)=>{const j=()=>{if(_.isMounted){let{next:L,bu:I,u:Y,parent:q,vnode:oe}=_,he=L,le;rn(_,!1),L?(L.el=oe.el,ne(_,L,$)):L=oe,I&&Ur(I),(le=L.props&&L.props.onVnodeBeforeUpdate)&&pt(le,q,L,oe),rn(_,!0);const Oe=Ds(_),tt=_.subTree;_.subTree=Oe,b(tt,Oe,f(tt.el),k(tt),_,O,R),L.el=Oe.el,he===null&&Ad(_,Oe.el),Y&&ze(Y,O),(le=L.props&&L.props.onVnodeUpdated)&&ze(()=>pt(le,q,L,oe),O)}else{let L;const{el:I,props:Y}=p,{bm:q,m:oe,parent:he}=_,le=er(p);if(rn(_,!1),q&&Ur(q),!le&&(L=Y&&Y.onVnodeBeforeMount)&&pt(L,he,p),rn(_,!0),I&&ae){const Oe=()=>{_.subTree=Ds(_),ae(I,_.subTree,_,O,null)};le?p.type.__asyncLoader().then(()=>!_.isUnmounted&&Oe()):Oe()}else{const Oe=_.subTree=Ds(_);b(null,Oe,m,y,_,O,R),p.el=Oe.el}if(oe&&ze(oe,O),!le&&(L=Y&&Y.onVnodeMounted)){const Oe=p;ze(()=>pt(L,he,Oe),O)}(p.shapeFlag&256||he&&er(he.vnode)&&he.vnode.shapeFlag&256)&&_.a&&ze(_.a,O),_.isMounted=!0,p=m=y=null}},U=_.effect=new to(j,()=>lo(B),_.scope),B=_.update=()=>U.run();B.id=_.uid,rn(_,!0),B()},ne=(_,p,m)=>{p.component=_;const y=_.vnode.props;_.vnode=p,_.next=null,ep(_,p.props,y,m),rp(_,p.children,m),$n(),Go(),jn()},ie=(_,p,m,y,O,R,$,j,U=!1)=>{const B=_&&_.children,L=_?_.shapeFlag:0,I=p.children,{patchFlag:Y,shapeFlag:q}=p;if(Y>0){if(Y&128){Le(B,I,m,y,O,R,$,j,U);return}else if(Y&256){we(B,I,m,y,O,R,$,j,U);return}}q&8?(L&16&&Ce(B,O,R),I!==B&&u(m,I)):L&16?q&16?Le(B,I,m,y,O,R,$,j,U):Ce(B,O,R,!0):(L&8&&u(m,""),q&16&&F(I,m,y,O,R,$,j,U))},we=(_,p,m,y,O,R,$,j,U)=>{_=_||Ln,p=p||Ln;const B=_.length,L=p.length,I=Math.min(B,L);let Y;for(Y=0;Y<I;Y++){const q=p[Y]=U?Ht(p[Y]):ht(p[Y]);b(_[Y],q,m,null,O,R,$,j,U)}B>L?Ce(_,O,R,!0,!1,I):F(p,m,y,O,R,$,j,U,I)},Le=(_,p,m,y,O,R,$,j,U)=>{let B=0;const L=p.length;let I=_.length-1,Y=L-1;for(;B<=I&&B<=Y;){const q=_[B],oe=p[B]=U?Ht(p[B]):ht(p[B]);if(ln(q,oe))b(q,oe,m,null,O,R,$,j,U);else break;B++}for(;B<=I&&B<=Y;){const q=_[I],oe=p[Y]=U?Ht(p[Y]):ht(p[Y]);if(ln(q,oe))b(q,oe,m,null,O,R,$,j,U);else break;I--,Y--}if(B>I){if(B<=Y){const q=Y+1,oe=q<L?p[q].el:y;for(;B<=Y;)b(null,p[B]=U?Ht(p[B]):ht(p[B]),m,oe,O,R,$,j,U),B++}}else if(B>Y)for(;B<=I;)Se(_[B],O,R,!0),B++;else{const q=B,oe=B,he=new Map;for(B=oe;B<=Y;B++){const Xe=p[B]=U?Ht(p[B]):ht(p[B]);Xe.key!=null&&he.set(Xe.key,B)}let le,Oe=0;const tt=Y-oe+1;let En=!1,Fo=0;const Vn=new Array(tt);for(B=0;B<tt;B++)Vn[B]=0;for(B=q;B<=I;B++){const Xe=_[B];if(Oe>=tt){Se(Xe,O,R,!0);continue}let dt;if(Xe.key!=null)dt=he.get(Xe.key);else for(le=oe;le<=Y;le++)if(Vn[le-oe]===0&&ln(Xe,p[le])){dt=le;break}dt===void 0?Se(Xe,O,R,!0):(Vn[dt-oe]=B+1,dt>=Fo?Fo=dt:En=!0,b(Xe,p[dt],m,null,O,R,$,j,U),Oe++)}const ko=En?op(Vn):Ln;for(le=ko.length-1,B=tt-1;B>=0;B--){const Xe=oe+B,dt=p[Xe],Do=Xe+1<L?p[Xe+1].el:y;Vn[B]===0?b(null,dt,m,Do,O,R,$,j,U):En&&(le<0||B!==ko[le]?Te(dt,m,Do,2):le--)}}},Te=(_,p,m,y,O=null)=>{const{el:R,type:$,transition:j,children:U,shapeFlag:B}=_;if(B&6){Te(_.component.subTree,p,m,y);return}if(B&128){_.suspense.move(p,m,y);return}if(B&64){$.move(_,p,m,V);return}if($===Ge){r(R,p,m);for(let I=0;I<U.length;I++)Te(U[I],p,m,y);r(_.anchor,p,m);return}if($===js){T(_,p,m);return}if(y!==2&&B&1&&j)if(y===0)j.beforeEnter(R),r(R,p,m),ze(()=>j.enter(R),O);else{const{leave:I,delayLeave:Y,afterLeave:q}=j,oe=()=>r(R,p,m),he=()=>{I(R,()=>{oe(),q&&q()})};Y?Y(R,oe,he):he()}else r(R,p,m)},Se=(_,p,m,y=!1,O=!1)=>{const{type:R,props:$,ref:j,children:U,dynamicChildren:B,shapeFlag:L,patchFlag:I,dirs:Y}=_;if(j!=null&&yi(j,null,m,_,!0),L&256){p.ctx.deactivate(_);return}const q=L&1&&Y,oe=!er(_);let he;if(oe&&(he=$&&$.onVnodeBeforeUnmount)&&pt(he,p,_),L&6)ft(_.component,m,y);else{if(L&128){_.suspense.unmount(m,y);return}q&&nn(_,null,p,"beforeUnmount"),L&64?_.type.remove(_,p,m,O,V,y):B&&(R!==Ge||I>0&&I&64)?Ce(B,p,m,!1,!0):(R===Ge&&I&384||!O&&L&16)&&Ce(U,p,m),y&&Ye(_)}(oe&&(he=$&&$.onVnodeUnmounted)||q)&&ze(()=>{he&&pt(he,p,_),q&&nn(_,null,p,"unmounted")},m)},Ye=_=>{const{type:p,el:m,anchor:y,transition:O}=_;if(p===Ge){qe(m,y);return}if(p===js){E(_);return}const R=()=>{s(m),O&&!O.persisted&&O.afterLeave&&O.afterLeave()};if(_.shapeFlag&1&&O&&!O.persisted){const{leave:$,delayLeave:j}=O,U=()=>$(m,R);j?j(_.el,R,U):U()}else R()},qe=(_,p)=>{let m;for(;_!==p;)m=d(_),s(_),_=m;s(p)},ft=(_,p,m)=>{const{bum:y,scope:O,update:R,subTree:$,um:j}=_;y&&Ur(y),O.stop(),R&&(R.active=!1,Se($,_,p,m)),j&&ze(j,p),ze(()=>{_.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&_.asyncDep&&!_.asyncResolved&&_.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},Ce=(_,p,m,y=!1,O=!1,R=0)=>{for(let $=R;$<_.length;$++)Se(_[$],p,m,y,O)},k=_=>_.shapeFlag&6?k(_.component.subTree):_.shapeFlag&128?_.suspense.next():d(_.anchor||_.el),z=(_,p,m)=>{_==null?p._vnode&&Se(p._vnode,null,null,!0):b(p._vnode||null,_,p,null,null,null,m),Go(),wc(),p._vnode=_},V={p:b,um:Se,m:Te,r:Ye,mt:J,mc:F,pc:ie,pbc:W,n:k,o:e};let K,ae;return t&&([K,ae]=t(V)),{render:z,hydrate:K,createApp:Jd(z,K)}}function rn({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function mo(e,t,n=!1){const r=e.children,s=t.children;if(Z(r)&&Z(s))for(let i=0;i<r.length;i++){const o=r[i];let l=s[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=s[i]=Ht(s[i]),l.el=o.el),n||mo(o,l)),l.type===Sr&&(l.el=o.el)}}function op(e){const t=e.slice(),n=[0];let r,s,i,o,l;const a=e.length;for(r=0;r<a;r++){const c=e[r];if(c!==0){if(s=n[n.length-1],e[s]<c){t[r]=s,n.push(r);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<c?i=l+1:o=l;c<e[n[i]]&&(i>0&&(t[r]=n[i-1]),n[i]=r)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}const ap=e=>e.__isTeleport,nr=e=>e&&(e.disabled||e.disabled===""),sa=e=>typeof SVGElement<"u"&&e instanceof SVGElement,_i=(e,t)=>{const n=e&&e.to;return Pe(n)?t?t(n):null:n},lp={__isTeleport:!0,process(e,t,n,r,s,i,o,l,a,c){const{mc:u,pc:f,pbc:d,o:{insert:h,querySelector:v,createText:b,createComment:C}}=c,g=nr(t.props);let{shapeFlag:w,children:T,dynamicChildren:E}=t;if(e==null){const S=t.el=b(""),N=t.anchor=b("");h(S,n,r),h(N,n,r);const M=t.target=_i(t.props,v),F=t.targetAnchor=b("");M&&(h(F,M),o=o||sa(M));const x=(W,P)=>{w&16&&u(T,W,P,s,i,o,l,a)};g?x(n,N):M&&x(M,F)}else{t.el=e.el;const S=t.anchor=e.anchor,N=t.target=e.target,M=t.targetAnchor=e.targetAnchor,F=nr(e.props),x=F?n:N,W=F?S:M;if(o=o||sa(N),E?(d(e.dynamicChildren,E,x,s,i,o,l),mo(e,t,!0)):a||f(e,t,x,W,s,i,o,l,!1),g)F||Fr(t,n,S,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const P=t.target=_i(t.props,v);P&&Fr(t,P,null,c,0)}else F&&Fr(t,N,M,c,1)}Vc(t)},remove(e,t,n,r,{um:s,o:{remove:i}},o){const{shapeFlag:l,children:a,anchor:c,targetAnchor:u,target:f,props:d}=e;if(f&&i(u),(o||!nr(d))&&(i(c),l&16))for(let h=0;h<a.length;h++){const v=a[h];s(v,t,n,!0,!!v.dynamicChildren)}},move:Fr,hydrate:cp};function Fr(e,t,n,{o:{insert:r},m:s},i=2){i===0&&r(e.targetAnchor,t,n);const{el:o,anchor:l,shapeFlag:a,children:c,props:u}=e,f=i===2;if(f&&r(o,t,n),(!f||nr(u))&&a&16)for(let d=0;d<c.length;d++)s(c[d],t,n,2);f&&r(l,t,n)}function cp(e,t,n,r,s,i,{o:{nextSibling:o,parentNode:l,querySelector:a}},c){const u=t.target=_i(t.props,a);if(u){const f=u._lpa||u.firstChild;if(t.shapeFlag&16)if(nr(t.props))t.anchor=c(o(e),t,l(e),n,r,s,i),t.targetAnchor=f;else{t.anchor=o(e);let d=f;for(;d;)if(d=o(d),d&&d.nodeType===8&&d.data==="teleport anchor"){t.targetAnchor=d,u._lpa=t.targetAnchor&&o(t.targetAnchor);break}c(f,t,u,n,r,s,i)}Vc(t)}return t.anchor&&o(t.anchor)}const up=lp;function Vc(e){const t=e.ctx;if(t&&t.ut){let n=e.children[0].el;for(;n!==e.targetAnchor;)n.nodeType===1&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}const Ge=Symbol.for("v-fgt"),Sr=Symbol.for("v-txt"),it=Symbol.for("v-cmt"),js=Symbol.for("v-stc"),rr=[];let lt=null;function Wc(e=!1){rr.push(lt=e?null:[])}function fp(){rr.pop(),lt=rr[rr.length-1]||null}let hr=1;function ia(e){hr+=e}function zc(e){return e.dynamicChildren=hr>0?lt||Ln:null,fp(),hr>0&&lt&&lt.push(e),e}function hE(e,t,n,r,s,i){return zc(Yc(e,t,n,r,s,i,!0))}function Gc(e,t,n,r,s){return zc(ee(e,t,n,r,s,!0))}function es(e){return e?e.__v_isVNode===!0:!1}function ln(e,t){return e.type===t.type&&e.key===t.key}const vs="__vInternal",Kc=({key:e})=>e!=null?e:null,Vr=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Pe(e)||Ee(e)||se(e)?{i:ke,r:e,k:t,f:!!n}:e:null);function Yc(e,t=null,n=null,r=0,s=null,i=e===Ge?0:1,o=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Kc(t),ref:t&&Vr(t),scopeId:ps,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:s,dynamicChildren:null,appContext:null,ctx:ke};return l?(ho(a,n),i&128&&e.normalize(a)):n&&(a.shapeFlag|=Pe(n)?8:16),hr>0&&!o&&lt&&(a.patchFlag>0||i&6)&&a.patchFlag!==32&&lt.push(a),a}const ee=dp;function dp(e,t=null,n=null,r=0,s=null,i=!1){if((!e||e===Ud)&&(e=it),es(e)){const l=Kt(e,t,!0);return n&&ho(l,n),hr>0&&!i&&lt&&(l.shapeFlag&6?lt[lt.indexOf(e)]=l:lt.push(l)),l.patchFlag|=-2,l}if(Sp(e)&&(e=e.__vccOpts),t){t=pp(t);let{class:l,style:a}=t;l&&!Pe(l)&&(t.class=Qi(l)),ye(a)&&(dc(a)&&!Z(a)&&(a=Ie({},a)),t.style=Ji(a))}const o=Pe(e)?1:Id(e)?128:ap(e)?64:ye(e)?4:se(e)?2:0;return Yc(e,t,n,r,s,o,i,!0)}function pp(e){return e?dc(e)||vs in e?Ie({},e):e:null}function Kt(e,t,n=!1){const{props:r,ref:s,patchFlag:i,children:o}=e,l=t?Tr(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&Kc(l),ref:t&&t.ref?n&&s?Z(s)?s.concat(Vr(t)):[s,Vr(t)]:Vr(t):s,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ge?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Kt(e.ssContent),ssFallback:e.ssFallback&&Kt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function mp(e=" ",t=0){return ee(Sr,null,e,t)}function gE(e="",t=!1){return t?(Wc(),Gc(it,null,e)):ee(it,null,e)}function ht(e){return e==null||typeof e=="boolean"?ee(it):Z(e)?ee(Ge,null,e.slice()):typeof e=="object"?Ht(e):ee(Sr,null,String(e))}function Ht(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Kt(e)}function ho(e,t){let n=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(Z(t))n=16;else if(typeof t=="object")if(r&65){const s=t.default;s&&(s._c&&(s._d=!1),ho(e,s()),s._c&&(s._d=!0));return}else{n=32;const s=t._;!s&&!(vs in t)?t._ctx=ke:s===3&&ke&&(ke.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else se(t)?(t={default:t,_ctx:ke},n=32):(t=String(t),r&64?(n=16,t=[mp(t)]):n=8);e.children=t,e.shapeFlag|=n}function Tr(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const s in r)if(s==="class")t.class!==r.class&&(t.class=Qi([t.class,r.class]));else if(s==="style")t.style=Ji([t.style,r.style]);else if(is(s)){const i=t[s],o=r[s];o&&i!==o&&!(Z(i)&&i.includes(o))&&(t[s]=i?[].concat(i,o):o)}else s!==""&&(t[s]=r[s])}return t}function pt(e,t,n,r=null){st(e,t,7,[n,r])}const hp=Dc();let gp=0;function vp(e,t,n){const r=e.type,s=(t?t.appContext:e.appContext)||hp,i={uid:gp++,vnode:e,type:r,parent:t,appContext:s,root:null,next:null,subTree:null,effect:null,update:null,scope:new Ql(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(s.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:$c(r,s),emitsOptions:Tc(r,s),emit:null,emitted:null,propsDefaults:_e,inheritAttrs:r.inheritAttrs,ctx:_e,data:_e,props:_e,attrs:_e,slots:_e,refs:_e,setupState:_e,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Cd.bind(null,i),e.ce&&e.ce(i),i}let Ne=null;const Yt=()=>Ne||ke;let go,wn,oa="__VUE_INSTANCE_SETTERS__";(wn=li()[oa])||(wn=li()[oa]=[]),wn.push(e=>Ne=e),go=e=>{wn.length>1?wn.forEach(t=>t(e)):wn[0](e)};const Rn=e=>{go(e),e.scope.on()},mn=()=>{Ne&&Ne.scope.off(),go(null)};function qc(e){return e.vnode.shapeFlag&4}let gr=!1;function bp(e,t=!1){gr=t;const{props:n,children:r}=e.vnode,s=qc(e);Zd(e,n,s,t),np(e,r);const i=s?yp(e,t):void 0;return gr=!1,i}function yp(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=us(new Proxy(e.ctx,Wd));const{setup:r}=n;if(r){const s=e.setupContext=r.length>1?Ep(e):null;Rn(e),$n();const i=zt(r,e,0,[e.props,s]);if(jn(),mn(),Kl(i)){if(i.then(mn,mn),t)return i.then(o=>{aa(e,o,t)}).catch(o=>{fs(o,e,0)});e.asyncDep=i}else aa(e,i,t)}else Xc(e,t)}function aa(e,t,n){se(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ye(t)&&(e.setupState=vc(t)),Xc(e,n)}let la;function Xc(e,t,n){const r=e.type;if(!e.render){if(!t&&la&&!r.render){const s=r.template||fo(e).template;if(s){const{isCustomElement:i,compilerOptions:o}=e.appContext.config,{delimiters:l,compilerOptions:a}=r,c=Ie(Ie({isCustomElement:i,delimiters:l},o),a);r.render=la(s,c)}}e.render=r.render||ct}Rn(e),$n(),zd(e),jn(),mn()}function _p(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return Ke(e,"get","$attrs"),t[n]}}))}function Ep(e){const t=n=>{e.exposed=n||{}};return{get attrs(){return _p(e)},slots:e.slots,emit:e.emit,expose:t}}function bs(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(vc(us(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in tr)return tr[n](e)},has(t,n){return n in t||n in tr}}))}function wp(e,t=!0){return se(e)?e.displayName||e.name:e.name||t&&e.__name}function Sp(e){return se(e)&&"__vccOpts"in e}const fe=(e,t)=>yd(e,t,gr);function $e(e,t,n){const r=arguments.length;return r===2?ye(t)&&!Z(t)?es(t)?ee(e,null,[t]):ee(e,t):ee(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&es(n)&&(n=[n]),ee(e,t,n))}const Tp=Symbol.for("v-scx"),Cp=()=>Ze(Tp),Op="3.3.4",xp="http://www.w3.org/2000/svg",cn=typeof document<"u"?document:null,ca=cn&&cn.createElement("template"),Pp={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const s=t?cn.createElementNS(xp,e):cn.createElement(e,n?{is:n}:void 0);return e==="select"&&r&&r.multiple!=null&&s.setAttribute("multiple",r.multiple),s},createText:e=>cn.createTextNode(e),createComment:e=>cn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>cn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,r,s,i){const o=n?n.previousSibling:t.lastChild;if(s&&(s===i||s.nextSibling))for(;t.insertBefore(s.cloneNode(!0),n),!(s===i||!(s=s.nextSibling)););else{ca.innerHTML=r?"<svg>".concat(e,"</svg>"):e;const l=ca.content;if(r){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function Lp(e,t,n){const r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function Ap(e,t,n){const r=e.style,s=Pe(n);if(n&&!s){if(t&&!Pe(t))for(const i in t)n[i]==null&&Ei(r,i,"");for(const i in n)Ei(r,i,n[i])}else{const i=r.display;s?t!==n&&(r.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(r.display=i)}}const ua=/\s*!important$/;function Ei(e,t,n){if(Z(n))n.forEach(r=>Ei(e,t,r));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=Ip(e,t);ua.test(n)?e.setProperty(Bn(r),n.replace(ua,""),"important"):e[r]=n}}const fa=["Webkit","Moz","ms"],Hs={};function Ip(e,t){const n=Hs[t];if(n)return n;let r=Et(t);if(r!=="filter"&&r in e)return Hs[t]=r;r=ls(r);for(let s=0;s<fa.length;s++){const i=fa[s]+r;if(i in e)return Hs[t]=i}return t}const da="http://www.w3.org/1999/xlink";function Np(e,t,n,r,s){if(r&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(da,t.slice(6,t.length)):e.setAttributeNS(da,t,n);else{const i=Df(t);n==null||i&&!Xl(n)?e.removeAttribute(t):e.setAttribute(t,i?"":n)}}function Rp(e,t,n,r,s,i,o){if(t==="innerHTML"||t==="textContent"){r&&o(r,s,i),e[t]=n==null?"":n;return}const l=e.tagName;if(t==="value"&&l!=="PROGRESS"&&!l.includes("-")){e._value=n;const c=l==="OPTION"?e.getAttribute("value"):e.value,u=n==null?"":n;c!==u&&(e.value=u),n==null&&e.removeAttribute(t);return}let a=!1;if(n===""||n==null){const c=typeof e[t];c==="boolean"?n=Xl(n):n==null&&c==="string"?(n="",a=!0):c==="number"&&(n=0,a=!0)}try{e[t]=n}catch(c){}a&&e.removeAttribute(t)}function Cn(e,t,n,r){e.addEventListener(t,n,r)}function Mp(e,t,n,r){e.removeEventListener(t,n,r)}function Fp(e,t,n,r,s=null){const i=e._vei||(e._vei={}),o=i[t];if(r&&o)o.value=r;else{const[l,a]=kp(t);if(r){const c=i[t]=$p(r,s);Cn(e,l,c,a)}else o&&(Mp(e,l,o,a),i[t]=void 0)}}const pa=/(?:Once|Passive|Capture)$/;function kp(e){let t;if(pa.test(e)){t={};let r;for(;r=e.match(pa);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Bn(e.slice(2)),t]}let Us=0;const Dp=Promise.resolve(),Bp=()=>Us||(Dp.then(()=>Us=0),Us=Date.now());function $p(e,t){const n=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=n.attached)return;st(jp(r,n.value),t,5,[r])};return n.value=e,n.attached=Bp(),n}function jp(e,t){if(Z(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(r=>s=>!s._stopped&&r&&r(s))}else return t}const ma=/^on[a-z]/,Hp=(e,t,n,r,s=!1,i,o,l,a)=>{t==="class"?Lp(e,r,s):t==="style"?Ap(e,n,r):is(t)?Ki(t)||Fp(e,t,n,r,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Up(e,t,r,s))?Rp(e,t,r,i,o,l,a):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),Np(e,t,r,s))};function Up(e,t,n,r){return r?!!(t==="innerHTML"||t==="textContent"||t in e&&ma.test(t)&&se(n)):t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA"||ma.test(t)&&Pe(n)?!1:t in e}const Ft="transition",Wn="animation",ys=(e,{slots:t})=>$e(kd,Vp(e),t);ys.displayName="Transition";const Jc={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};ys.props=Ie({},Pc,Jc);const sn=(e,t=[])=>{Z(e)?e.forEach(n=>n(...t)):e&&e(...t)},ha=e=>e?Z(e)?e.some(t=>t.length>1):e.length>1:!1;function Vp(e){const t={};for(const A in e)A in Jc||(t[A]=e[A]);if(e.css===!1)return t;const{name:n="v",type:r,duration:s,enterFromClass:i="".concat(n,"-enter-from"),enterActiveClass:o="".concat(n,"-enter-active"),enterToClass:l="".concat(n,"-enter-to"),appearFromClass:a=i,appearActiveClass:c=o,appearToClass:u=l,leaveFromClass:f="".concat(n,"-leave-from"),leaveActiveClass:d="".concat(n,"-leave-active"),leaveToClass:h="".concat(n,"-leave-to")}=e,v=Wp(s),b=v&&v[0],C=v&&v[1],{onBeforeEnter:g,onEnter:w,onEnterCancelled:T,onLeave:E,onLeaveCancelled:S,onBeforeAppear:N=g,onAppear:M=w,onAppearCancelled:F=T}=t,x=(A,H,J)=>{on(A,H?u:l),on(A,H?c:o),J&&J()},W=(A,H)=>{A._isLeaving=!1,on(A,f),on(A,h),on(A,d),H&&H()},P=A=>(H,J)=>{const re=A?M:w,X=()=>x(H,A,J);sn(re,[H,X]),ga(()=>{on(H,A?a:i),kt(H,A?u:l),ha(re)||va(H,r,b,X)})};return Ie(t,{onBeforeEnter(A){sn(g,[A]),kt(A,i),kt(A,o)},onBeforeAppear(A){sn(N,[A]),kt(A,a),kt(A,c)},onEnter:P(!1),onAppear:P(!0),onLeave(A,H){A._isLeaving=!0;const J=()=>W(A,H);kt(A,f),Kp(),kt(A,d),ga(()=>{A._isLeaving&&(on(A,f),kt(A,h),ha(E)||va(A,r,C,J))}),sn(E,[A,J])},onEnterCancelled(A){x(A,!1),sn(T,[A])},onAppearCancelled(A){x(A,!0),sn(F,[A])},onLeaveCancelled(A){W(A),sn(S,[A])}})}function Wp(e){if(e==null)return null;if(ye(e))return[Vs(e.enter),Vs(e.leave)];{const t=Vs(e);return[t,t]}}function Vs(e){return If(e)}function kt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e._vtc||(e._vtc=new Set)).add(t)}function on(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function ga(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let zp=0;function va(e,t,n,r){const s=e._endId=++zp,i=()=>{s===e._endId&&r()};if(n)return setTimeout(i,n);const{type:o,timeout:l,propCount:a}=Gp(e,t);if(!o)return r();const c=o+"end";let u=0;const f=()=>{e.removeEventListener(c,d),i()},d=h=>{h.target===e&&++u>=a&&f()};setTimeout(()=>{u<a&&f()},l+1),e.addEventListener(c,d)}function Gp(e,t){const n=window.getComputedStyle(e),r=v=>(n[v]||"").split(", "),s=r("".concat(Ft,"Delay")),i=r("".concat(Ft,"Duration")),o=ba(s,i),l=r("".concat(Wn,"Delay")),a=r("".concat(Wn,"Duration")),c=ba(l,a);let u=null,f=0,d=0;t===Ft?o>0&&(u=Ft,f=o,d=i.length):t===Wn?c>0&&(u=Wn,f=c,d=a.length):(f=Math.max(o,c),u=f>0?o>c?Ft:Wn:null,d=u?u===Ft?i.length:a.length:0);const h=u===Ft&&/\b(transform|all)(,|$)/.test(r("".concat(Ft,"Property")).toString());return{type:u,timeout:f,propCount:d,hasTransform:h}}function ba(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,r)=>ya(n)+ya(e[r])))}function ya(e){return Number(e.slice(0,-1).replace(",","."))*1e3}function Kp(){return document.body.offsetHeight}const _a=e=>{const t=e.props["onUpdate:modelValue"]||!1;return Z(t)?n=>Ur(t,n):t};function Yp(e){e.target.composing=!0}function Ea(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const vE={created(e,{modifiers:{lazy:t,trim:n,number:r}},s){e._assign=_a(s);const i=r||s.props&&s.props.type==="number";Cn(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;n&&(l=l.trim()),i&&(l=ai(l)),e._assign(l)}),n&&Cn(e,"change",()=>{e.value=e.value.trim()}),t||(Cn(e,"compositionstart",Yp),Cn(e,"compositionend",Ea),Cn(e,"change",Ea))},mounted(e,{value:t}){e.value=t==null?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:r,number:s}},i){if(e._assign=_a(i),e.composing||document.activeElement===e&&e.type!=="range"&&(n||r&&e.value.trim()===t||(s||e.type==="number")&&ai(e.value)===t))return;const o=t==null?"":t;e.value!==o&&(e.value=o)}},qp=["ctrl","shift","alt","meta"],Xp={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>qp.some(n=>e["".concat(n,"Key")]&&!t.includes(n))},bE=(e,t)=>(n,...r)=>{for(let s=0;s<t.length;s++){const i=Xp[t[s]];if(i&&i(n,t))return}return e(n,...r)},Qc={beforeMount(e,{value:t},{transition:n}){e._vod=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):zn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!=!n&&(r?t?(r.beforeEnter(e),zn(e,!0),r.enter(e)):r.leave(e,()=>{zn(e,!1)}):zn(e,t))},beforeUnmount(e,{value:t}){zn(e,t)}};function zn(e,t){e.style.display=t?e._vod:"none"}const Jp=Ie({patchProp:Hp},Pp);let wa;function Qp(){return wa||(wa=sp(Jp))}const Zp=(...e)=>{const t=Qp().createApp(...e),{mount:n}=t;return t.mount=r=>{const s=em(r);if(!s)return;const i=t._component;!se(i)&&!i.render&&!i.template&&(i.template=s.innerHTML),s.innerHTML="";const o=n(s,!1,s instanceof SVGElement);return s instanceof Element&&(s.removeAttribute("v-cloak"),s.setAttribute("data-v-app","")),o},t};function em(e){return Pe(e)?document.querySelector(e):e}var _s=function(){return typeof document<"u"&&typeof window<"u"},tm=function(t,n){return new Promise(function(r,s){var i=document.head||document.getElementsByTagName("head")[0],o=document.createElement("script");if(o.async=!0,o.src=t,o.charset="utf-8",n){var l=document.createElement("link");l.href=n,l.rel="preconnect",i.appendChild(l)}i.appendChild(o),o.onload=r,o.onerror=s})},nm=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};Object.keys(n).forEach(function(r){t[r]=n[r]})},Zc=It({property:null,isEnabled:!0,disableScriptLoader:!1,useDebugger:!1,globalObjectName:"gtag",dataLayerName:"dataLayer",resourceURL:"https://www.googletagmanager.com/gtag/js",preconnectOrigin:"https://www.googletagmanager.com",customResource:null,appName:null,appId:null,appVersion:null}),Qt=function(){return bc(Zc)},eu=fe(function(){var e=Qt(),t=e.property;if(t.value)return Array.isArray(t.value)?t.value.find(function(n){return n.default===!0})||t.value[0]:t.value}),rm=fe(function(){var e=Qt(),t=e.property;return!!(t.value&&t.value.id!==null)}),vr=fe(function(){var e=Qt(),t=e.property;return Array.isArray(t.value)?t.value:[t.value]}),sm=fe(function(){var e=Qt(),t=e.isEnabled,n=eu.value;return!!(n&&n.id&&t.value)}),Cr=function(){var e;if(_s()){for(var t=Qt(),n=t.globalObjectName,r=t.useDebugger,s=arguments.length,i=new Array(s),o=0;o<s;o++)i[o]=arguments[o];r.value&&console.warn("[vue-gtag] Debugger:",i),(e=window)[n.value].apply(e,i)}},vo=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];vr.value.forEach(function(r){Cr.apply(void 0,["config",r.id].concat(t))})},im=function(e){vo({custom_map:e})},om=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;_s()&&vr.value.forEach(function(t){window["ga-disable-".concat(t.id)]=e})},vn=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=Object.assign({},t);!n.send_to&&vr.value.length>1&&(n.send_to=vr.value.map(function(r){return r.id})),Cr("event",e,n)},am=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];vn.apply(void 0,["exception"].concat(t))},lm=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];vo.apply(void 0,["linker"].concat(t))},cm=function(e){var t={};typeof e=="string"?t={page_path:e,page_location:window.location.href}:t=e,typeof t.send_page_view>"u"&&(t.send_page_view=!0),vn("page_view",t)},um=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];vn.apply(void 0,["purchase"].concat(t))},fm=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];vn.apply(void 0,["refund"].concat(t))},dm=function(){for(var e=Qt(),t=e.appName,n=e.appId,r=e.appVersion,s=arguments.length,i=new Array(s),o=0;o<s;o++)i[o]=arguments[o];var l=i[0],a={};typeof l=="string"?a={screen_name:l}:a=l,a.app_name==null&&t.value!=null&&(a.app_name=t.value),a.app_id==null&&n.value!=null&&(a.app_id=n.value),a.app_version==null&&r.value!=null&&(a.app_version=r.value),vn("screen_view",a)},pm=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];Cr.apply(void 0,["set"].concat(t))},mm=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];vn.apply(void 0,["timing_complete"].concat(t))},tu=Object.freeze({__proto__:null,config:vo,customMap:im,disable:om,event:vn,exception:am,linker:lm,pageview:cm,purchase:um,query:Cr,refund:fm,screenview:dm,set:pm,time:mm}),Sa=Q(!1),Ta=Q(!1),hm=function(){var t=Qt(),n=t.disableScriptLoader,r=t.preconnectOrigin,s=t.resourceURL,i=t.dataLayerName;if(!(!_s()||!rm.value||Ta.value)){if(Ta.value=!0,vr.value.forEach(function(l){var a=Object.assign({send_page_view:!1},l.params);Cr("config",l.id,a)}),n.value){Sa.value=!0;return}var o="".concat(s.value,"?id=").concat(eu.value.id,"&l=").concat(i.value);tm(o,r.value).then(function(){Sa.value=!0})}},gm=function(){De(function(){return sm.value},function(t){return t&&hm()},{immediate:!0})},vm=function(){if(_s()){var e=Qt(),t=e.globalObjectName,n=e.dataLayerName;window[t.value]==null&&(window[n.value]=window[n.value]||[],window[t.value]=function(){window[n.value].push(arguments)}),window[t.value]("js",new Date)}},yE=function(){return tu};It({template:null,useScreenview:!1,skipSamePath:!0});var _E={install:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};nm(Zc,n),vm(),gm(),t.config.globalProperties.$gtag=tu}};function nu(e,t){return function(){return e.apply(t,arguments)}}const{toString:bm}=Object.prototype,{getPrototypeOf:bo}=Object,Es=(e=>t=>{const n=bm.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),wt=e=>(e=e.toLowerCase(),t=>Es(t)===e),ws=e=>t=>typeof t===e,{isArray:Un}=Array,br=ws("undefined");function ym(e){return e!==null&&!br(e)&&e.constructor!==null&&!br(e.constructor)&&ot(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const ru=wt("ArrayBuffer");function _m(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&ru(e.buffer),t}const Em=ws("string"),ot=ws("function"),su=ws("number"),Ss=e=>e!==null&&typeof e=="object",wm=e=>e===!0||e===!1,Wr=e=>{if(Es(e)!=="object")return!1;const t=bo(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Sm=wt("Date"),Tm=wt("File"),Cm=wt("Blob"),Om=wt("FileList"),xm=e=>Ss(e)&&ot(e.pipe),Pm=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ot(e.append)&&((t=Es(e))==="formdata"||t==="object"&&ot(e.toString)&&e.toString()==="[object FormData]"))},Lm=wt("URLSearchParams"),Am=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Or(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),Un(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let l;for(r=0;r<o;r++)l=i[r],t.call(null,e[l],l,e)}}function iu(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const ou=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),au=e=>!br(e)&&e!==ou;function wi(){const{caseless:e}=au(this)&&this||{},t={},n=(r,s)=>{const i=e&&iu(t,s)||s;Wr(t[i])&&Wr(r)?t[i]=wi(t[i],r):Wr(r)?t[i]=wi({},r):Un(r)?t[i]=r.slice():t[i]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&Or(arguments[r],n);return t}const Im=(e,t,n,{allOwnKeys:r}={})=>(Or(t,(s,i)=>{n&&ot(s)?e[i]=nu(s,n):e[i]=s},{allOwnKeys:r}),e),Nm=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Rm=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Mm=(e,t,n,r)=>{let s,i,o;const l={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),i=s.length;i-- >0;)o=s[i],(!r||r(o,e,t))&&!l[o]&&(t[o]=e[o],l[o]=!0);e=n!==!1&&bo(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Fm=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},km=e=>{if(!e)return null;if(Un(e))return e;let t=e.length;if(!su(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Dm=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&bo(Uint8Array)),Bm=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let s;for(;(s=r.next())&&!s.done;){const i=s.value;t.call(e,i[0],i[1])}},$m=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},jm=wt("HTMLFormElement"),Hm=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Ca=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Um=wt("RegExp"),lu=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Or(n,(s,i)=>{t(s,i,e)!==!1&&(r[i]=s)}),Object.defineProperties(e,r)},Vm=e=>{lu(e,(t,n)=>{if(ot(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(ot(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Wm=(e,t)=>{const n={},r=s=>{s.forEach(i=>{n[i]=!0})};return Un(e)?r(e):r(String(e).split(t)),n},zm=()=>{},Gm=(e,t)=>(e=+e,Number.isFinite(e)?e:t),Ws="abcdefghijklmnopqrstuvwxyz",Oa="0123456789",cu={DIGIT:Oa,ALPHA:Ws,ALPHA_DIGIT:Ws+Ws.toUpperCase()+Oa},Km=(e=16,t=cu.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n};function Ym(e){return!!(e&&ot(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const qm=e=>{const t=new Array(10),n=(r,s)=>{if(Ss(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const i=Un(r)?[]:{};return Or(r,(o,l)=>{const a=n(o,s+1);!br(a)&&(i[l]=a)}),t[s]=void 0,i}}return r};return n(e,0)},Xm=wt("AsyncFunction"),Jm=e=>e&&(Ss(e)||ot(e))&&ot(e.then)&&ot(e.catch),D={isArray:Un,isArrayBuffer:ru,isBuffer:ym,isFormData:Pm,isArrayBufferView:_m,isString:Em,isNumber:su,isBoolean:wm,isObject:Ss,isPlainObject:Wr,isUndefined:br,isDate:Sm,isFile:Tm,isBlob:Cm,isRegExp:Um,isFunction:ot,isStream:xm,isURLSearchParams:Lm,isTypedArray:Dm,isFileList:Om,forEach:Or,merge:wi,extend:Im,trim:Am,stripBOM:Nm,inherits:Rm,toFlatObject:Mm,kindOf:Es,kindOfTest:wt,endsWith:Fm,toArray:km,forEachEntry:Bm,matchAll:$m,isHTMLForm:jm,hasOwnProperty:Ca,hasOwnProp:Ca,reduceDescriptors:lu,freezeMethods:Vm,toObjectSet:Wm,toCamelCase:Hm,noop:zm,toFiniteNumber:Gm,findKey:iu,global:ou,isContextDefined:au,ALPHABET:cu,generateString:Km,isSpecCompliantForm:Ym,toJSONObject:qm,isAsyncFn:Xm,isThenable:Jm};function pe(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s)}D.inherits(pe,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:D.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const uu=pe.prototype,fu={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{fu[e]={value:e}});Object.defineProperties(pe,fu);Object.defineProperty(uu,"isAxiosError",{value:!0});pe.from=(e,t,n,r,s,i)=>{const o=Object.create(uu);return D.toFlatObject(e,o,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),pe.call(o,e.message,t,n,r,s),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const Qm=null;function Si(e){return D.isPlainObject(e)||D.isArray(e)}function du(e){return D.endsWith(e,"[]")?e.slice(0,-2):e}function xa(e,t,n){return e?e.concat(t).map(function(s,i){return s=du(s),!n&&i?"["+s+"]":s}).join(n?".":""):t}function Zm(e){return D.isArray(e)&&!e.some(Si)}const eh=D.toFlatObject(D,{},null,function(t){return/^is[A-Z]/.test(t)});function Ts(e,t,n){if(!D.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=D.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(b,C){return!D.isUndefined(C[b])});const r=n.metaTokens,s=n.visitor||u,i=n.dots,o=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&D.isSpecCompliantForm(t);if(!D.isFunction(s))throw new TypeError("visitor must be a function");function c(v){if(v===null)return"";if(D.isDate(v))return v.toISOString();if(!a&&D.isBlob(v))throw new pe("Blob is not supported. Use a Buffer instead.");return D.isArrayBuffer(v)||D.isTypedArray(v)?a&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function u(v,b,C){let g=v;if(v&&!C&&typeof v=="object"){if(D.endsWith(b,"{}"))b=r?b:b.slice(0,-2),v=JSON.stringify(v);else if(D.isArray(v)&&Zm(v)||(D.isFileList(v)||D.endsWith(b,"[]"))&&(g=D.toArray(v)))return b=du(b),g.forEach(function(T,E){!(D.isUndefined(T)||T===null)&&t.append(o===!0?xa([b],E,i):o===null?b:b+"[]",c(T))}),!1}return Si(v)?!0:(t.append(xa(C,b,i),c(v)),!1)}const f=[],d=Object.assign(eh,{defaultVisitor:u,convertValue:c,isVisitable:Si});function h(v,b){if(!D.isUndefined(v)){if(f.indexOf(v)!==-1)throw Error("Circular reference detected in "+b.join("."));f.push(v),D.forEach(v,function(g,w){(!(D.isUndefined(g)||g===null)&&s.call(t,g,D.isString(w)?w.trim():w,b,d))===!0&&h(g,b?b.concat(w):[w])}),f.pop()}}if(!D.isObject(e))throw new TypeError("data must be an object");return h(e),t}function Pa(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function yo(e,t){this._pairs=[],e&&Ts(e,this,t)}const pu=yo.prototype;pu.append=function(t,n){this._pairs.push([t,n])};pu.toString=function(t){const n=t?function(r){return t.call(this,r,Pa)}:Pa;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function th(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function mu(e,t,n){if(!t)return e;const r=n&&n.encode||th,s=n&&n.serialize;let i;if(s?i=s(t,n):i=D.isURLSearchParams(t)?t.toString():new yo(t,n).toString(r),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class nh{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){D.forEach(this.handlers,function(r){r!==null&&t(r)})}}const La=nh,hu={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},rh=typeof URLSearchParams<"u"?URLSearchParams:yo,sh=typeof FormData<"u"?FormData:null,ih=typeof Blob<"u"?Blob:null,oh=(()=>{let e;return typeof navigator<"u"&&((e=navigator.product)==="ReactNative"||e==="NativeScript"||e==="NS")?!1:typeof window<"u"&&typeof document<"u"})(),ah=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),bt={isBrowser:!0,classes:{URLSearchParams:rh,FormData:sh,Blob:ih},isStandardBrowserEnv:oh,isStandardBrowserWebWorkerEnv:ah,protocols:["http","https","file","blob","url","data"]};function lh(e,t){return Ts(e,new bt.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,i){return bt.isNode&&D.isBuffer(n)?(this.append(r,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function ch(e){return D.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function uh(e){const t={},n=Object.keys(e);let r;const s=n.length;let i;for(r=0;r<s;r++)i=n[r],t[i]=e[i];return t}function gu(e){function t(n,r,s,i){let o=n[i++];const l=Number.isFinite(+o),a=i>=n.length;return o=!o&&D.isArray(s)?s.length:o,a?(D.hasOwnProp(s,o)?s[o]=[s[o],r]:s[o]=r,!l):((!s[o]||!D.isObject(s[o]))&&(s[o]=[]),t(n,r,s[o],i)&&D.isArray(s[o])&&(s[o]=uh(s[o])),!l)}if(D.isFormData(e)&&D.isFunction(e.entries)){const n={};return D.forEachEntry(e,(r,s)=>{t(ch(r),s,n,0)}),n}return null}const fh={"Content-Type":void 0};function dh(e,t,n){if(D.isString(e))try{return(t||JSON.parse)(e),D.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Cs={transitional:hu,adapter:["xhr","http"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,i=D.isObject(t);if(i&&D.isHTMLForm(t)&&(t=new FormData(t)),D.isFormData(t))return s&&s?JSON.stringify(gu(t)):t;if(D.isArrayBuffer(t)||D.isBuffer(t)||D.isStream(t)||D.isFile(t)||D.isBlob(t))return t;if(D.isArrayBufferView(t))return t.buffer;if(D.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(i){if(r.indexOf("application/x-www-form-urlencoded")>-1)return lh(t,this.formSerializer).toString();if((l=D.isFileList(t))||r.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return Ts(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return i||s?(n.setContentType("application/json",!1),dh(t)):t}],transformResponse:[function(t){const n=this.transitional||Cs.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(t&&D.isString(t)&&(r&&!this.responseType||s)){const o=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(l){if(o)throw l.name==="SyntaxError"?pe.from(l,pe.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:bt.classes.FormData,Blob:bt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};D.forEach(["delete","get","head"],function(t){Cs.headers[t]={}});D.forEach(["post","put","patch"],function(t){Cs.headers[t]=D.merge(fh)});const _o=Cs,ph=D.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),mh=e=>{const t={};let n,r,s;return e&&e.split("\n").forEach(function(o){s=o.indexOf(":"),n=o.substring(0,s).trim().toLowerCase(),r=o.substring(s+1).trim(),!(!n||t[n]&&ph[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Aa=Symbol("internals");function Gn(e){return e&&String(e).trim().toLowerCase()}function zr(e){return e===!1||e==null?e:D.isArray(e)?e.map(zr):String(e)}function hh(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const gh=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function zs(e,t,n,r,s){if(D.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!D.isString(t)){if(D.isString(r))return t.indexOf(r)!==-1;if(D.isRegExp(r))return r.test(t)}}function vh(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function bh(e,t){const n=D.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,i,o){return this[r].call(this,t,s,i,o)},configurable:!0})})}class Os{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function i(l,a,c){const u=Gn(a);if(!u)throw new Error("header name must be a non-empty string");const f=D.findKey(s,u);(!f||s[f]===void 0||c===!0||c===void 0&&s[f]!==!1)&&(s[f||a]=zr(l))}const o=(l,a)=>D.forEach(l,(c,u)=>i(c,u,a));return D.isPlainObject(t)||t instanceof this.constructor?o(t,n):D.isString(t)&&(t=t.trim())&&!gh(t)?o(mh(t),n):t!=null&&i(n,t,r),this}get(t,n){if(t=Gn(t),t){const r=D.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return hh(s);if(D.isFunction(n))return n.call(this,s,r);if(D.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Gn(t),t){const r=D.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||zs(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function i(o){if(o=Gn(o),o){const l=D.findKey(r,o);l&&(!n||zs(r,r[l],l,n))&&(delete r[l],s=!0)}}return D.isArray(t)?t.forEach(i):i(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const i=n[r];(!t||zs(this,this[i],i,t,!0))&&(delete this[i],s=!0)}return s}normalize(t){const n=this,r={};return D.forEach(this,(s,i)=>{const o=D.findKey(r,i);if(o){n[o]=zr(s),delete n[i];return}const l=t?vh(i):String(i).trim();l!==i&&delete n[i],n[l]=zr(s),r[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return D.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&D.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Aa]=this[Aa]={accessors:{}}).accessors,s=this.prototype;function i(o){const l=Gn(o);r[l]||(bh(s,o),r[l]=!0)}return D.isArray(t)?t.forEach(i):i(t),this}}Os.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);D.freezeMethods(Os.prototype);D.freezeMethods(Os);const Lt=Os;function Gs(e,t){const n=this||_o,r=t||n,s=Lt.from(r.headers);let i=r.data;return D.forEach(e,function(l){i=l.call(n,i,s.normalize(),t?t.status:void 0)}),s.normalize(),i}function vu(e){return!!(e&&e.__CANCEL__)}function xr(e,t,n){pe.call(this,e==null?"canceled":e,pe.ERR_CANCELED,t,n),this.name="CanceledError"}D.inherits(xr,pe,{__CANCEL__:!0});function yh(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new pe("Request failed with status code "+n.status,[pe.ERR_BAD_REQUEST,pe.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}const _h=bt.isStandardBrowserEnv?function(){return{write:function(n,r,s,i,o,l){const a=[];a.push(n+"="+encodeURIComponent(r)),D.isNumber(s)&&a.push("expires="+new Date(s).toGMTString()),D.isString(i)&&a.push("path="+i),D.isString(o)&&a.push("domain="+o),l===!0&&a.push("secure"),document.cookie=a.join("; ")},read:function(n){const r=document.cookie.match(new RegExp("(^|;\\s*)("+n+")=([^;]*)"));return r?decodeURIComponent(r[3]):null},remove:function(n){this.write(n,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function Eh(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function wh(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function bu(e,t){return e&&!Eh(t)?wh(e,t):t}const Sh=bt.isStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");let r;function s(i){let o=i;return t&&(n.setAttribute("href",o),o=n.href),n.setAttribute("href",o),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:n.pathname.charAt(0)==="/"?n.pathname:"/"+n.pathname}}return r=s(window.location.href),function(o){const l=D.isString(o)?s(o):o;return l.protocol===r.protocol&&l.host===r.host}}():function(){return function(){return!0}}();function Th(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Ch(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,i=0,o;return t=t!==void 0?t:1e3,function(a){const c=Date.now(),u=r[i];o||(o=c),n[s]=a,r[s]=c;let f=i,d=0;for(;f!==s;)d+=n[f++],f=f%e;if(s=(s+1)%e,s===i&&(i=(i+1)%e),c-o<t)return;const h=u&&c-u;return h?Math.round(d*1e3/h):void 0}}function Ia(e,t){let n=0;const r=Ch(50,250);return s=>{const i=s.loaded,o=s.lengthComputable?s.total:void 0,l=i-n,a=r(l),c=i<=o;n=i;const u={loaded:i,total:o,progress:o?i/o:void 0,bytes:l,rate:a||void 0,estimated:a&&o&&c?(o-i)/a:void 0,event:s};u[t?"download":"upload"]=!0,e(u)}}const Oh=typeof XMLHttpRequest<"u",xh=Oh&&function(e){return new Promise(function(n,r){let s=e.data;const i=Lt.from(e.headers).normalize(),o=e.responseType;let l;function a(){e.cancelToken&&e.cancelToken.unsubscribe(l),e.signal&&e.signal.removeEventListener("abort",l)}D.isFormData(s)&&(bt.isStandardBrowserEnv||bt.isStandardBrowserWebWorkerEnv?i.setContentType(!1):i.setContentType("multipart/form-data;",!1));let c=new XMLHttpRequest;if(e.auth){const h=e.auth.username||"",v=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";i.set("Authorization","Basic "+btoa(h+":"+v))}const u=bu(e.baseURL,e.url);c.open(e.method.toUpperCase(),mu(u,e.params,e.paramsSerializer),!0),c.timeout=e.timeout;function f(){if(!c)return;const h=Lt.from("getAllResponseHeaders"in c&&c.getAllResponseHeaders()),b={data:!o||o==="text"||o==="json"?c.responseText:c.response,status:c.status,statusText:c.statusText,headers:h,config:e,request:c};yh(function(g){n(g),a()},function(g){r(g),a()},b),c=null}if("onloadend"in c?c.onloadend=f:c.onreadystatechange=function(){!c||c.readyState!==4||c.status===0&&!(c.responseURL&&c.responseURL.indexOf("file:")===0)||setTimeout(f)},c.onabort=function(){c&&(r(new pe("Request aborted",pe.ECONNABORTED,e,c)),c=null)},c.onerror=function(){r(new pe("Network Error",pe.ERR_NETWORK,e,c)),c=null},c.ontimeout=function(){let v=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const b=e.transitional||hu;e.timeoutErrorMessage&&(v=e.timeoutErrorMessage),r(new pe(v,b.clarifyTimeoutError?pe.ETIMEDOUT:pe.ECONNABORTED,e,c)),c=null},bt.isStandardBrowserEnv){const h=(e.withCredentials||Sh(u))&&e.xsrfCookieName&&_h.read(e.xsrfCookieName);h&&i.set(e.xsrfHeaderName,h)}s===void 0&&i.setContentType(null),"setRequestHeader"in c&&D.forEach(i.toJSON(),function(v,b){c.setRequestHeader(b,v)}),D.isUndefined(e.withCredentials)||(c.withCredentials=!!e.withCredentials),o&&o!=="json"&&(c.responseType=e.responseType),typeof e.onDownloadProgress=="function"&&c.addEventListener("progress",Ia(e.onDownloadProgress,!0)),typeof e.onUploadProgress=="function"&&c.upload&&c.upload.addEventListener("progress",Ia(e.onUploadProgress)),(e.cancelToken||e.signal)&&(l=h=>{c&&(r(!h||h.type?new xr(null,e,c):h),c.abort(),c=null)},e.cancelToken&&e.cancelToken.subscribe(l),e.signal&&(e.signal.aborted?l():e.signal.addEventListener("abort",l)));const d=Th(u);if(d&&bt.protocols.indexOf(d)===-1){r(new pe("Unsupported protocol "+d+":",pe.ERR_BAD_REQUEST,e));return}c.send(s||null)})},Gr={http:Qm,xhr:xh};D.forEach(Gr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}});const Ph={getAdapter:e=>{e=D.isArray(e)?e:[e];const{length:t}=e;let n,r;for(let s=0;s<t&&(n=e[s],!(r=D.isString(n)?Gr[n.toLowerCase()]:n));s++);if(!r)throw r===!1?new pe("Adapter ".concat(n," is not supported by the environment"),"ERR_NOT_SUPPORT"):new Error(D.hasOwnProp(Gr,n)?"Adapter '".concat(n,"' is not available in the build"):"Unknown adapter '".concat(n,"'"));if(!D.isFunction(r))throw new TypeError("adapter is not a function");return r},adapters:Gr};function Ks(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new xr(null,e)}function Na(e){return Ks(e),e.headers=Lt.from(e.headers),e.data=Gs.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ph.getAdapter(e.adapter||_o.adapter)(e).then(function(r){return Ks(e),r.data=Gs.call(e,e.transformResponse,r),r.headers=Lt.from(r.headers),r},function(r){return vu(r)||(Ks(e),r&&r.response&&(r.response.data=Gs.call(e,e.transformResponse,r.response),r.response.headers=Lt.from(r.response.headers))),Promise.reject(r)})}const Ra=e=>e instanceof Lt?e.toJSON():e;function Mn(e,t){t=t||{};const n={};function r(c,u,f){return D.isPlainObject(c)&&D.isPlainObject(u)?D.merge.call({caseless:f},c,u):D.isPlainObject(u)?D.merge({},u):D.isArray(u)?u.slice():u}function s(c,u,f){if(D.isUndefined(u)){if(!D.isUndefined(c))return r(void 0,c,f)}else return r(c,u,f)}function i(c,u){if(!D.isUndefined(u))return r(void 0,u)}function o(c,u){if(D.isUndefined(u)){if(!D.isUndefined(c))return r(void 0,c)}else return r(void 0,u)}function l(c,u,f){if(f in t)return r(c,u);if(f in e)return r(void 0,c)}const a={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:l,headers:(c,u)=>s(Ra(c),Ra(u),!0)};return D.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=a[u]||s,d=f(e[u],t[u],u);D.isUndefined(d)&&f!==l||(n[u]=d)}),n}const yu="1.4.0",Eo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Eo[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Ma={};Eo.transitional=function(t,n,r){function s(i,o){return"[Axios v"+yu+"] Transitional option '"+i+"'"+o+(r?". "+r:"")}return(i,o,l)=>{if(t===!1)throw new pe(s(o," has been removed"+(n?" in "+n:"")),pe.ERR_DEPRECATED);return n&&!Ma[o]&&(Ma[o]=!0,console.warn(s(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,o,l):!0}};function Lh(e,t,n){if(typeof e!="object")throw new pe("options must be an object",pe.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const i=r[s],o=t[i];if(o){const l=e[i],a=l===void 0||o(l,i,e);if(a!==!0)throw new pe("option "+i+" must be "+a,pe.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new pe("Unknown option "+i,pe.ERR_BAD_OPTION)}}const Ti={assertOptions:Lh,validators:Eo},Dt=Ti.validators;class ts{constructor(t){this.defaults=t,this.interceptors={request:new La,response:new La}}request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Mn(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:i}=n;r!==void 0&&Ti.assertOptions(r,{silentJSONParsing:Dt.transitional(Dt.boolean),forcedJSONParsing:Dt.transitional(Dt.boolean),clarifyTimeoutError:Dt.transitional(Dt.boolean)},!1),s!=null&&(D.isFunction(s)?n.paramsSerializer={serialize:s}:Ti.assertOptions(s,{encode:Dt.function,serialize:Dt.function},!0)),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o;o=i&&D.merge(i.common,i[n.method]),o&&D.forEach(["delete","get","head","post","put","patch","common"],v=>{delete i[v]}),n.headers=Lt.concat(o,i);const l=[];let a=!0;this.interceptors.request.forEach(function(b){typeof b.runWhen=="function"&&b.runWhen(n)===!1||(a=a&&b.synchronous,l.unshift(b.fulfilled,b.rejected))});const c=[];this.interceptors.response.forEach(function(b){c.push(b.fulfilled,b.rejected)});let u,f=0,d;if(!a){const v=[Na.bind(this),void 0];for(v.unshift.apply(v,l),v.push.apply(v,c),d=v.length,u=Promise.resolve(n);f<d;)u=u.then(v[f++],v[f++]);return u}d=l.length;let h=n;for(f=0;f<d;){const v=l[f++],b=l[f++];try{h=v(h)}catch(C){b.call(this,C);break}}try{u=Na.call(this,h)}catch(v){return Promise.reject(v)}for(f=0,d=c.length;f<d;)u=u.then(c[f++],c[f++]);return u}getUri(t){t=Mn(this.defaults,t);const n=bu(t.baseURL,t.url);return mu(n,t.params,t.paramsSerializer)}}D.forEach(["delete","get","head","options"],function(t){ts.prototype[t]=function(n,r){return this.request(Mn(r||{},{method:t,url:n,data:(r||{}).data}))}});D.forEach(["post","put","patch"],function(t){function n(r){return function(i,o,l){return this.request(Mn(l||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}ts.prototype[t]=n(),ts.prototype[t+"Form"]=n(!0)});const Kr=ts;class wo{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const r=this;this.promise.then(s=>{if(!r._listeners)return;let i=r._listeners.length;for(;i-- >0;)r._listeners[i](s);r._listeners=null}),this.promise.then=s=>{let i;const o=new Promise(l=>{r.subscribe(l),i=l}).then(s);return o.cancel=function(){r.unsubscribe(i)},o},t(function(i,o,l){r.reason||(r.reason=new xr(i,o,l),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}static source(){let t;return{token:new wo(function(s){t=s}),cancel:t}}}const Ah=wo;function Ih(e){return function(n){return e.apply(null,n)}}function Nh(e){return D.isObject(e)&&e.isAxiosError===!0}const Ci={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ci).forEach(([e,t])=>{Ci[t]=e});const Rh=Ci;function _u(e){const t=new Kr(e),n=nu(Kr.prototype.request,t);return D.extend(n,Kr.prototype,t,{allOwnKeys:!0}),D.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return _u(Mn(e,s))},n}const Fe=_u(_o);Fe.Axios=Kr;Fe.CanceledError=xr;Fe.CancelToken=Ah;Fe.isCancel=vu;Fe.VERSION=yu;Fe.toFormData=Ts;Fe.AxiosError=pe;Fe.Cancel=Fe.CanceledError;Fe.all=function(t){return Promise.all(t)};Fe.spread=Ih;Fe.isAxiosError=Nh;Fe.mergeConfig=Mn;Fe.AxiosHeaders=Lt;Fe.formToJSON=e=>gu(D.isHTMLForm(e)?new FormData(e):e);Fe.HttpStatusCode=Rh;Fe.default=Fe;const EE=Fe;/*!
  * shared v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const Oi=typeof window<"u",Mh=typeof Symbol=="function"&&typeof Symbol.toStringTag=="symbol",Zt=e=>Mh?Symbol(e):e,Fh=(e,t,n)=>kh({l:e,k:t,s:n}),kh=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Re=e=>typeof e=="number"&&isFinite(e),Dh=e=>To(e)==="[object Date]",qt=e=>To(e)==="[object RegExp]",xs=e=>te(e)&&Object.keys(e).length===0;function Bh(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Be=Object.assign;let Fa;const sr=()=>Fa||(Fa=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ka(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const $h=Object.prototype.hasOwnProperty;function So(e,t){return $h.call(e,t)}const ve=Array.isArray,xe=e=>typeof e=="function",G=e=>typeof e=="string",ce=e=>typeof e=="boolean",be=e=>e!==null&&typeof e=="object",Eu=Object.prototype.toString,To=e=>Eu.call(e),te=e=>To(e)==="[object Object]",jh=e=>e==null?"":ve(e)||te(e)&&e.toString===Eu?JSON.stringify(e,null,2):String(e);/*!
  * message-compiler v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const me={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,__EXTEND_POINT__:15};function Ps(e,t,n={}){const{domain:r,messages:s,args:i}=n,o=e,l=new SyntaxError(String(o));return l.code=e,t&&(l.location=t),l.domain=r,l}function Hh(e){throw e}function Uh(e,t,n){return{line:e,column:t,offset:n}}function xi(e,t,n){const r={start:e,end:t};return n!=null&&(r.source=n),r}const St=" ",Vh="\r",Ue="\n",Wh=String.fromCharCode(8232),zh=String.fromCharCode(8233);function Gh(e){const t=e;let n=0,r=1,s=1,i=0;const o=M=>t[M]===Vh&&t[M+1]===Ue,l=M=>t[M]===Ue,a=M=>t[M]===zh,c=M=>t[M]===Wh,u=M=>o(M)||l(M)||a(M)||c(M),f=()=>n,d=()=>r,h=()=>s,v=()=>i,b=M=>o(M)||a(M)||c(M)?Ue:t[M],C=()=>b(n),g=()=>b(n+i);function w(){return i=0,u(n)&&(r++,s=0),o(n)&&n++,n++,s++,t[n]}function T(){return o(n+i)&&i++,i++,t[n+i]}function E(){n=0,r=1,s=1,i=0}function S(M=0){i=M}function N(){const M=n+i;for(;M!==n;)w();i=0}return{index:f,line:d,column:h,peekOffset:v,charAt:b,currentChar:C,currentPeek:g,next:w,peek:T,reset:E,resetPeek:S,skipToPeek:N}}const Bt=void 0,Da="'",Kh="tokenizer";function Yh(e,t={}){const n=t.location!==!1,r=Gh(e),s=()=>r.index(),i=()=>Uh(r.line(),r.column(),r.index()),o=i(),l=s(),a={currentType:14,offset:l,startLoc:o,endLoc:o,lastType:14,lastOffset:l,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},c=()=>a,{onError:u}=t;function f(p,m,y,...O){const R=c();if(m.column+=y,m.offset+=y,u){const $=xi(R.startLoc,m),j=Ps(p,$,{domain:Kh,args:O});u(j)}}function d(p,m,y){p.endLoc=i(),p.currentType=m;const O={type:m};return n&&(O.loc=xi(p.startLoc,p.endLoc)),y!=null&&(O.value=y),O}const h=p=>d(p,14);function v(p,m){return p.currentChar()===m?(p.next(),m):(f(me.EXPECTED_TOKEN,i(),0,m),"")}function b(p){let m="";for(;p.currentPeek()===St||p.currentPeek()===Ue;)m+=p.currentPeek(),p.peek();return m}function C(p){const m=b(p);return p.skipToPeek(),m}function g(p){if(p===Bt)return!1;const m=p.charCodeAt(0);return m>=97&&m<=122||m>=65&&m<=90||m===95}function w(p){if(p===Bt)return!1;const m=p.charCodeAt(0);return m>=48&&m<=57}function T(p,m){const{currentType:y}=m;if(y!==2)return!1;b(p);const O=g(p.currentPeek());return p.resetPeek(),O}function E(p,m){const{currentType:y}=m;if(y!==2)return!1;b(p);const O=p.currentPeek()==="-"?p.peek():p.currentPeek(),R=w(O);return p.resetPeek(),R}function S(p,m){const{currentType:y}=m;if(y!==2)return!1;b(p);const O=p.currentPeek()===Da;return p.resetPeek(),O}function N(p,m){const{currentType:y}=m;if(y!==8)return!1;b(p);const O=p.currentPeek()===".";return p.resetPeek(),O}function M(p,m){const{currentType:y}=m;if(y!==9)return!1;b(p);const O=g(p.currentPeek());return p.resetPeek(),O}function F(p,m){const{currentType:y}=m;if(!(y===8||y===12))return!1;b(p);const O=p.currentPeek()===":";return p.resetPeek(),O}function x(p,m){const{currentType:y}=m;if(y!==10)return!1;const O=()=>{const $=p.currentPeek();return $==="{"?g(p.peek()):$==="@"||$==="%"||$==="|"||$===":"||$==="."||$===St||!$?!1:$===Ue?(p.peek(),O()):g($)},R=O();return p.resetPeek(),R}function W(p){b(p);const m=p.currentPeek()==="|";return p.resetPeek(),m}function P(p){const m=b(p),y=p.currentPeek()==="%"&&p.peek()==="{";return p.resetPeek(),{isModulo:y,hasSpace:m.length>0}}function A(p,m=!0){const y=(R=!1,$="",j=!1)=>{const U=p.currentPeek();return U==="{"?$==="%"?!1:R:U==="@"||!U?$==="%"?!0:R:U==="%"?(p.peek(),y(R,"%",!0)):U==="|"?$==="%"||j?!0:!($===St||$===Ue):U===St?(p.peek(),y(!0,St,j)):U===Ue?(p.peek(),y(!0,Ue,j)):!0},O=y();return m&&p.resetPeek(),O}function H(p,m){const y=p.currentChar();return y===Bt?Bt:m(y)?(p.next(),y):null}function J(p){return H(p,y=>{const O=y.charCodeAt(0);return O>=97&&O<=122||O>=65&&O<=90||O>=48&&O<=57||O===95||O===36})}function re(p){return H(p,y=>{const O=y.charCodeAt(0);return O>=48&&O<=57})}function X(p){return H(p,y=>{const O=y.charCodeAt(0);return O>=48&&O<=57||O>=65&&O<=70||O>=97&&O<=102})}function ne(p){let m="",y="";for(;m=re(p);)y+=m;return y}function ie(p){C(p);const m=p.currentChar();return m!=="%"&&f(me.EXPECTED_TOKEN,i(),0,m),p.next(),"%"}function we(p){let m="";for(;;){const y=p.currentChar();if(y==="{"||y==="}"||y==="@"||y==="|"||!y)break;if(y==="%")if(A(p))m+=y,p.next();else break;else if(y===St||y===Ue)if(A(p))m+=y,p.next();else{if(W(p))break;m+=y,p.next()}else m+=y,p.next()}return m}function Le(p){C(p);let m="",y="";for(;m=J(p);)y+=m;return p.currentChar()===Bt&&f(me.UNTERMINATED_CLOSING_BRACE,i(),0),y}function Te(p){C(p);let m="";return p.currentChar()==="-"?(p.next(),m+="-".concat(ne(p))):m+=ne(p),p.currentChar()===Bt&&f(me.UNTERMINATED_CLOSING_BRACE,i(),0),m}function Se(p){C(p),v(p,"'");let m="",y="";const O=$=>$!==Da&&$!==Ue;for(;m=H(p,O);)m==="\\"?y+=Ye(p):y+=m;const R=p.currentChar();return R===Ue||R===Bt?(f(me.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,i(),0),R===Ue&&(p.next(),v(p,"'")),y):(v(p,"'"),y)}function Ye(p){const m=p.currentChar();switch(m){case"\\":case"'":return p.next(),"\\".concat(m);case"u":return qe(p,m,4);case"U":return qe(p,m,6);default:return f(me.UNKNOWN_ESCAPE_SEQUENCE,i(),0,m),""}}function qe(p,m,y){v(p,m);let O="";for(let R=0;R<y;R++){const $=X(p);if(!$){f(me.INVALID_UNICODE_ESCAPE_SEQUENCE,i(),0,"\\".concat(m).concat(O).concat(p.currentChar()));break}O+=$}return"\\".concat(m).concat(O)}function ft(p){C(p);let m="",y="";const O=R=>R!=="{"&&R!=="}"&&R!==St&&R!==Ue;for(;m=H(p,O);)y+=m;return y}function Ce(p){let m="",y="";for(;m=J(p);)y+=m;return y}function k(p){const m=(y=!1,O)=>{const R=p.currentChar();return R==="{"||R==="%"||R==="@"||R==="|"||!R||R===St?O:R===Ue?(O+=R,p.next(),m(y,O)):(O+=R,p.next(),m(!0,O))};return m(!1,"")}function z(p){C(p);const m=v(p,"|");return C(p),m}function V(p,m){let y=null;switch(p.currentChar()){case"{":return m.braceNest>=1&&f(me.NOT_ALLOW_NEST_PLACEHOLDER,i(),0),p.next(),y=d(m,2,"{"),C(p),m.braceNest++,y;case"}":return m.braceNest>0&&m.currentType===2&&f(me.EMPTY_PLACEHOLDER,i(),0),p.next(),y=d(m,3,"}"),m.braceNest--,m.braceNest>0&&C(p),m.inLinked&&m.braceNest===0&&(m.inLinked=!1),y;case"@":return m.braceNest>0&&f(me.UNTERMINATED_CLOSING_BRACE,i(),0),y=K(p,m)||h(m),m.braceNest=0,y;default:let R=!0,$=!0,j=!0;if(W(p))return m.braceNest>0&&f(me.UNTERMINATED_CLOSING_BRACE,i(),0),y=d(m,1,z(p)),m.braceNest=0,m.inLinked=!1,y;if(m.braceNest>0&&(m.currentType===5||m.currentType===6||m.currentType===7))return f(me.UNTERMINATED_CLOSING_BRACE,i(),0),m.braceNest=0,ae(p,m);if(R=T(p,m))return y=d(m,5,Le(p)),C(p),y;if($=E(p,m))return y=d(m,6,Te(p)),C(p),y;if(j=S(p,m))return y=d(m,7,Se(p)),C(p),y;if(!R&&!$&&!j)return y=d(m,13,ft(p)),f(me.INVALID_TOKEN_IN_PLACEHOLDER,i(),0,y.value),C(p),y;break}return y}function K(p,m){const{currentType:y}=m;let O=null;const R=p.currentChar();switch((y===8||y===9||y===12||y===10)&&(R===Ue||R===St)&&f(me.INVALID_LINKED_FORMAT,i(),0),R){case"@":return p.next(),O=d(m,8,"@"),m.inLinked=!0,O;case".":return C(p),p.next(),d(m,9,".");case":":return C(p),p.next(),d(m,10,":");default:return W(p)?(O=d(m,1,z(p)),m.braceNest=0,m.inLinked=!1,O):N(p,m)||F(p,m)?(C(p),K(p,m)):M(p,m)?(C(p),d(m,12,Ce(p))):x(p,m)?(C(p),R==="{"?V(p,m)||O:d(m,11,k(p))):(y===8&&f(me.INVALID_LINKED_FORMAT,i(),0),m.braceNest=0,m.inLinked=!1,ae(p,m))}}function ae(p,m){let y={type:14};if(m.braceNest>0)return V(p,m)||h(m);if(m.inLinked)return K(p,m)||h(m);switch(p.currentChar()){case"{":return V(p,m)||h(m);case"}":return f(me.UNBALANCED_CLOSING_BRACE,i(),0),p.next(),d(m,3,"}");case"@":return K(p,m)||h(m);default:if(W(p))return y=d(m,1,z(p)),m.braceNest=0,m.inLinked=!1,y;const{isModulo:R,hasSpace:$}=P(p);if(R)return $?d(m,0,we(p)):d(m,4,ie(p));if(A(p))return d(m,0,we(p));break}return y}function _(){const{currentType:p,offset:m,startLoc:y,endLoc:O}=a;return a.lastType=p,a.lastOffset=m,a.lastStartLoc=y,a.lastEndLoc=O,a.offset=s(),a.startLoc=i(),r.currentChar()===Bt?d(a,14):ae(r,a)}return{nextToken:_,currentOffset:s,currentPosition:i,context:c}}const qh="parser",Xh=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function Jh(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const r=parseInt(t||n,16);return r<=55295||r>=57344?String.fromCodePoint(r):"�"}}}function Qh(e={}){const t=e.location!==!1,{onError:n}=e;function r(g,w,T,E,...S){const N=g.currentPosition();if(N.offset+=E,N.column+=E,n){const M=xi(T,N),F=Ps(w,M,{domain:qh,args:S});n(F)}}function s(g,w,T){const E={type:g,start:w,end:w};return t&&(E.loc={start:T,end:T}),E}function i(g,w,T,E){g.end=w,E&&(g.type=E),t&&g.loc&&(g.loc.end=T)}function o(g,w){const T=g.context(),E=s(3,T.offset,T.startLoc);return E.value=w,i(E,g.currentOffset(),g.currentPosition()),E}function l(g,w){const T=g.context(),{lastOffset:E,lastStartLoc:S}=T,N=s(5,E,S);return N.index=parseInt(w,10),g.nextToken(),i(N,g.currentOffset(),g.currentPosition()),N}function a(g,w){const T=g.context(),{lastOffset:E,lastStartLoc:S}=T,N=s(4,E,S);return N.key=w,g.nextToken(),i(N,g.currentOffset(),g.currentPosition()),N}function c(g,w){const T=g.context(),{lastOffset:E,lastStartLoc:S}=T,N=s(9,E,S);return N.value=w.replace(Xh,Jh),g.nextToken(),i(N,g.currentOffset(),g.currentPosition()),N}function u(g){const w=g.nextToken(),T=g.context(),{lastOffset:E,lastStartLoc:S}=T,N=s(8,E,S);return w.type!==12?(r(g,me.UNEXPECTED_EMPTY_LINKED_MODIFIER,T.lastStartLoc,0),N.value="",i(N,E,S),{nextConsumeToken:w,node:N}):(w.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,T.lastStartLoc,0,mt(w)),N.value=w.value||"",i(N,g.currentOffset(),g.currentPosition()),{node:N})}function f(g,w){const T=g.context(),E=s(7,T.offset,T.startLoc);return E.value=w,i(E,g.currentOffset(),g.currentPosition()),E}function d(g){const w=g.context(),T=s(6,w.offset,w.startLoc);let E=g.nextToken();if(E.type===9){const S=u(g);T.modifier=S.node,E=S.nextConsumeToken||g.nextToken()}switch(E.type!==10&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(E)),E=g.nextToken(),E.type===2&&(E=g.nextToken()),E.type){case 11:E.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(E)),T.key=f(g,E.value||"");break;case 5:E.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(E)),T.key=a(g,E.value||"");break;case 6:E.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(E)),T.key=l(g,E.value||"");break;case 7:E.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(E)),T.key=c(g,E.value||"");break;default:r(g,me.UNEXPECTED_EMPTY_LINKED_KEY,w.lastStartLoc,0);const S=g.context(),N=s(7,S.offset,S.startLoc);return N.value="",i(N,S.offset,S.startLoc),T.key=N,i(T,S.offset,S.startLoc),{nextConsumeToken:E,node:T}}return i(T,g.currentOffset(),g.currentPosition()),{node:T}}function h(g){const w=g.context(),T=w.currentType===1?g.currentOffset():w.offset,E=w.currentType===1?w.endLoc:w.startLoc,S=s(2,T,E);S.items=[];let N=null;do{const x=N||g.nextToken();switch(N=null,x.type){case 0:x.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(x)),S.items.push(o(g,x.value||""));break;case 6:x.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(x)),S.items.push(l(g,x.value||""));break;case 5:x.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(x)),S.items.push(a(g,x.value||""));break;case 7:x.value==null&&r(g,me.UNEXPECTED_LEXICAL_ANALYSIS,w.lastStartLoc,0,mt(x)),S.items.push(c(g,x.value||""));break;case 8:const W=d(g);S.items.push(W.node),N=W.nextConsumeToken||null;break}}while(w.currentType!==14&&w.currentType!==1);const M=w.currentType===1?w.lastOffset:g.currentOffset(),F=w.currentType===1?w.lastEndLoc:g.currentPosition();return i(S,M,F),S}function v(g,w,T,E){const S=g.context();let N=E.items.length===0;const M=s(1,w,T);M.cases=[],M.cases.push(E);do{const F=h(g);N||(N=F.items.length===0),M.cases.push(F)}while(S.currentType!==14);return N&&r(g,me.MUST_HAVE_MESSAGES_IN_PLURAL,T,0),i(M,g.currentOffset(),g.currentPosition()),M}function b(g){const w=g.context(),{offset:T,startLoc:E}=w,S=h(g);return w.currentType===14?S:v(g,T,E,S)}function C(g){const w=Yh(g,Be({},e)),T=w.context(),E=s(0,T.offset,T.startLoc);return t&&E.loc&&(E.loc.source=g),E.body=b(w),T.currentType!==14&&r(w,me.UNEXPECTED_LEXICAL_ANALYSIS,T.lastStartLoc,0,g[T.offset]||""),i(E,w.currentOffset(),w.currentPosition()),E}return{parse:C}}function mt(e){if(e.type===14)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Zh(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:i=>(n.helpers.add(i),i)}}function Ba(e,t){for(let n=0;n<e.length;n++)Co(e[n],t)}function Co(e,t){switch(e.type){case 1:Ba(e.cases,t),t.helper("plural");break;case 2:Ba(e.items,t);break;case 6:Co(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function eg(e,t={}){const n=Zh(e);n.helper("normalize"),e.body&&Co(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function tg(e,t){const{sourceMap:n,filename:r,breakLineCode:s,needIndent:i}=t,o={source:e.loc.source,filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:s,needIndent:i,indentLevel:0},l=()=>o;function a(b,C){o.code+=b}function c(b,C=!0){const g=C?s:"";a(i?g+"  ".repeat(b):g)}function u(b=!0){const C=++o.indentLevel;b&&c(C)}function f(b=!0){const C=--o.indentLevel;b&&c(C)}function d(){c(o.indentLevel)}return{context:l,push:a,indent:u,deindent:f,newline:d,helper:b=>"_".concat(b),needIndent:()=>o.needIndent}}function ng(e,t){const{helper:n}=e;e.push("".concat(n("linked"),"(")),Fn(e,t.key),t.modifier?(e.push(", "),Fn(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function rg(e,t){const{helper:n,needIndent:r}=e;e.push("".concat(n("normalize"),"([")),e.indent(r());const s=t.items.length;for(let i=0;i<s&&(Fn(e,t.items[i]),i!==s-1);i++)e.push(", ");e.deindent(r()),e.push("])")}function sg(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push("".concat(n("plural"),"([")),e.indent(r());const s=t.cases.length;for(let i=0;i<s&&(Fn(e,t.cases[i]),i!==s-1);i++)e.push(", ");e.deindent(r()),e.push("])")}}function ig(e,t){t.body?Fn(e,t.body):e.push("null")}function Fn(e,t){const{helper:n}=e;switch(t.type){case 0:ig(e,t);break;case 1:sg(e,t);break;case 2:rg(e,t);break;case 6:ng(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push("".concat(n("interpolate"),"(").concat(n("list"),"(").concat(t.index,"))"),t);break;case 4:e.push("".concat(n("interpolate"),"(").concat(n("named"),"(").concat(JSON.stringify(t.key),"))"),t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const og=(e,t={})=>{const n=G(t.mode)?t.mode:"normal",r=G(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,i=t.breakLineCode!=null?t.breakLineCode:n==="arrow"?";":"\n",o=t.needIndent?t.needIndent:n!=="arrow",l=e.helpers||[],a=tg(e,{mode:n,filename:r,sourceMap:s,breakLineCode:i,needIndent:o});a.push(n==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),a.indent(o),l.length>0&&(a.push("const { ".concat(l.map(f=>"".concat(f,": _").concat(f)).join(", ")," } = ctx")),a.newline()),a.push("return "),Fn(a,e),a.deindent(o),a.push("}");const{code:c,map:u}=a.context();return{ast:e,code:c,map:u?u.toJSON():void 0}};function ag(e,t={}){const n=Be({},t),s=Qh(n).parse(e);return eg(s,n),og(s,n)}/*!
  * devtools-if v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const wu={I18nInit:"i18n:init",FunctionTranslate:"function:translate"};/*!
  * core-base v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const en=[];en[0]={w:[0],i:[3,0],"[":[4],o:[7]};en[1]={w:[1],".":[2],"[":[4],o:[7]};en[2]={w:[2],i:[3,0],0:[3,0]};en[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};en[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};en[5]={"'":[4,0],o:8,l:[5,0]};en[6]={'"':[4,0],o:8,l:[6,0]};const lg=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function cg(e){return lg.test(e)}function ug(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t===n&&(t===34||t===39)?e.slice(1,-1):e}function fg(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function dg(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:cg(t)?ug(t):"*"+t}function pg(e){const t=[];let n=-1,r=0,s=0,i,o,l,a,c,u,f;const d=[];d[0]=()=>{o===void 0?o=l:o+=l},d[1]=()=>{o!==void 0&&(t.push(o),o=void 0)},d[2]=()=>{d[0](),s++},d[3]=()=>{if(s>0)s--,r=4,d[0]();else{if(s=0,o===void 0||(o=dg(o),o===!1))return!1;d[1]()}};function h(){const v=e[n+1];if(r===5&&v==="'"||r===6&&v==='"')return n++,l="\\"+v,d[0](),!0}for(;r!==null;)if(n++,i=e[n],!(i==="\\"&&h())){if(a=fg(i),f=en[r],c=f[a]||f.l||8,c===8||(r=c[0],c[1]!==void 0&&(u=d[c[1]],u&&(l=i,u()===!1))))return;if(r===7)return t}}const $a=new Map;function mg(e,t){return be(e)?e[t]:null}function hg(e,t){if(!be(e))return null;let n=$a.get(t);if(n||(n=pg(t),n&&$a.set(t,n)),!n)return null;const r=n.length;let s=e,i=0;for(;i<r;){const o=s[n[i]];if(o===void 0)return null;s=o,i++}return s}const gg=e=>e,vg=e=>"",bg="text",yg=e=>e.length===0?"":e.join(""),_g=jh;function ja(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function Eg(e){const t=Re(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Re(e.named.count)||Re(e.named.n))?Re(e.named.count)?e.named.count:Re(e.named.n)?e.named.n:t:t}function wg(e,t){t.count||(t.count=e),t.n||(t.n=e)}function Sg(e={}){const t=e.locale,n=Eg(e),r=be(e.pluralRules)&&G(t)&&xe(e.pluralRules[t])?e.pluralRules[t]:ja,s=be(e.pluralRules)&&G(t)&&xe(e.pluralRules[t])?ja:void 0,i=g=>g[r(n,g.length,s)],o=e.list||[],l=g=>o[g],a=e.named||{};Re(e.pluralIndex)&&wg(n,a);const c=g=>a[g];function u(g){const w=xe(e.messages)?e.messages(g):be(e.messages)?e.messages[g]:!1;return w||(e.parent?e.parent.message(g):vg)}const f=g=>e.modifiers?e.modifiers[g]:gg,d=te(e.processor)&&xe(e.processor.normalize)?e.processor.normalize:yg,h=te(e.processor)&&xe(e.processor.interpolate)?e.processor.interpolate:_g,v=te(e.processor)&&G(e.processor.type)?e.processor.type:bg,C={list:l,named:c,plural:i,linked:(g,...w)=>{const[T,E]=w;let S="text",N="";w.length===1?be(T)?(N=T.modifier||N,S=T.type||S):G(T)&&(N=T||N):w.length===2&&(G(T)&&(N=T||N),G(E)&&(S=E||S));let M=u(g)(C);return S==="vnode"&&ve(M)&&N&&(M=M[0]),N?f(N)(M,S):M},message:u,type:v,interpolate:h,normalize:d};return C}let yr=null;function Tg(e){yr=e}function Cg(e,t,n){yr&&yr.emit(wu.I18nInit,{timestamp:Date.now(),i18n:e,version:t,meta:n})}const Og=xg(wu.FunctionTranslate);function xg(e){return t=>yr&&yr.emit(e,t)}function Pg(e,t,n){return[...new Set([n,...ve(t)?t:be(t)?Object.keys(t):G(t)?[t]:[n]])]}function Su(e,t,n){const r=G(n)?n:Pr,s=e;s.__localeChainCache||(s.__localeChainCache=new Map);let i=s.__localeChainCache.get(r);if(!i){i=[];let o=[n];for(;ve(o);)o=Ha(i,o,t);const l=ve(t)||!te(t)?t:t.default?t.default:null;o=G(l)?[l]:l,ve(o)&&Ha(i,o,!1),s.__localeChainCache.set(r,i)}return i}function Ha(e,t,n){let r=!0;for(let s=0;s<t.length&&ce(r);s++){const i=t[s];G(i)&&(r=Lg(e,t[s],n))}return r}function Lg(e,t,n){let r;const s=t.split("-");do{const i=s.join("-");r=Ag(e,i,n),s.splice(-1,1)}while(s.length&&r===!0);return r}function Ag(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r=t[t.length-1]!=="!";const s=t.replace(/!/g,"");e.push(s),(ve(n)||te(n))&&n[s]&&(r=n[s])}return r}const Ig="9.2.2",Ls=-1,Pr="en-US",Ua="",Va=e=>"".concat(e.charAt(0).toLocaleUpperCase()).concat(e.substr(1));function Ng(){return{upper:(e,t)=>t==="text"&&G(e)?e.toUpperCase():t==="vnode"&&be(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&G(e)?e.toLowerCase():t==="vnode"&&be(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&G(e)?Va(e):t==="vnode"&&be(e)&&"__v_isVNode"in e?Va(e.children):e}}let Tu;function Rg(e){Tu=e}let Cu;function Mg(e){Cu=e}let Ou;function Fg(e){Ou=e}let xu=null;const Wa=e=>{xu=e},kg=()=>xu;let Pu=null;const za=e=>{Pu=e},Dg=()=>Pu;let Ga=0;function Bg(e={}){const t=G(e.version)?e.version:Ig,n=G(e.locale)?e.locale:Pr,r=ve(e.fallbackLocale)||te(e.fallbackLocale)||G(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:n,s=te(e.messages)?e.messages:{[n]:{}},i=te(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},o=te(e.numberFormats)?e.numberFormats:{[n]:{}},l=Be({},e.modifiers||{},Ng()),a=e.pluralRules||{},c=xe(e.missing)?e.missing:null,u=ce(e.missingWarn)||qt(e.missingWarn)?e.missingWarn:!0,f=ce(e.fallbackWarn)||qt(e.fallbackWarn)?e.fallbackWarn:!0,d=!!e.fallbackFormat,h=!!e.unresolving,v=xe(e.postTranslation)?e.postTranslation:null,b=te(e.processor)?e.processor:null,C=ce(e.warnHtmlMessage)?e.warnHtmlMessage:!0,g=!!e.escapeParameter,w=xe(e.messageCompiler)?e.messageCompiler:Tu,T=xe(e.messageResolver)?e.messageResolver:Cu||mg,E=xe(e.localeFallbacker)?e.localeFallbacker:Ou||Pg,S=be(e.fallbackContext)?e.fallbackContext:void 0,N=xe(e.onWarn)?e.onWarn:Bh,M=e,F=be(M.__datetimeFormatters)?M.__datetimeFormatters:new Map,x=be(M.__numberFormatters)?M.__numberFormatters:new Map,W=be(M.__meta)?M.__meta:{};Ga++;const P={version:t,cid:Ga,locale:n,fallbackLocale:r,messages:s,modifiers:l,pluralRules:a,missing:c,missingWarn:u,fallbackWarn:f,fallbackFormat:d,unresolving:h,postTranslation:v,processor:b,warnHtmlMessage:C,escapeParameter:g,messageCompiler:w,messageResolver:T,localeFallbacker:E,fallbackContext:S,onWarn:N,__meta:W};return P.datetimeFormats=i,P.numberFormats=o,P.__datetimeFormatters=F,P.__numberFormatters=x,__INTLIFY_PROD_DEVTOOLS__&&Cg(P,t,W),P}function Oo(e,t,n,r,s){const{missing:i,onWarn:o}=e;if(i!==null){const l=i(e,n,t,s);return G(l)?l:t}else return t}function Kn(e,t,n){const r=e;r.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}const $g=e=>e;let Ka=Object.create(null);function jg(e,t={}){{const r=(t.onCacheKey||$g)(e),s=Ka[r];if(s)return s;let i=!1;const o=t.onError||Hh;t.onError=c=>{i=!0,o(c)};const{code:l}=ag(e,t),a=new Function("return ".concat(l))();return i?a:Ka[r]=a}}let Lu=me.__EXTEND_POINT__;const Ys=()=>++Lu,xn={INVALID_ARGUMENT:Lu,INVALID_DATE_ARGUMENT:Ys(),INVALID_ISO_DATE_ARGUMENT:Ys(),__EXTEND_POINT__:Ys()};function Pn(e){return Ps(e,null,void 0)}const Ya=()=>"",gt=e=>xe(e);function qa(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:s,messageCompiler:i,fallbackLocale:o,messages:l}=e,[a,c]=Pi(...t),u=ce(c.missingWarn)?c.missingWarn:e.missingWarn,f=ce(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,d=ce(c.escapeParameter)?c.escapeParameter:e.escapeParameter,h=!!c.resolvedMessage,v=G(c.default)||ce(c.default)?ce(c.default)?i?a:()=>a:c.default:n?i?a:()=>a:"",b=n||v!=="",C=G(c.locale)?c.locale:e.locale;d&&Hg(c);let[g,w,T]=h?[a,C,l[C]||{}]:Au(e,a,C,o,f,u),E=g,S=a;if(!h&&!(G(E)||gt(E))&&b&&(E=v,S=E),!h&&(!(G(E)||gt(E))||!G(w)))return s?Ls:a;let N=!1;const M=()=>{N=!0},F=gt(E)?E:Iu(e,a,w,E,S,M);if(N)return E;const x=Wg(e,w,T,c),W=Sg(x),P=Ug(e,F,W),A=r?r(P,a):P;if(__INTLIFY_PROD_DEVTOOLS__){const H={timestamp:Date.now(),key:G(a)?a:gt(E)?E.key:"",locale:w||(gt(E)?E.locale:""),format:G(E)?E:gt(E)?E.source:"",message:A};H.meta=Be({},e.__meta,kg()||{}),Og(H)}return A}function Hg(e){ve(e.list)?e.list=e.list.map(t=>G(t)?ka(t):t):be(e.named)&&Object.keys(e.named).forEach(t=>{G(e.named[t])&&(e.named[t]=ka(e.named[t]))})}function Au(e,t,n,r,s,i){const{messages:o,onWarn:l,messageResolver:a,localeFallbacker:c}=e,u=c(e,r,n);let f={},d,h=null;const v="translate";for(let b=0;b<u.length&&(d=u[b],f=o[d]||{},(h=a(f,t))===null&&(h=f[t]),!(G(h)||xe(h)));b++){const C=Oo(e,t,d,i,v);C!==t&&(h=C)}return[h,d,f]}function Iu(e,t,n,r,s,i){const{messageCompiler:o,warnHtmlMessage:l}=e;if(gt(r)){const c=r;return c.locale=c.locale||n,c.key=c.key||t,c}if(o==null){const c=()=>r;return c.locale=n,c.key=t,c}const a=o(r,Vg(e,n,s,r,l,i));return a.locale=n,a.key=t,a.source=r,a}function Ug(e,t,n){return t(n)}function Pi(...e){const[t,n,r]=e,s={};if(!G(t)&&!Re(t)&&!gt(t))throw Pn(xn.INVALID_ARGUMENT);const i=Re(t)?String(t):(gt(t),t);return Re(n)?s.plural=n:G(n)?s.default=n:te(n)&&!xs(n)?s.named=n:ve(n)&&(s.list=n),Re(r)?s.plural=r:G(r)?s.default=r:te(r)&&Be(s,r),[i,s]}function Vg(e,t,n,r,s,i){return{warnHtmlMessage:s,onError:o=>{throw i&&i(o),o},onCacheKey:o=>Fh(t,n,o)}}function Wg(e,t,n,r){const{modifiers:s,pluralRules:i,messageResolver:o,fallbackLocale:l,fallbackWarn:a,missingWarn:c,fallbackContext:u}=e,d={locale:t,modifiers:s,pluralRules:i,messages:h=>{let v=o(n,h);if(v==null&&u){const[,,b]=Au(u,h,t,l,a,c);v=o(b,h)}if(G(v)){let b=!1;const g=Iu(e,h,t,v,h,()=>{b=!0});return b?Ya:g}else return gt(v)?v:Ya}};return e.processor&&(d.processor=e.processor),r.list&&(d.list=r.list),r.named&&(d.named=r.named),Re(r.plural)&&(d.pluralIndex=r.plural),d}function Xa(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:s,onWarn:i,localeFallbacker:o}=e,{__datetimeFormatters:l}=e,[a,c,u,f]=Li(...t),d=ce(u.missingWarn)?u.missingWarn:e.missingWarn;ce(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const h=!!u.part,v=G(u.locale)?u.locale:e.locale,b=o(e,s,v);if(!G(a)||a==="")return new Intl.DateTimeFormat(v,f).format(c);let C={},g,w=null;const T="datetime format";for(let N=0;N<b.length&&(g=b[N],C=n[g]||{},w=C[a],!te(w));N++)Oo(e,a,g,d,T);if(!te(w)||!G(g))return r?Ls:a;let E="".concat(g,"__").concat(a);xs(f)||(E="".concat(E,"__").concat(JSON.stringify(f)));let S=l.get(E);return S||(S=new Intl.DateTimeFormat(g,Be({},w,f)),l.set(E,S)),h?S.formatToParts(c):S.format(c)}const Nu=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Li(...e){const[t,n,r,s]=e,i={};let o={},l;if(G(t)){const a=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!a)throw Pn(xn.INVALID_ISO_DATE_ARGUMENT);const c=a[3]?a[3].trim().startsWith("T")?"".concat(a[1].trim()).concat(a[3].trim()):"".concat(a[1].trim(),"T").concat(a[3].trim()):a[1].trim();l=new Date(c);try{l.toISOString()}catch(u){throw Pn(xn.INVALID_ISO_DATE_ARGUMENT)}}else if(Dh(t)){if(isNaN(t.getTime()))throw Pn(xn.INVALID_DATE_ARGUMENT);l=t}else if(Re(t))l=t;else throw Pn(xn.INVALID_ARGUMENT);return G(n)?i.key=n:te(n)&&Object.keys(n).forEach(a=>{Nu.includes(a)?o[a]=n[a]:i[a]=n[a]}),G(r)?i.locale=r:te(r)&&(o=r),te(s)&&(o=s),[i.key||"",l,i,o]}function Ja(e,t,n){const r=e;for(const s in n){const i="".concat(t,"__").concat(s);r.__datetimeFormatters.has(i)&&r.__datetimeFormatters.delete(i)}}function Qa(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:s,onWarn:i,localeFallbacker:o}=e,{__numberFormatters:l}=e,[a,c,u,f]=Ai(...t),d=ce(u.missingWarn)?u.missingWarn:e.missingWarn;ce(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const h=!!u.part,v=G(u.locale)?u.locale:e.locale,b=o(e,s,v);if(!G(a)||a==="")return new Intl.NumberFormat(v,f).format(c);let C={},g,w=null;const T="number format";for(let N=0;N<b.length&&(g=b[N],C=n[g]||{},w=C[a],!te(w));N++)Oo(e,a,g,d,T);if(!te(w)||!G(g))return r?Ls:a;let E="".concat(g,"__").concat(a);xs(f)||(E="".concat(E,"__").concat(JSON.stringify(f)));let S=l.get(E);return S||(S=new Intl.NumberFormat(g,Be({},w,f)),l.set(E,S)),h?S.formatToParts(c):S.format(c)}const Ru=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Ai(...e){const[t,n,r,s]=e,i={};let o={};if(!Re(t))throw Pn(xn.INVALID_ARGUMENT);const l=t;return G(n)?i.key=n:te(n)&&Object.keys(n).forEach(a=>{Ru.includes(a)?o[a]=n[a]:i[a]=n[a]}),G(r)?i.locale=r:te(r)&&(o=r),te(s)&&(o=s),[i.key||"",l,i,o]}function Za(e,t,n){const r=e;for(const s in n){const i="".concat(t,"__").concat(s);r.__numberFormatters.has(i)&&r.__numberFormatters.delete(i)}}typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(sr().__INTLIFY_PROD_DEVTOOLS__=!1);/*!
  * vue-i18n v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */const zg="9.2.2";function Gg(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(sr().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(sr().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(sr().__INTLIFY_PROD_DEVTOOLS__=!1)}let Mu=me.__EXTEND_POINT__;const We=()=>++Mu,Ae={UNEXPECTED_RETURN_TYPE:Mu,INVALID_ARGUMENT:We(),MUST_BE_CALL_SETUP_TOP:We(),NOT_INSLALLED:We(),NOT_AVAILABLE_IN_LEGACY_MODE:We(),REQUIRED_VALUE:We(),INVALID_VALUE:We(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:We(),NOT_INSLALLED_WITH_PROVIDE:We(),UNEXPECTED_ERROR:We(),NOT_COMPATIBLE_LEGACY_VUE_I18N:We(),BRIDGE_SUPPORT_VUE_2_ONLY:We(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:We(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:We(),__EXTEND_POINT__:We()};function Me(e,...t){return Ps(e,null,void 0)}const Ii=Zt("__transrateVNode"),Ni=Zt("__datetimeParts"),Ri=Zt("__numberParts"),Fu=Zt("__setPluralRules");Zt("__intlifyMeta");const ku=Zt("__injectWithOption");function Mi(e){if(!be(e))return e;for(const t in e)if(So(e,t))if(!t.includes("."))be(e[t])&&Mi(e[t]);else{const n=t.split("."),r=n.length-1;let s=e;for(let i=0;i<r;i++)n[i]in s||(s[n[i]]={}),s=s[n[i]];s[n[r]]=e[t],delete e[t],be(s[n[r]])&&Mi(s[n[r]])}return e}function As(e,t){const{messages:n,__i18n:r,messageResolver:s,flatJson:i}=t,o=te(n)?n:ve(r)?{}:{[e]:{}};if(ve(r)&&r.forEach(l=>{if("locale"in l&&"resource"in l){const{locale:a,resource:c}=l;a?(o[a]=o[a]||{},ir(c,o[a])):ir(c,o)}else G(l)&&ir(JSON.parse(l),o)}),s==null&&i)for(const l in o)So(o,l)&&Mi(o[l]);return o}const kr=e=>!be(e)||ve(e);function ir(e,t){if(kr(e)||kr(t))throw Me(Ae.INVALID_VALUE);for(const n in e)So(e,n)&&(kr(e[n])||kr(t[n])?t[n]=e[n]:ir(e[n],t[n]))}function Du(e){return e.type}function Bu(e,t,n){let r=be(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=As(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const s=Object.keys(r);s.length&&s.forEach(i=>{e.mergeLocaleMessage(i,r[i])});{if(be(t.datetimeFormats)){const i=Object.keys(t.datetimeFormats);i.length&&i.forEach(o=>{e.mergeDateTimeFormat(o,t.datetimeFormats[o])})}if(be(t.numberFormats)){const i=Object.keys(t.numberFormats);i.length&&i.forEach(o=>{e.mergeNumberFormat(o,t.numberFormats[o])})}}}function el(e){return ee(Sr,null,e,0)}const tl="__INTLIFY_META__";let nl=0;function rl(e){return(t,n,r,s)=>e(n,r,Yt()||void 0,s)}const Kg=()=>{const e=Yt();let t=null;return e&&(t=Du(e)[tl])?{[tl]:t}:null};function xo(e={},t){const{__root:n}=e,r=n===void 0;let s=ce(e.inheritLocale)?e.inheritLocale:!0;const i=Q(n&&s?n.locale.value:G(e.locale)?e.locale:Pr),o=Q(n&&s?n.fallbackLocale.value:G(e.fallbackLocale)||ve(e.fallbackLocale)||te(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:i.value),l=Q(As(i.value,e)),a=Q(te(e.datetimeFormats)?e.datetimeFormats:{[i.value]:{}}),c=Q(te(e.numberFormats)?e.numberFormats:{[i.value]:{}});let u=n?n.missingWarn:ce(e.missingWarn)||qt(e.missingWarn)?e.missingWarn:!0,f=n?n.fallbackWarn:ce(e.fallbackWarn)||qt(e.fallbackWarn)?e.fallbackWarn:!0,d=n?n.fallbackRoot:ce(e.fallbackRoot)?e.fallbackRoot:!0,h=!!e.fallbackFormat,v=xe(e.missing)?e.missing:null,b=xe(e.missing)?rl(e.missing):null,C=xe(e.postTranslation)?e.postTranslation:null,g=n?n.warnHtmlMessage:ce(e.warnHtmlMessage)?e.warnHtmlMessage:!0,w=!!e.escapeParameter;const T=n?n.modifiers:te(e.modifiers)?e.modifiers:{};let E=e.pluralRules||n&&n.pluralRules,S;S=(()=>{r&&za(null);const L={version:zg,locale:i.value,fallbackLocale:o.value,messages:l.value,modifiers:T,pluralRules:E,missing:b===null?void 0:b,missingWarn:u,fallbackWarn:f,fallbackFormat:h,unresolving:!0,postTranslation:C===null?void 0:C,warnHtmlMessage:g,escapeParameter:w,messageResolver:e.messageResolver,__meta:{framework:"vue"}};L.datetimeFormats=a.value,L.numberFormats=c.value,L.__datetimeFormatters=te(S)?S.__datetimeFormatters:void 0,L.__numberFormatters=te(S)?S.__numberFormatters:void 0;const I=Bg(L);return r&&za(I),I})(),Kn(S,i.value,o.value);function M(){return[i.value,o.value,l.value,a.value,c.value]}const F=fe({get:()=>i.value,set:L=>{i.value=L,S.locale=i.value}}),x=fe({get:()=>o.value,set:L=>{o.value=L,S.fallbackLocale=o.value,Kn(S,i.value,L)}}),W=fe(()=>l.value),P=fe(()=>a.value),A=fe(()=>c.value);function H(){return xe(C)?C:null}function J(L){C=L,S.postTranslation=L}function re(){return v}function X(L){L!==null&&(b=rl(L)),v=L,S.missing=b}const ne=(L,I,Y,q,oe,he)=>{M();let le;if(__INTLIFY_PROD_DEVTOOLS__)try{Wa(Kg()),r||(S.fallbackContext=n?Dg():void 0),le=L(S)}finally{Wa(null),r||(S.fallbackContext=void 0)}else le=L(S);if(Re(le)&&le===Ls){const[Oe,tt]=I();return n&&d?q(n):oe(Oe)}else{if(he(le))return le;throw Me(Ae.UNEXPECTED_RETURN_TYPE)}};function ie(...L){return ne(I=>Reflect.apply(qa,null,[I,...L]),()=>Pi(...L),"translate",I=>Reflect.apply(I.t,I,[...L]),I=>I,I=>G(I))}function we(...L){const[I,Y,q]=L;if(q&&!be(q))throw Me(Ae.INVALID_ARGUMENT);return ie(I,Y,Be({resolvedMessage:!0},q||{}))}function Le(...L){return ne(I=>Reflect.apply(Xa,null,[I,...L]),()=>Li(...L),"datetime format",I=>Reflect.apply(I.d,I,[...L]),()=>Ua,I=>G(I))}function Te(...L){return ne(I=>Reflect.apply(Qa,null,[I,...L]),()=>Ai(...L),"number format",I=>Reflect.apply(I.n,I,[...L]),()=>Ua,I=>G(I))}function Se(L){return L.map(I=>G(I)||Re(I)||ce(I)?el(String(I)):I)}const qe={normalize:Se,interpolate:L=>L,type:"vnode"};function ft(...L){return ne(I=>{let Y;const q=I;try{q.processor=qe,Y=Reflect.apply(qa,null,[q,...L])}finally{q.processor=null}return Y},()=>Pi(...L),"translate",I=>I[Ii](...L),I=>[el(I)],I=>ve(I))}function Ce(...L){return ne(I=>Reflect.apply(Qa,null,[I,...L]),()=>Ai(...L),"number format",I=>I[Ri](...L),()=>[],I=>G(I)||ve(I))}function k(...L){return ne(I=>Reflect.apply(Xa,null,[I,...L]),()=>Li(...L),"datetime format",I=>I[Ni](...L),()=>[],I=>G(I)||ve(I))}function z(L){E=L,S.pluralRules=E}function V(L,I){const Y=G(I)?I:i.value,q=_(Y);return S.messageResolver(q,L)!==null}function K(L){let I=null;const Y=Su(S,o.value,i.value);for(let q=0;q<Y.length;q++){const oe=l.value[Y[q]]||{},he=S.messageResolver(oe,L);if(he!=null){I=he;break}}return I}function ae(L){const I=K(L);return I!=null?I:n?n.tm(L)||{}:{}}function _(L){return l.value[L]||{}}function p(L,I){l.value[L]=I,S.messages=l.value}function m(L,I){l.value[L]=l.value[L]||{},ir(I,l.value[L]),S.messages=l.value}function y(L){return a.value[L]||{}}function O(L,I){a.value[L]=I,S.datetimeFormats=a.value,Ja(S,L,I)}function R(L,I){a.value[L]=Be(a.value[L]||{},I),S.datetimeFormats=a.value,Ja(S,L,I)}function $(L){return c.value[L]||{}}function j(L,I){c.value[L]=I,S.numberFormats=c.value,Za(S,L,I)}function U(L,I){c.value[L]=Be(c.value[L]||{},I),S.numberFormats=c.value,Za(S,L,I)}nl++,n&&Oi&&(De(n.locale,L=>{s&&(i.value=L,S.locale=L,Kn(S,i.value,o.value))}),De(n.fallbackLocale,L=>{s&&(o.value=L,S.fallbackLocale=L,Kn(S,i.value,o.value))}));const B={id:nl,locale:F,fallbackLocale:x,get inheritLocale(){return s},set inheritLocale(L){s=L,L&&n&&(i.value=n.locale.value,o.value=n.fallbackLocale.value,Kn(S,i.value,o.value))},get availableLocales(){return Object.keys(l.value).sort()},messages:W,get modifiers(){return T},get pluralRules(){return E||{}},get isGlobal(){return r},get missingWarn(){return u},set missingWarn(L){u=L,S.missingWarn=u},get fallbackWarn(){return f},set fallbackWarn(L){f=L,S.fallbackWarn=f},get fallbackRoot(){return d},set fallbackRoot(L){d=L},get fallbackFormat(){return h},set fallbackFormat(L){h=L,S.fallbackFormat=h},get warnHtmlMessage(){return g},set warnHtmlMessage(L){g=L,S.warnHtmlMessage=L},get escapeParameter(){return w},set escapeParameter(L){w=L,S.escapeParameter=L},t:ie,getLocaleMessage:_,setLocaleMessage:p,mergeLocaleMessage:m,getPostTranslationHandler:H,setPostTranslationHandler:J,getMissingHandler:re,setMissingHandler:X,[Fu]:z};return B.datetimeFormats=P,B.numberFormats=A,B.rt=we,B.te=V,B.tm=ae,B.d=Le,B.n=Te,B.getDateTimeFormat=y,B.setDateTimeFormat=O,B.mergeDateTimeFormat=R,B.getNumberFormat=$,B.setNumberFormat=j,B.mergeNumberFormat=U,B[ku]=e.__injectWithOption,B[Ii]=ft,B[Ni]=k,B[Ri]=Ce,B}function Yg(e){const t=G(e.locale)?e.locale:Pr,n=G(e.fallbackLocale)||ve(e.fallbackLocale)||te(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,r=xe(e.missing)?e.missing:void 0,s=ce(e.silentTranslationWarn)||qt(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,i=ce(e.silentFallbackWarn)||qt(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,o=ce(e.fallbackRoot)?e.fallbackRoot:!0,l=!!e.formatFallbackMessages,a=te(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=xe(e.postTranslation)?e.postTranslation:void 0,f=G(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,d=!!e.escapeParameterHtml,h=ce(e.sync)?e.sync:!0;let v=e.messages;if(te(e.sharedMessages)){const S=e.sharedMessages;v=Object.keys(S).reduce((M,F)=>{const x=M[F]||(M[F]={});return Be(x,S[F]),M},v||{})}const{__i18n:b,__root:C,__injectWithOption:g}=e,w=e.datetimeFormats,T=e.numberFormats,E=e.flatJson;return{locale:t,fallbackLocale:n,messages:v,flatJson:E,datetimeFormats:w,numberFormats:T,missing:r,missingWarn:s,fallbackWarn:i,fallbackRoot:o,fallbackFormat:l,modifiers:a,pluralRules:c,postTranslation:u,warnHtmlMessage:f,escapeParameter:d,messageResolver:e.messageResolver,inheritLocale:h,__i18n:b,__root:C,__injectWithOption:g}}function Fi(e={},t){{const n=xo(Yg(e)),r={id:n.id,get locale(){return n.locale.value},set locale(s){n.locale.value=s},get fallbackLocale(){return n.fallbackLocale.value},set fallbackLocale(s){n.fallbackLocale.value=s},get messages(){return n.messages.value},get datetimeFormats(){return n.datetimeFormats.value},get numberFormats(){return n.numberFormats.value},get availableLocales(){return n.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(s){},get missing(){return n.getMissingHandler()},set missing(s){n.setMissingHandler(s)},get silentTranslationWarn(){return ce(n.missingWarn)?!n.missingWarn:n.missingWarn},set silentTranslationWarn(s){n.missingWarn=ce(s)?!s:s},get silentFallbackWarn(){return ce(n.fallbackWarn)?!n.fallbackWarn:n.fallbackWarn},set silentFallbackWarn(s){n.fallbackWarn=ce(s)?!s:s},get modifiers(){return n.modifiers},get formatFallbackMessages(){return n.fallbackFormat},set formatFallbackMessages(s){n.fallbackFormat=s},get postTranslation(){return n.getPostTranslationHandler()},set postTranslation(s){n.setPostTranslationHandler(s)},get sync(){return n.inheritLocale},set sync(s){n.inheritLocale=s},get warnHtmlInMessage(){return n.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(s){n.warnHtmlMessage=s!=="off"},get escapeParameterHtml(){return n.escapeParameter},set escapeParameterHtml(s){n.escapeParameter=s},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(s){},get pluralizationRules(){return n.pluralRules||{}},__composer:n,t(...s){const[i,o,l]=s,a={};let c=null,u=null;if(!G(i))throw Me(Ae.INVALID_ARGUMENT);const f=i;return G(o)?a.locale=o:ve(o)?c=o:te(o)&&(u=o),ve(l)?c=l:te(l)&&(u=l),Reflect.apply(n.t,n,[f,c||u||{},a])},rt(...s){return Reflect.apply(n.rt,n,[...s])},tc(...s){const[i,o,l]=s,a={plural:1};let c=null,u=null;if(!G(i))throw Me(Ae.INVALID_ARGUMENT);const f=i;return G(o)?a.locale=o:Re(o)?a.plural=o:ve(o)?c=o:te(o)&&(u=o),G(l)?a.locale=l:ve(l)?c=l:te(l)&&(u=l),Reflect.apply(n.t,n,[f,c||u||{},a])},te(s,i){return n.te(s,i)},tm(s){return n.tm(s)},getLocaleMessage(s){return n.getLocaleMessage(s)},setLocaleMessage(s,i){n.setLocaleMessage(s,i)},mergeLocaleMessage(s,i){n.mergeLocaleMessage(s,i)},d(...s){return Reflect.apply(n.d,n,[...s])},getDateTimeFormat(s){return n.getDateTimeFormat(s)},setDateTimeFormat(s,i){n.setDateTimeFormat(s,i)},mergeDateTimeFormat(s,i){n.mergeDateTimeFormat(s,i)},n(...s){return Reflect.apply(n.n,n,[...s])},getNumberFormat(s){return n.getNumberFormat(s)},setNumberFormat(s,i){n.setNumberFormat(s,i)},mergeNumberFormat(s,i){n.mergeNumberFormat(s,i)},getChoiceIndex(s,i){return-1},__onComponentInstanceCreated(s){const{componentInstanceCreatedListener:i}=e;i&&i(s,r)}};return r}}const Po={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function qg({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((r,s)=>r=[...r,...ve(s.children)?s.children:[s]],[]):t.reduce((n,r)=>{const s=e[r];return s&&(n[r]=s()),n},{})}function $u(e){return Ge}const sl={name:"i18n-t",props:Be({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Re(e)||!isNaN(e)}},Po),setup(e,t){const{slots:n,attrs:r}=t,s=e.i18n||Lo({useScope:e.scope,__useComponent:!0});return()=>{const i=Object.keys(n).filter(f=>f!=="_"),o={};e.locale&&(o.locale=e.locale),e.plural!==void 0&&(o.plural=G(e.plural)?+e.plural:e.plural);const l=qg(t,i),a=s[Ii](e.keypath,l,o),c=Be({},r),u=G(e.tag)||be(e.tag)?e.tag:$u();return $e(u,c,a)}}};function Xg(e){return ve(e)&&!G(e[0])}function ju(e,t,n,r){const{slots:s,attrs:i}=t;return()=>{const o={part:!0};let l={};e.locale&&(o.locale=e.locale),G(e.format)?o.key=e.format:be(e.format)&&(G(e.format.key)&&(o.key=e.format.key),l=Object.keys(e.format).reduce((d,h)=>n.includes(h)?Be({},d,{[h]:e.format[h]}):d,{}));const a=r(e.value,o,l);let c=[o.key];ve(a)?c=a.map((d,h)=>{const v=s[d.type],b=v?v({[d.type]:d.value,index:h,parts:a}):[d.value];return Xg(b)&&(b[0].key="".concat(d.type,"-").concat(h)),b}):G(a)&&(c=[a]);const u=Be({},i),f=G(e.tag)||be(e.tag)?e.tag:$u();return $e(f,u,c)}}const il={name:"i18n-n",props:Be({value:{type:Number,required:!0},format:{type:[String,Object]}},Po),setup(e,t){const n=e.i18n||Lo({useScope:"parent",__useComponent:!0});return ju(e,t,Ru,(...r)=>n[Ri](...r))}},ol={name:"i18n-d",props:Be({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Po),setup(e,t){const n=e.i18n||Lo({useScope:"parent",__useComponent:!0});return ju(e,t,Nu,(...r)=>n[Ni](...r))}};function Jg(e,t){const n=e;if(e.mode==="composition")return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return r!=null?r.__composer:e.global.__composer}}function Qg(e){const t=o=>{const{instance:l,modifiers:a,value:c}=o;if(!l||!l.$)throw Me(Ae.UNEXPECTED_ERROR);const u=Jg(e,l.$),f=al(c);return[Reflect.apply(u.t,u,[...ll(f)]),u]};return{created:(o,l)=>{const[a,c]=t(l);Oi&&e.global===c&&(o.__i18nWatcher=De(c.locale,()=>{l.instance&&l.instance.$forceUpdate()})),o.__composer=c,o.textContent=a},unmounted:o=>{Oi&&o.__i18nWatcher&&(o.__i18nWatcher(),o.__i18nWatcher=void 0,delete o.__i18nWatcher),o.__composer&&(o.__composer=void 0,delete o.__composer)},beforeUpdate:(o,{value:l})=>{if(o.__composer){const a=o.__composer,c=al(l);o.textContent=Reflect.apply(a.t,a,[...ll(c)])}},getSSRProps:o=>{const[l]=t(o);return{textContent:l}}}}function al(e){if(G(e))return{path:e};if(te(e)){if(!("path"in e))throw Me(Ae.REQUIRED_VALUE,"path");return e}else throw Me(Ae.INVALID_VALUE)}function ll(e){const{path:t,locale:n,args:r,choice:s,plural:i}=e,o={},l=r||{};return G(n)&&(o.locale=n),Re(s)&&(o.plural=s),Re(i)&&(o.plural=i),[t,l,o]}function Zg(e,t,...n){const r=te(n[0])?n[0]:{},s=!!r.useI18nComponentName;(ce(r.globalInstall)?r.globalInstall:!0)&&(e.component(s?"i18n":sl.name,sl),e.component(il.name,il),e.component(ol.name,ol)),e.directive("t",Qg(t))}function ev(e,t,n){return{beforeCreate(){const r=Yt();if(!r)throw Me(Ae.UNEXPECTED_ERROR);const s=this.$options;if(s.i18n){const i=s.i18n;s.__i18n&&(i.__i18n=s.__i18n),i.__root=t,this===this.$root?this.$i18n=cl(e,i):(i.__injectWithOption=!0,this.$i18n=Fi(i))}else s.__i18n?this===this.$root?this.$i18n=cl(e,s):this.$i18n=Fi({__i18n:s.__i18n,__injectWithOption:!0,__root:t}):this.$i18n=e;s.__i18nGlobal&&Bu(t,s,s),e.__onComponentInstanceCreated(this.$i18n),n.__setInstance(r,this.$i18n),this.$t=(...i)=>this.$i18n.t(...i),this.$rt=(...i)=>this.$i18n.rt(...i),this.$tc=(...i)=>this.$i18n.tc(...i),this.$te=(i,o)=>this.$i18n.te(i,o),this.$d=(...i)=>this.$i18n.d(...i),this.$n=(...i)=>this.$i18n.n(...i),this.$tm=i=>this.$i18n.tm(i)},mounted(){},unmounted(){const r=Yt();if(!r)throw Me(Ae.UNEXPECTED_ERROR);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__deleteInstance(r),delete this.$i18n}}}function cl(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Fu](t.pluralizationRules||e.pluralizationRules);const n=As(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach(r=>e.mergeLocaleMessage(r,n[r])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(r=>e.mergeDateTimeFormat(r,t.datetimeFormats[r])),t.numberFormats&&Object.keys(t.numberFormats).forEach(r=>e.mergeNumberFormat(r,t.numberFormats[r])),e}const tv=Zt("global-vue-i18n");function wE(e={},t){const n=__VUE_I18N_LEGACY_API__&&ce(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,r=ce(e.globalInjection)?e.globalInjection:!0,s=__VUE_I18N_LEGACY_API__&&n?!!e.allowComposition:!0,i=new Map,[o,l]=nv(e,n),a=Zt("");function c(d){return i.get(d)||null}function u(d,h){i.set(d,h)}function f(d){i.delete(d)}{const d={get mode(){return __VUE_I18N_LEGACY_API__&&n?"legacy":"composition"},get allowComposition(){return s},async install(h,...v){h.__VUE_I18N_SYMBOL__=a,h.provide(h.__VUE_I18N_SYMBOL__,d),!n&&r&&fv(h,d.global),__VUE_I18N_FULL_INSTALL__&&Zg(h,d,...v),__VUE_I18N_LEGACY_API__&&n&&h.mixin(ev(l,l.__composer,d));const b=h.unmount;h.unmount=()=>{d.dispose(),b()}},get global(){return l},dispose(){o.stop()},__instances:i,__getInstance:c,__setInstance:u,__deleteInstance:f};return d}}function Lo(e={}){const t=Yt();if(t==null)throw Me(Ae.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Me(Ae.NOT_INSLALLED);const n=rv(t),r=iv(n),s=Du(t),i=sv(e,s);if(__VUE_I18N_LEGACY_API__&&n.mode==="legacy"&&!e.__useComponent){if(!n.allowComposition)throw Me(Ae.NOT_AVAILABLE_IN_LEGACY_MODE);return lv(t,i,r,e)}if(i==="global")return Bu(r,e,s),r;if(i==="parent"){let a=ov(n,t,e.__useComponent);return a==null&&(a=r),a}const o=n;let l=o.__getInstance(t);if(l==null){const a=Be({},e);"__i18n"in s&&(a.__i18n=s.__i18n),r&&(a.__root=r),l=xo(a),av(o,t),o.__setInstance(t,l)}return l}function nv(e,t,n){const r=Zi();{const s=__VUE_I18N_LEGACY_API__&&t?r.run(()=>Fi(e)):r.run(()=>xo(e));if(s==null)throw Me(Ae.UNEXPECTED_ERROR);return[r,s]}}function rv(e){{const t=Ze(e.isCE?tv:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Me(e.isCE?Ae.NOT_INSLALLED_WITH_PROVIDE:Ae.UNEXPECTED_ERROR);return t}}function sv(e,t){return xs(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function iv(e){return e.mode==="composition"?e.global:e.global.__composer}function ov(e,t,n=!1){let r=null;const s=t.root;let i=t.parent;for(;i!=null;){const o=e;if(e.mode==="composition")r=o.__getInstance(i);else if(__VUE_I18N_LEGACY_API__){const l=o.__getInstance(i);l!=null&&(r=l.__composer,n&&r&&!r[ku]&&(r=null))}if(r!=null||s===i)break;i=i.parent}return r}function av(e,t,n){Jt(()=>{},t),wr(()=>{e.__deleteInstance(t)},t)}function lv(e,t,n,r={}){const s=t==="local",i=hc(null);if(s&&e.proxy&&!(e.proxy.$options.i18n||e.proxy.$options.__i18n))throw Me(Ae.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const o=ce(r.inheritLocale)?r.inheritLocale:!0,l=Q(s&&o?n.locale.value:G(r.locale)?r.locale:Pr),a=Q(s&&o?n.fallbackLocale.value:G(r.fallbackLocale)||ve(r.fallbackLocale)||te(r.fallbackLocale)||r.fallbackLocale===!1?r.fallbackLocale:l.value),c=Q(As(l.value,r)),u=Q(te(r.datetimeFormats)?r.datetimeFormats:{[l.value]:{}}),f=Q(te(r.numberFormats)?r.numberFormats:{[l.value]:{}}),d=s?n.missingWarn:ce(r.missingWarn)||qt(r.missingWarn)?r.missingWarn:!0,h=s?n.fallbackWarn:ce(r.fallbackWarn)||qt(r.fallbackWarn)?r.fallbackWarn:!0,v=s?n.fallbackRoot:ce(r.fallbackRoot)?r.fallbackRoot:!0,b=!!r.fallbackFormat,C=xe(r.missing)?r.missing:null,g=xe(r.postTranslation)?r.postTranslation:null,w=s?n.warnHtmlMessage:ce(r.warnHtmlMessage)?r.warnHtmlMessage:!0,T=!!r.escapeParameter,E=s?n.modifiers:te(r.modifiers)?r.modifiers:{},S=r.pluralRules||s&&n.pluralRules;function N(){return[l.value,a.value,c.value,u.value,f.value]}const M=fe({get:()=>i.value?i.value.locale.value:l.value,set:m=>{i.value&&(i.value.locale.value=m),l.value=m}}),F=fe({get:()=>i.value?i.value.fallbackLocale.value:a.value,set:m=>{i.value&&(i.value.fallbackLocale.value=m),a.value=m}}),x=fe(()=>i.value?i.value.messages.value:c.value),W=fe(()=>u.value),P=fe(()=>f.value);function A(){return i.value?i.value.getPostTranslationHandler():g}function H(m){i.value&&i.value.setPostTranslationHandler(m)}function J(){return i.value?i.value.getMissingHandler():C}function re(m){i.value&&i.value.setMissingHandler(m)}function X(m){return N(),m()}function ne(...m){return i.value?X(()=>Reflect.apply(i.value.t,null,[...m])):X(()=>"")}function ie(...m){return i.value?Reflect.apply(i.value.rt,null,[...m]):""}function we(...m){return i.value?X(()=>Reflect.apply(i.value.d,null,[...m])):X(()=>"")}function Le(...m){return i.value?X(()=>Reflect.apply(i.value.n,null,[...m])):X(()=>"")}function Te(m){return i.value?i.value.tm(m):{}}function Se(m,y){return i.value?i.value.te(m,y):!1}function Ye(m){return i.value?i.value.getLocaleMessage(m):{}}function qe(m,y){i.value&&(i.value.setLocaleMessage(m,y),c.value[m]=y)}function ft(m,y){i.value&&i.value.mergeLocaleMessage(m,y)}function Ce(m){return i.value?i.value.getDateTimeFormat(m):{}}function k(m,y){i.value&&(i.value.setDateTimeFormat(m,y),u.value[m]=y)}function z(m,y){i.value&&i.value.mergeDateTimeFormat(m,y)}function V(m){return i.value?i.value.getNumberFormat(m):{}}function K(m,y){i.value&&(i.value.setNumberFormat(m,y),f.value[m]=y)}function ae(m,y){i.value&&i.value.mergeNumberFormat(m,y)}const _={get id(){return i.value?i.value.id:-1},locale:M,fallbackLocale:F,messages:x,datetimeFormats:W,numberFormats:P,get inheritLocale(){return i.value?i.value.inheritLocale:o},set inheritLocale(m){i.value&&(i.value.inheritLocale=m)},get availableLocales(){return i.value?i.value.availableLocales:Object.keys(c.value)},get modifiers(){return i.value?i.value.modifiers:E},get pluralRules(){return i.value?i.value.pluralRules:S},get isGlobal(){return i.value?i.value.isGlobal:!1},get missingWarn(){return i.value?i.value.missingWarn:d},set missingWarn(m){i.value&&(i.value.missingWarn=m)},get fallbackWarn(){return i.value?i.value.fallbackWarn:h},set fallbackWarn(m){i.value&&(i.value.missingWarn=m)},get fallbackRoot(){return i.value?i.value.fallbackRoot:v},set fallbackRoot(m){i.value&&(i.value.fallbackRoot=m)},get fallbackFormat(){return i.value?i.value.fallbackFormat:b},set fallbackFormat(m){i.value&&(i.value.fallbackFormat=m)},get warnHtmlMessage(){return i.value?i.value.warnHtmlMessage:w},set warnHtmlMessage(m){i.value&&(i.value.warnHtmlMessage=m)},get escapeParameter(){return i.value?i.value.escapeParameter:T},set escapeParameter(m){i.value&&(i.value.escapeParameter=m)},t:ne,getPostTranslationHandler:A,setPostTranslationHandler:H,getMissingHandler:J,setMissingHandler:re,rt:ie,d:we,n:Le,tm:Te,te:Se,getLocaleMessage:Ye,setLocaleMessage:qe,mergeLocaleMessage:ft,getDateTimeFormat:Ce,setDateTimeFormat:k,mergeDateTimeFormat:z,getNumberFormat:V,setNumberFormat:K,mergeNumberFormat:ae};function p(m){m.locale.value=l.value,m.fallbackLocale.value=a.value,Object.keys(c.value).forEach(y=>{m.mergeLocaleMessage(y,c.value[y])}),Object.keys(u.value).forEach(y=>{m.mergeDateTimeFormat(y,u.value[y])}),Object.keys(f.value).forEach(y=>{m.mergeNumberFormat(y,f.value[y])}),m.escapeParameter=T,m.fallbackFormat=b,m.fallbackRoot=v,m.fallbackWarn=h,m.missingWarn=d,m.warnHtmlMessage=w}return Nc(()=>{if(e.proxy==null||e.proxy.$i18n==null)throw Me(Ae.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const m=i.value=e.proxy.$i18n.__composer;t==="global"?(l.value=m.locale.value,a.value=m.fallbackLocale.value,c.value=m.messages.value,u.value=m.datetimeFormats.value,f.value=m.numberFormats.value):s&&p(m)}),_}const cv=["locale","fallbackLocale","availableLocales"],uv=["t","rt","d","n","tm"];function fv(e,t){const n=Object.create(null);cv.forEach(r=>{const s=Object.getOwnPropertyDescriptor(t,r);if(!s)throw Me(Ae.UNEXPECTED_ERROR);const i=Ee(s.value)?{get(){return s.value.value},set(o){s.value.value=o}}:{get(){return s.get&&s.get()}};Object.defineProperty(n,r,i)}),e.config.globalProperties.$i18n=n,uv.forEach(r=>{const s=Object.getOwnPropertyDescriptor(t,r);if(!s||!s.value)throw Me(Ae.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,"$".concat(r),s)})}Rg(jg);Mg(hg);Fg(Su);Gg();if(__INTLIFY_PROD_DEVTOOLS__){const e=sr();e.__INTLIFY__=!0,Tg(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}function dv(){}const bn=Object.assign,Hu=typeof window<"u",Is=e=>e!==null&&typeof e=="object",Xt=e=>e!=null,ki=e=>typeof e=="function",pv=e=>Is(e)&&ki(e.then)&&ki(e.catch),Uu=e=>typeof e=="number"||/^\d+(\.\d+)?$/.test(e),mv=()=>Hu?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function ul(e,t){const n=t.split(".");let r=e;return n.forEach(s=>{var i;r=Is(r)&&(i=r[s])!=null?i:""}),r}function hv(e,t,n){return t.reduce((r,s)=>((!n||e[s]!==void 0)&&(r[s]=e[s]),r),{})}const fl=(e,t)=>JSON.stringify(e)===JSON.stringify(t),ns=null,Ve=[Number,String],fn={type:Boolean,default:!0},gv=e=>({type:Number,default:e}),qs=e=>({type:Ve,default:e}),_t=e=>({type:String,default:e});var Ns=typeof window<"u",vv=e=>e===window,dl=(e,t)=>({top:0,left:0,right:e,bottom:t,width:e,height:t}),pl=e=>{const t=yt(e);if(vv(t)){const n=t.innerWidth,r=t.innerHeight;return dl(n,r)}return t!=null&&t.getBoundingClientRect?t.getBoundingClientRect():dl(0,0)};function Vu(e){let t;Jt(()=>{e(),Hn(()=>{t=!0})}),co(()=>{t&&e()})}function Rs(e,t,n={}){if(!Ns)return;const{target:r=window,passive:s=!1,capture:i=!1}=n;let o=!1,l;const a=f=>{if(o)return;const d=yt(f);d&&!l&&(d.addEventListener(e,t,{capture:i,passive:s}),l=!0)},c=f=>{if(o)return;const d=yt(f);d&&l&&(d.removeEventListener(e,t,i),l=!1)};wr(()=>c(r)),hs(()=>c(r)),Vu(()=>a(r));let u;return Ee(r)&&(u=De(r,(f,d)=>{c(d),a(f)})),()=>{u==null||u(),c(r),o=!0}}function SE(e,t,n={}){if(!Ns)return;const{eventName:r="click"}=n;Rs(r,i=>{(Array.isArray(e)?e:[e]).every(a=>{const c=yt(a);return c&&!c.contains(i.target)})&&t(i)},{target:document})}var Dr,Xs;function bv(){if(!Dr&&(Dr=Q(0),Xs=Q(0),Ns)){const e=()=>{Dr.value=window.innerWidth,Xs.value=window.innerHeight};e(),window.addEventListener("resize",e,{passive:!0}),window.addEventListener("orientationchange",e,{passive:!0})}return{width:Dr,height:Xs}}var yv=/scroll|auto|overlay/i,_v=Ns?window:void 0;function Ev(e){return e.tagName!=="HTML"&&e.tagName!=="BODY"&&e.nodeType===1}function wv(e,t=_v){let n=e;for(;n&&n!==t&&Ev(n);){const{overflowY:r}=window.getComputedStyle(n);if(yv.test(r))return n;n=n.parentNode}return t}var Sv=Symbol("van-field");function Tv(e){const t=Ze(Sv,null);t&&!t.customValue.value&&(t.customValue.value=e,De(e,()=>{t.resetValidation(),t.validateWithTrigger("onChange")}))}mv();const Wu=e=>e.stopPropagation();function Ao(e,t){(typeof e.cancelable!="boolean"||e.cancelable)&&e.preventDefault(),t&&Wu(e)}bv();function rt(e){if(Xt(e))return Uu(e)?"".concat(e,"px"):String(e)}function zu(e){if(Xt(e)){if(Array.isArray(e))return{width:rt(e[0]),height:rt(e[1])};const t=rt(e);return{width:t,height:t}}}function Cv(e){const t={};return e!==void 0&&(t.zIndex=+e),t}const Ov=/-(\w)/g,Gu=e=>e.replace(Ov,(t,n)=>n.toUpperCase()),xv=(e,t,n)=>Math.min(Math.max(e,t),n);function Pv(e,t){return Math.round((e+t)*1e10)/1e10}const{hasOwnProperty:Lv}=Object.prototype;function Av(e,t,n){const r=t[n];Xt(r)&&(!Lv.call(e,n)||!Is(r)?e[n]=r:e[n]=Ku(Object(e[n]),r))}function Ku(e,t){return Object.keys(t).forEach(n=>{Av(e,t,n)}),e}var Iv={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(e,t)=>"".concat(e,"年").concat(t,"月"),rangePrompt:e=>"最多选择 ".concat(e," 天")},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:e=>"".concat(e,"折"),condition:e=>"满".concat(e,"元可用")},vanCouponCell:{title:"优惠券",count:e=>"".concat(e,"张可用")},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const ml=Q("zh-CN"),hl=It({"zh-CN":Iv}),Nv={messages(){return hl[ml.value]},use(e,t){ml.value=e,this.add({[e]:t})},add(e={}){Ku(hl,e)}};var Rv=Nv;function Mv(e){const t=Gu(e)+".";return(n,...r)=>{const s=Rv.messages(),i=ul(s,t+n)||ul(s,n);return ki(i)?i(...r):i}}function Di(e,t){return t?typeof t=="string"?" ".concat(e,"--").concat(t):Array.isArray(t)?t.reduce((n,r)=>n+Di(e,r),""):Object.keys(t).reduce((n,r)=>n+(t[r]?Di(e,r):""),""):""}function Fv(e){return(t,n)=>(t&&typeof t!="string"&&(n=t,t=""),t=t?"".concat(e,"__").concat(t):e,"".concat(t).concat(Di(t,n)))}function tn(e){const t="van-".concat(e);return[t,Fv(t),Mv(t)]}const kv="van-haptics-feedback",gl=5;function Dv(e,{args:t=[],done:n,canceled:r}){if(e){const s=e.apply(null,t);pv(s)?s.then(i=>{i?n():r&&r()}).catch(dv):s?n():r&&r()}else n()}function yn(e){return e.install=t=>{const{name:n}=e;n&&(t.component(n,e),t.component(Gu("-".concat(n)),e))},e}const Bv=Symbol();function Yu(e){const t=Yt();t&&bn(t.proxy,e)}const[$v,vl]=tn("badge"),jv={dot:Boolean,max:Ve,tag:_t("div"),color:String,offset:Array,content:Ve,showZero:fn,position:_t("top-right")};var Hv=Nt({name:$v,props:jv,setup(e,{slots:t}){const n=()=>{if(t.content)return!0;const{content:l,showZero:a}=e;return Xt(l)&&l!==""&&(a||l!==0&&l!=="0")},r=()=>{const{dot:l,max:a,content:c}=e;if(!l&&n())return t.content?t.content():Xt(a)&&Uu(c)&&+c>+a?"".concat(a,"+"):c},s=l=>l.startsWith("-")?l.replace("-",""):"-".concat(l),i=fe(()=>{const l={background:e.color};if(e.offset){const[a,c]=e.offset,{position:u}=e,[f,d]=u.split("-");t.default?(typeof c=="number"?l[f]=rt(f==="top"?c:-c):l[f]=f==="top"?rt(c):s(c),typeof a=="number"?l[d]=rt(d==="left"?a:-a):l[d]=d==="left"?rt(a):s(a)):(l.marginTop=rt(c),l.marginLeft=rt(a))}return l}),o=()=>{if(n()||e.dot)return ee("div",{class:vl([e.position,{dot:e.dot,fixed:!!t.default}]),style:i.value},[r()])};return()=>{if(t.default){const{tag:l}=e;return ee(l,{class:vl("wrapper")},{default:()=>[t.default(),o()]})}return o()}}});const Uv=yn(Hv);let Vv=2e3;const Wv=()=>++Vv,[zv,TE]=tn("config-provider"),Gv=Symbol(zv),[Kv,bl]=tn("icon"),Yv=e=>e==null?void 0:e.includes("/"),qv={dot:Boolean,tag:_t("i"),name:String,size:Ve,badge:Ve,color:String,badgeProps:Object,classPrefix:String};var Xv=Nt({name:Kv,props:qv,setup(e,{slots:t}){const n=Ze(Gv,null),r=fe(()=>e.classPrefix||(n==null?void 0:n.iconPrefix)||bl());return()=>{const{tag:s,dot:i,name:o,size:l,badge:a,color:c}=e,u=Yv(o);return ee(Uv,Tr({dot:i,tag:s,class:[r.value,u?"":"".concat(r.value,"-").concat(o)],style:{color:c,fontSize:rt(l)},content:a},e.badgeProps),{default:()=>{var f;return[(f=t.default)==null?void 0:f.call(t),u&&ee("img",{class:bl("image"),src:o},null)]}})}}});const qu=yn(Xv),[Jv,or]=tn("loading"),Qv=Array(12).fill(null).map((e,t)=>ee("i",{class:or("line",String(t+1))},null)),Zv=ee("svg",{class:or("circular"),viewBox:"25 25 50 50"},[ee("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),eb={size:Ve,type:_t("circular"),color:String,vertical:Boolean,textSize:Ve,textColor:String};var tb=Nt({name:Jv,props:eb,setup(e,{slots:t}){const n=fe(()=>bn({color:e.color},zu(e.size))),r=()=>{const i=e.type==="spinner"?Qv:Zv;return ee("span",{class:or("spinner",e.type),style:n.value},[t.icon?t.icon():i])},s=()=>{var i;if(t.default)return ee("span",{class:or("text"),style:{fontSize:rt(e.textSize),color:(i=e.textColor)!=null?i:e.color}},[t.default()])};return()=>{const{type:i,vertical:o}=e;return ee("div",{class:or([i,{vertical:o}]),"aria-live":"polite","aria-busy":!0},[r(),s()])}}});const nb=yn(tb),rb={show:Boolean,zIndex:Ve,overlay:fn,duration:Ve,teleport:[String,Object],lockScroll:fn,lazyRender:fn,beforeClose:Function,overlayStyle:Object,overlayClass:ns,transitionAppear:Boolean,closeOnClickOverlay:fn};function sb(e,t){return e>t?"horizontal":t>e?"vertical":""}function Xu(){const e=Q(0),t=Q(0),n=Q(0),r=Q(0),s=Q(0),i=Q(0),o=Q(""),l=Q(!0),a=()=>o.value==="vertical",c=()=>o.value==="horizontal",u=()=>{n.value=0,r.value=0,s.value=0,i.value=0,o.value="",l.value=!0};return{move:h=>{const v=h.touches[0];n.value=(v.clientX<0?0:v.clientX)-e.value,r.value=v.clientY-t.value,s.value=Math.abs(n.value),i.value=Math.abs(r.value);const b=10;(!o.value||s.value<b&&i.value<b)&&(o.value=sb(s.value,i.value)),l.value&&(s.value>gl||i.value>gl)&&(l.value=!1)},start:h=>{u(),e.value=h.touches[0].clientX,t.value=h.touches[0].clientY},reset:u,startX:e,startY:t,deltaX:n,deltaY:r,offsetX:s,offsetY:i,direction:o,isVertical:a,isHorizontal:c,isTap:l}}let Yn=0;const yl="van-overflow-hidden";function ib(e,t){const n=Xu(),r="01",s="10",i=u=>{n.move(u);const f=n.deltaY.value>0?s:r,d=wv(u.target,e.value),{scrollHeight:h,offsetHeight:v,scrollTop:b}=d;let C="11";b===0?C=v>=h?"00":"01":b+v>=h&&(C="10"),C!=="11"&&n.isVertical()&&!(parseInt(C,2)&parseInt(f,2))&&Ao(u,!0)},o=()=>{document.addEventListener("touchstart",n.start),document.addEventListener("touchmove",i,{passive:!1}),Yn||document.body.classList.add(yl),Yn++},l=()=>{Yn&&(document.removeEventListener("touchstart",n.start),document.removeEventListener("touchmove",i),Yn--,Yn||document.body.classList.remove(yl))},a=()=>t()&&o(),c=()=>t()&&l();Vu(a),hs(c),Er(c),De(t,u=>{u?o():l()})}function Ju(e){const t=Q(!1);return De(e,n=>{n&&(t.value=n)},{immediate:!0}),n=>()=>t.value?n():null}const[ob,ab]=tn("overlay"),lb={show:Boolean,zIndex:Ve,duration:Ve,className:ns,lockScroll:fn,lazyRender:fn,customStyle:Object};var cb=Nt({name:ob,props:lb,setup(e,{slots:t}){const n=Q(),r=Ju(()=>e.show||!e.lazyRender),s=o=>{e.lockScroll&&Ao(o,!0)},i=r(()=>{var o;const l=bn(Cv(e.zIndex),e.customStyle);return Xt(e.duration)&&(l.animationDuration="".concat(e.duration,"s")),xc(ee("div",{ref:n,style:l,class:[ab(),e.className]},[(o=t.default)==null?void 0:o.call(t)]),[[Qc,e.show]])});return Rs("touchmove",s,{target:n}),()=>ee(ys,{name:"van-fade",appear:!0},{default:i})}});const ub=yn(cb),fb=bn({},rb,{round:Boolean,position:_t("center"),closeIcon:_t("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:_t("top-right"),safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[db,_l]=tn("popup");var pb=Nt({name:db,inheritAttrs:!1,props:fb,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(e,{emit:t,attrs:n,slots:r}){let s,i;const o=Q(),l=Q(),a=Ju(()=>e.show||!e.lazyRender),c=fe(()=>{const N={zIndex:o.value};if(Xt(e.duration)){const M=e.position==="center"?"animationDuration":"transitionDuration";N[M]="".concat(e.duration,"s")}return N}),u=()=>{s||(s=!0,o.value=e.zIndex!==void 0?+e.zIndex:Wv(),t("open"))},f=()=>{s&&Dv(e.beforeClose,{done(){s=!1,t("close"),t("update:show",!1)}})},d=N=>{t("clickOverlay",N),e.closeOnClickOverlay&&f()},h=()=>{if(e.overlay)return ee(ub,{show:e.show,class:e.overlayClass,zIndex:o.value,duration:e.duration,customStyle:e.overlayStyle,role:e.closeOnClickOverlay?"button":void 0,tabindex:e.closeOnClickOverlay?0:void 0,onClick:d},{default:r["overlay-content"]})},v=N=>{t("clickCloseIcon",N),f()},b=()=>{if(e.closeable)return ee(qu,{role:"button",tabindex:0,name:e.closeIcon,class:[_l("close-icon",e.closeIconPosition),kv],classPrefix:e.iconPrefix,onClick:v},null)};let C;const g=()=>{C&&clearTimeout(C),C=setTimeout(()=>{t("opened")})},w=()=>t("closed"),T=N=>t("keydown",N),E=a(()=>{var N;const{round:M,position:F,safeAreaInsetTop:x,safeAreaInsetBottom:W}=e;return xc(ee("div",Tr({ref:l,style:c.value,role:"dialog",tabindex:0,class:[_l({round:M,[F]:F}),{"van-safe-area-top":x,"van-safe-area-bottom":W}],onKeydown:T},n),[(N=r.default)==null?void 0:N.call(r),b()]),[[Qc,e.show]])}),S=()=>{const{position:N,transition:M,transitionAppear:F}=e,x=N==="center"?"van-fade":"van-popup-slide-".concat(N);return ee(ys,{name:M||x,appear:F,onAfterEnter:g,onAfterLeave:w},{default:E})};return De(()=>e.show,N=>{N&&!s&&(u(),n.tabindex===0&&Hn(()=>{var M;(M=l.value)==null||M.focus()})),!N&&s&&(s=!1,t("close"))}),Yu({popupRef:l}),ib(l,()=>e.show&&e.lockScroll),Rs("popstate",()=>{e.closeOnPopstate&&(f(),i=!1)}),Jt(()=>{e.show&&u()}),co(()=>{i&&(t("update:show",!0),i=!1)}),hs(()=>{e.show&&e.teleport&&(f(),i=!0)}),pn(Bv,()=>e.show),()=>e.teleport?ee(up,{to:e.teleport},{default:()=>[h(),S()]}):ee(Ge,null,[h(),S()])}});const mb=yn(pb);let qn=0;function hb(e){e?(qn||document.body.classList.add("van-toast--unclickable"),qn++):qn&&(qn--,qn||document.body.classList.remove("van-toast--unclickable"))}const[gb,Sn]=tn("toast"),vb=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay"],bb={icon:String,show:Boolean,type:_t("text"),overlay:Boolean,message:Ve,iconSize:Ve,duration:gv(2e3),position:_t("middle"),teleport:[String,Object],wordBreak:String,className:ns,iconPrefix:String,transition:_t("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:ns,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean};var Qu=Nt({name:gb,props:bb,emits:["update:show"],setup(e,{emit:t,slots:n}){let r,s=!1;const i=()=>{const f=e.show&&e.forbidClick;s!==f&&(s=f,hb(s))},o=f=>t("update:show",f),l=()=>{e.closeOnClick&&o(!1)},a=()=>clearTimeout(r),c=()=>{const{icon:f,type:d,iconSize:h,iconPrefix:v,loadingType:b}=e;if(f||d==="success"||d==="fail")return ee(qu,{name:f||d,size:h,class:Sn("icon"),classPrefix:v},null);if(d==="loading")return ee(nb,{class:Sn("loading"),size:h,type:b},null)},u=()=>{const{type:f,message:d}=e;if(n.message)return ee("div",{class:Sn("text")},[n.message()]);if(Xt(d)&&d!=="")return f==="html"?ee("div",{key:0,class:Sn("text"),innerHTML:String(d)},null):ee("div",{class:Sn("text")},[d])};return De(()=>[e.show,e.forbidClick],i),De(()=>[e.show,e.type,e.message,e.duration],()=>{a(),e.show&&e.duration>0&&(r=setTimeout(()=>{o(!1)},e.duration))}),Jt(i),wr(i),()=>ee(mb,Tr({class:[Sn([e.position,e.wordBreak==="normal"?"break-normal":e.wordBreak,{[e.type]:!e.icon}]),e.className],lockScroll:!1,onClick:l,onClosed:a,"onUpdate:show":o},hv(e,vb)),{default:()=>[c(),u()]})}});function yb(){const e=It({show:!1}),t=s=>{e.show=s},n=s=>{bn(e,s,{transitionAppear:!0}),t(!0)},r=()=>t(!1);return Yu({open:n,close:r,toggle:t}),{open:n,close:r,state:e,toggle:t}}function _b(e){const t=Zp(e),n=document.createElement("div");return document.body.appendChild(n),{instance:t.mount(n),unmount(){t.unmount(),document.body.removeChild(n)}}}const Eb={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let Br=[],wb=!1,El=bn({},Eb);const Sb=new Map;function Tb(e){return Is(e)?e:{message:e}}function Cb(){const{instance:e,unmount:t}=_b({setup(){const n=Q(""),{open:r,state:s,close:i,toggle:o}=yb(),l=()=>{},a=()=>ee(Qu,Tr(s,{onClosed:l,"onUpdate:show":o}),null);return De(n,c=>{s.message=c}),Yt().render=a,{open:r,close:i,message:n}}});return e}function Ob(){if(!Br.length||wb){const e=Cb();Br.push(e)}return Br[Br.length-1]}function CE(e={}){if(!Hu)return{};const t=Ob(),n=Tb(e);return t.open(bn({},El,Sb.get(n.type||El.type),n)),t}yn(Qu);const[xb,Xn]=tn("slider"),Pb={min:qs(0),max:qs(100),step:qs(1),range:Boolean,reverse:Boolean,disabled:Boolean,readonly:Boolean,vertical:Boolean,barHeight:Ve,buttonSize:Ve,activeColor:String,inactiveColor:String,modelValue:{type:[Number,Array],default:0}};var Lb=Nt({name:xb,props:Pb,emits:["change","dragEnd","dragStart","update:modelValue"],setup(e,{emit:t,slots:n}){let r,s,i;const o=Q(),l=[Q(),Q()],a=Q(),c=Xu(),u=fe(()=>Number(e.max)-Number(e.min)),f=fe(()=>{const P=e.vertical?"width":"height";return{background:e.inactiveColor,[P]:rt(e.barHeight)}}),d=P=>e.range&&Array.isArray(P),h=()=>{const{modelValue:P,min:A}=e;return d(P)?"".concat((P[1]-P[0])*100/u.value,"%"):"".concat((P-Number(A))*100/u.value,"%")},v=()=>{const{modelValue:P,min:A}=e;return d(P)?"".concat((P[0]-Number(A))*100/u.value,"%"):"0%"},b=fe(()=>{const A={[e.vertical?"height":"width"]:h(),background:e.activeColor};a.value&&(A.transition="none");const H=()=>e.vertical?e.reverse?"bottom":"top":e.reverse?"right":"left";return A[H()]=v(),A}),C=P=>{const A=+e.min,H=+e.max,J=+e.step;P=xv(P,A,H);const re=Math.round((P-A)/J)*J;return Pv(A,re)},g=()=>{const P=e.modelValue;d(P)?i=P.map(C):i=C(P)},w=P=>{var A,H;const J=(A=P[0])!=null?A:Number(e.min),re=(H=P[1])!=null?H:Number(e.max);return J>re?[re,J]:[J,re]},T=(P,A)=>{d(P)?P=w(P).map(C):P=C(P),fl(P,e.modelValue)||t("update:modelValue",P),A&&!fl(P,i)&&t("change",P)},E=P=>{if(P.stopPropagation(),e.disabled||e.readonly)return;g();const{min:A,reverse:H,vertical:J,modelValue:re}=e,X=pl(o),ne=()=>J?H?X.bottom-P.clientY:P.clientY-X.top:H?X.right-P.clientX:P.clientX-X.left,ie=J?X.height:X.width,we=Number(A)+ne()/ie*u.value;if(d(re)){const[Le,Te]=re,Se=(Le+Te)/2;we<=Se?T([we,Te],!0):T([Le,we],!0)}else T(we,!0)},S=P=>{e.disabled||e.readonly||(c.start(P),s=e.modelValue,g(),a.value="start")},N=P=>{if(e.disabled||e.readonly)return;a.value==="start"&&t("dragStart",P),Ao(P,!0),c.move(P),a.value="dragging";const A=pl(o),H=e.vertical?c.deltaY.value:c.deltaX.value,J=e.vertical?A.height:A.width;let re=H/J*u.value;if(e.reverse&&(re=-re),d(i)){const X=e.reverse?1-r:r;s[X]=i[X]+re}else s=i+re;T(s)},M=P=>{e.disabled||e.readonly||(a.value==="dragging"&&(T(s,!0),t("dragEnd",P)),a.value="")},F=P=>typeof P=="number"?Xn("button-wrapper",["left","right"][P]):Xn("button-wrapper",e.reverse?"left":"right"),x=(P,A)=>{const H=a.value==="dragging";if(typeof A=="number"){const J=n[A===0?"left-button":"right-button"];let re;if(H&&Array.isArray(s)&&(re=s[0]>s[1]?r^1:r),J)return J({value:P,dragging:H,dragIndex:re})}return n.button?n.button({value:P,dragging:H}):ee("div",{class:Xn("button"),style:zu(e.buttonSize)},null)},W=P=>{const A=typeof P=="number"?e.modelValue[P]:e.modelValue;return ee("div",{ref:l[P!=null?P:0],role:"slider",class:F(P),tabindex:e.disabled?void 0:0,"aria-valuemin":e.min,"aria-valuenow":A,"aria-valuemax":e.max,"aria-disabled":e.disabled||void 0,"aria-readonly":e.readonly||void 0,"aria-orientation":e.vertical?"vertical":"horizontal",onTouchstartPassive:H=>{typeof P=="number"&&(r=P),S(H)},onTouchend:M,onTouchcancel:M,onClick:Wu},[x(A,P)])};return T(e.modelValue),Tv(()=>e.modelValue),l.forEach(P=>{Rs("touchmove",N,{target:P})}),()=>ee("div",{ref:o,style:f.value,class:Xn({vertical:e.vertical,disabled:e.disabled}),onClick:E},[ee("div",{class:Xn("bar"),style:b.value},[e.range?[W(0),W(1)]:W()])])}});const OE=yn(Lb);(function(){if(typeof window>"u")return;var e,t="ontouchstart"in window;document.createTouch||(document.createTouch=function(u,f,d,h,v,b,C){return new n(f,d,{pageX:h,pageY:v,screenX:b,screenY:C,clientX:h-window.pageXOffset,clientY:v-window.pageYOffset},0,0)}),document.createTouchList||(document.createTouchList=function(){for(var u=r(),f=0;f<arguments.length;f++)u[f]=arguments[f];return u.length=arguments.length,u}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(u){var f=this;do{if(f.matches(u))return f;f=f.parentElement||f.parentNode}while(f!==null&&f.nodeType===1);return null});var n=function(f,d,h,v,b){v=v||0,b=b||0,this.identifier=d,this.target=f,this.clientX=h.clientX+v,this.clientY=h.clientY+b,this.screenX=h.screenX+v,this.screenY=h.screenY+b,this.pageX=h.pageX+v,this.pageY=h.pageY+b};function r(){var u=[];return u.item=function(f){return this[f]||null},u.identifiedTouch=function(f){return this[f+1]||null},u}var s=!1;function i(u){return function(f){f.type==="mousedown"&&(s=!0),f.type==="mouseup"&&(s=!1),!(f.type==="mousemove"&&!s)&&((f.type==="mousedown"||!e||e&&!e.dispatchEvent)&&(e=f.target),e.closest("[data-no-touch-simulate]")==null&&o(u,f),f.type==="mouseup"&&(e=null))}}function o(u,f){var d=document.createEvent("Event");d.initEvent(u,!0,!0),d.altKey=f.altKey,d.ctrlKey=f.ctrlKey,d.metaKey=f.metaKey,d.shiftKey=f.shiftKey,d.touches=a(f),d.targetTouches=a(f),d.changedTouches=l(f),e.dispatchEvent(d)}function l(u){var f=r();return f.push(new n(e,1,u,0,0)),f}function a(u){return u.type==="mouseup"?r():l(u)}function c(){window.addEventListener("mousedown",i("touchstart"),!0),window.addEventListener("mousemove",i("touchmove"),!0),window.addEventListener("mouseup",i("touchend"),!0)}c.multiTouchOffset=75,t||new c})();var Ab=!1;/*!
 * pinia v2.1.6
 * (c) 2023 Eduardo San Martin Morote
 * @license MIT
 */let Zu;const Ms=e=>Zu=e,ef=Symbol();function Bi(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var ar;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(ar||(ar={}));function xE(){const e=Zi(!0),t=e.run(()=>Q({}));let n=[],r=[];const s=us({install(i){Ms(s),s._a=i,i.provide(ef,s),i.config.globalProperties.$pinia=s,r.forEach(o=>n.push(o)),r=[]},use(i){return!this._a&&!Ab?r.push(i):n.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return s}const tf=()=>{};function wl(e,t,n,r=tf){e.push(t);const s=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),r())};return!n&&Zl()&&$f(s),s}function Tn(e,...t){e.slice().forEach(n=>{n(...t)})}const Ib=e=>e();function $i(e,t){e instanceof Map&&t instanceof Map&&t.forEach((n,r)=>e.set(r,n)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const r=t[n],s=e[n];Bi(s)&&Bi(r)&&e.hasOwnProperty(n)&&!Ee(r)&&!Pt(r)?e[n]=$i(s,r):e[n]=r}return e}const Nb=Symbol();function Rb(e){return!Bi(e)||!e.hasOwnProperty(Nb)}const{assign:jt}=Object;function Mb(e){return!!(Ee(e)&&e.effect)}function Fb(e,t,n,r){const{state:s,actions:i,getters:o}=t,l=n.state.value[e];let a;function c(){l||(n.state.value[e]=s?s():{});const u=bc(n.state.value[e]);return jt(u,i,Object.keys(o||{}).reduce((f,d)=>(f[d]=us(fe(()=>{Ms(n);const h=n._s.get(e);return o[d].call(h,h)})),f),{}))}return a=nf(e,c,t,n,r,!0),a}function nf(e,t,n={},r,s,i){let o;const l=jt({actions:{}},n),a={deep:!0};let c,u,f=[],d=[],h;const v=r.state.value[e];!i&&!v&&(r.state.value[e]={}),Q({});let b;function C(F){let x;c=u=!1,typeof F=="function"?(F(r.state.value[e]),x={type:ar.patchFunction,storeId:e,events:h}):($i(r.state.value[e],F),x={type:ar.patchObject,payload:F,storeId:e,events:h});const W=b=Symbol();Hn().then(()=>{b===W&&(c=!0)}),u=!0,Tn(f,x,r.state.value[e])}const g=i?function(){const{state:x}=n,W=x?x():{};this.$patch(P=>{jt(P,W)})}:tf;function w(){o.stop(),f=[],d=[],r._s.delete(e)}function T(F,x){return function(){Ms(r);const W=Array.from(arguments),P=[],A=[];function H(X){P.push(X)}function J(X){A.push(X)}Tn(d,{args:W,name:F,store:S,after:H,onError:J});let re;try{re=x.apply(this&&this.$id===e?this:S,W)}catch(X){throw Tn(A,X),X}return re instanceof Promise?re.then(X=>(Tn(P,X),X)).catch(X=>(Tn(A,X),Promise.reject(X))):(Tn(P,re),re)}}const E={_p:r,$id:e,$onAction:wl.bind(null,d),$patch:C,$reset:g,$subscribe(F,x={}){const W=wl(f,F,x.detached,()=>P()),P=o.run(()=>De(()=>r.state.value[e],A=>{(x.flush==="sync"?u:c)&&F({storeId:e,type:ar.direct,events:h},A)},jt({},a,x)));return W},$dispose:w},S=It(E);r._s.set(e,S);const N=r._a&&r._a.runWithContext||Ib,M=r._e.run(()=>(o=Zi(),N(()=>o.run(t))));for(const F in M){const x=M[F];if(Ee(x)&&!Mb(x)||Pt(x))i||(v&&Rb(x)&&(Ee(x)?x.value=v[F]:$i(x,v[F])),r.state.value[e][F]=x);else if(typeof x=="function"){const W=T(F,x);M[F]=W,l.actions[F]=x}}return jt(S,M),jt(ue(S),M),Object.defineProperty(S,"$state",{get:()=>r.state.value[e],set:F=>{C(x=>{jt(x,F)})}}),r._p.forEach(F=>{jt(S,o.run(()=>F({store:S,app:r._a,pinia:r,options:l})))}),v&&i&&n.hydrate&&n.hydrate(S.$state,v),c=!0,u=!0,S}function PE(e,t,n){let r,s;const i=typeof t=="function";typeof e=="string"?(r=e,s=i?n:t):(s=e,r=e.id);function o(l,a){const c=Qd();return l=l||(c?Ze(ef,null):null),l&&Ms(l),l=Zu,l._s.has(r)||(i?nf(r,t,s,l):Fb(r,s,l)),l._s.get(r)}return o.$id=r,o}function LE(e){{e=ue(e);const t={};for(const n in e){const r=e[n];(Ee(r)||Pt(r))&&(t[n]=vd(e,n))}return t}}/*!
  * vue-router v4.2.4
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */const On=typeof window<"u";function kb(e){return e.__esModule||e[Symbol.toStringTag]==="Module"}const ge=Object.assign;function Js(e,t){const n={};for(const r in t){const s=t[r];n[r]=ut(s)?s.map(e):e(s)}return n}const lr=()=>{},ut=Array.isArray,Db=/\/$/,Bb=e=>e.replace(Db,"");function Qs(e,t,n="/"){let r,s={},i="",o="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(r=t.slice(0,a),i=t.slice(a+1,l>-1?l:t.length),s=e(i)),l>-1&&(r=r||t.slice(0,l),o=t.slice(l,t.length)),r=Ub(r!=null?r:t,n),{fullPath:r+(i&&"?")+i+o,path:r,query:s,hash:o}}function $b(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Sl(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function jb(e,t,n){const r=t.matched.length-1,s=n.matched.length-1;return r>-1&&r===s&&kn(t.matched[r],n.matched[s])&&rf(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function kn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function rf(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Hb(e[n],t[n]))return!1;return!0}function Hb(e,t){return ut(e)?Tl(e,t):ut(t)?Tl(t,e):e===t}function Tl(e,t){return ut(t)?e.length===t.length&&e.every((n,r)=>n===t[r]):e.length===1&&e[0]===t}function Ub(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/"),s=r[r.length-1];(s===".."||s===".")&&r.push("");let i=n.length-1,o,l;for(o=0;o<r.length;o++)if(l=r[o],l!==".")if(l==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+r.slice(o-(o===r.length?1:0)).join("/")}var _r;(function(e){e.pop="pop",e.push="push"})(_r||(_r={}));var cr;(function(e){e.back="back",e.forward="forward",e.unknown=""})(cr||(cr={}));function Vb(e){if(!e)if(On){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Bb(e)}const Wb=/^[^#]+#/;function zb(e,t){return e.replace(Wb,"#")+t}function Gb(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const Fs=()=>({left:window.pageXOffset,top:window.pageYOffset});function Kb(e){let t;if("el"in e){const n=e.el,r=typeof n=="string"&&n.startsWith("#"),s=typeof n=="string"?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!s)return;t=Gb(s,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.pageXOffset,t.top!=null?t.top:window.pageYOffset)}function Cl(e,t){return(history.state?history.state.position-t:-1)+e}const ji=new Map;function Yb(e,t){ji.set(e,t)}function qb(e){const t=ji.get(e);return ji.delete(e),t}let Xb=()=>location.protocol+"//"+location.host;function sf(e,t){const{pathname:n,search:r,hash:s}=t,i=e.indexOf("#");if(i>-1){let l=s.includes(e.slice(i))?e.slice(i).length:1,a=s.slice(l);return a[0]!=="/"&&(a="/"+a),Sl(a,"")}return Sl(n,e)+r+s}function Jb(e,t,n,r){let s=[],i=[],o=null;const l=({state:d})=>{const h=sf(e,location),v=n.value,b=t.value;let C=0;if(d){if(n.value=h,t.value=d,o&&o===v){o=null;return}C=b?d.position-b.position:0}else r(h);s.forEach(g=>{g(n.value,v,{delta:C,type:_r.pop,direction:C?C>0?cr.forward:cr.back:cr.unknown})})};function a(){o=n.value}function c(d){s.push(d);const h=()=>{const v=s.indexOf(d);v>-1&&s.splice(v,1)};return i.push(h),h}function u(){const{history:d}=window;d.state&&d.replaceState(ge({},d.state,{scroll:Fs()}),"")}function f(){for(const d of i)d();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:a,listen:c,destroy:f}}function Ol(e,t,n,r=!1,s=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:s?Fs():null}}function Qb(e){const{history:t,location:n}=window,r={value:sf(e,n)},s={value:t.state};s.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(a,c,u){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:Xb()+e+a;try{t[u?"replaceState":"pushState"](c,"",d),s.value=c}catch(h){console.error(h),n[u?"replace":"assign"](d)}}function o(a,c){const u=ge({},t.state,Ol(s.value.back,a,s.value.forward,!0),c,{position:s.value.position});i(a,u,!0),r.value=a}function l(a,c){const u=ge({},s.value,t.state,{forward:a,scroll:Fs()});i(u.current,u,!0);const f=ge({},Ol(r.value,a,null),{position:u.position+1},c);i(a,f,!1),r.value=a}return{location:r,state:s,push:l,replace:o}}function AE(e){e=Vb(e);const t=Qb(e),n=Jb(e,t.state,t.location,t.replace);function r(i,o=!0){o||n.pauseListeners(),history.go(i)}const s=ge({location:"",base:e,go:r,createHref:zb.bind(null,e)},t,n);return Object.defineProperty(s,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(s,"state",{enumerable:!0,get:()=>t.state.value}),s}function Zb(e){return typeof e=="string"||e&&typeof e=="object"}function of(e){return typeof e=="string"||typeof e=="symbol"}const $t={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},af=Symbol("");var xl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(xl||(xl={}));function Dn(e,t){return ge(new Error,{type:e,[af]:!0},t)}function Tt(e,t){return e instanceof Error&&af in e&&(t==null||!!(e.type&t))}const Pl="[^/]+?",ey={sensitive:!1,strict:!1,start:!0,end:!0},ty=/[.+*?^${}()[\]/\\]/g;function ny(e,t){const n=ge({},ey,t),r=[];let s=n.start?"^":"";const i=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(s+="/");for(let f=0;f<c.length;f++){const d=c[f];let h=40+(n.sensitive?.25:0);if(d.type===0)f||(s+="/"),s+=d.value.replace(ty,"\\$&"),h+=40;else if(d.type===1){const{value:v,repeatable:b,optional:C,regexp:g}=d;i.push({name:v,repeatable:b,optional:C});const w=g||Pl;if(w!==Pl){h+=10;try{new RegExp("(".concat(w,")"))}catch(E){throw new Error('Invalid custom RegExp for param "'.concat(v,'" (').concat(w,"): ")+E.message)}}let T=b?"((?:".concat(w,")(?:/(?:").concat(w,"))*)"):"(".concat(w,")");f||(T=C&&c.length<2?"(?:/".concat(T,")"):"/"+T),C&&(T+="?"),s+=T,h+=20,C&&(h+=-8),b&&(h+=-20),w===".*"&&(h+=-50)}u.push(h)}r.push(u)}if(n.strict&&n.end){const c=r.length-1;r[c][r[c].length-1]+=.7000000000000001}n.strict||(s+="/?"),n.end?s+="$":n.strict&&(s+="(?:/|$)");const o=new RegExp(s,n.sensitive?"":"i");function l(c){const u=c.match(o),f={};if(!u)return null;for(let d=1;d<u.length;d++){const h=u[d]||"",v=i[d-1];f[v.name]=h&&v.repeatable?h.split("/"):h}return f}function a(c){let u="",f=!1;for(const d of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const h of d)if(h.type===0)u+=h.value;else if(h.type===1){const{value:v,repeatable:b,optional:C}=h,g=v in c?c[v]:"";if(ut(g)&&!b)throw new Error('Provided param "'.concat(v,'" is an array but it is not repeatable (* or + modifiers)'));const w=ut(g)?g.join("/"):g;if(!w)if(C)d.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error('Missing required param "'.concat(v,'"'));u+=w}}return u||"/"}return{re:o,score:r,keys:i,parse:l,stringify:a}}function ry(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function sy(e,t){let n=0;const r=e.score,s=t.score;for(;n<r.length&&n<s.length;){const i=ry(r[n],s[n]);if(i)return i;n++}if(Math.abs(s.length-r.length)===1){if(Ll(r))return 1;if(Ll(s))return-1}return s.length-r.length}function Ll(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const iy={type:0,value:""},oy=/[a-zA-Z0-9_]/;function ay(e){if(!e)return[[]];if(e==="/")return[[iy]];if(!e.startsWith("/"))throw new Error('Invalid path "'.concat(e,'"'));function t(h){throw new Error("ERR (".concat(n,')/"').concat(c,'": ').concat(h))}let n=0,r=n;const s=[];let i;function o(){i&&s.push(i),i=[]}let l=0,a,c="",u="";function f(){c&&(n===0?i.push({type:0,value:c}):n===1||n===2||n===3?(i.length>1&&(a==="*"||a==="+")&&t("A repeatable param (".concat(c,") must be alone in its segment. eg: '/:ids+.")),i.push({type:1,value:c,regexp:u,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),c="")}function d(){c+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){r=n,n=4;continue}switch(n){case 0:a==="/"?(c&&f(),o()):a===":"?(f(),n=1):d();break;case 4:d(),n=r;break;case 1:a==="("?n=2:oy.test(a)?d():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+a:n=3:u+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t('Unfinished custom RegExp for param "'.concat(c,'"')),f(),o(),s}function ly(e,t,n){const r=ny(ay(e.path),n),s=ge(r,{record:e,parent:t,children:[],alias:[]});return t&&!s.record.aliasOf==!t.record.aliasOf&&t.children.push(s),s}function cy(e,t){const n=[],r=new Map;t=Nl({strict:!1,end:!0,sensitive:!1},t);function s(u){return r.get(u)}function i(u,f,d){const h=!d,v=uy(u);v.aliasOf=d&&d.record;const b=Nl(t,u),C=[v];if("alias"in u){const T=typeof u.alias=="string"?[u.alias]:u.alias;for(const E of T)C.push(ge({},v,{components:d?d.record.components:v.components,path:E,aliasOf:d?d.record:v}))}let g,w;for(const T of C){const{path:E}=T;if(f&&E[0]!=="/"){const S=f.record.path,N=S[S.length-1]==="/"?"":"/";T.path=f.record.path+(E&&N+E)}if(g=ly(T,f,b),d?d.alias.push(g):(w=w||g,w!==g&&w.alias.push(g),h&&u.name&&!Il(g)&&o(u.name)),v.children){const S=v.children;for(let N=0;N<S.length;N++)i(S[N],g,d&&d.children[N])}d=d||g,(g.record.components&&Object.keys(g.record.components).length||g.record.name||g.record.redirect)&&a(g)}return w?()=>{o(w)}:lr}function o(u){if(of(u)){const f=r.get(u);f&&(r.delete(u),n.splice(n.indexOf(f),1),f.children.forEach(o),f.alias.forEach(o))}else{const f=n.indexOf(u);f>-1&&(n.splice(f,1),u.record.name&&r.delete(u.record.name),u.children.forEach(o),u.alias.forEach(o))}}function l(){return n}function a(u){let f=0;for(;f<n.length&&sy(u,n[f])>=0&&(u.record.path!==n[f].record.path||!lf(u,n[f]));)f++;n.splice(f,0,u),u.record.name&&!Il(u)&&r.set(u.record.name,u)}function c(u,f){let d,h={},v,b;if("name"in u&&u.name){if(d=r.get(u.name),!d)throw Dn(1,{location:u});b=d.record.name,h=ge(Al(f.params,d.keys.filter(w=>!w.optional).map(w=>w.name)),u.params&&Al(u.params,d.keys.map(w=>w.name))),v=d.stringify(h)}else if("path"in u)v=u.path,d=n.find(w=>w.re.test(v)),d&&(h=d.parse(v),b=d.record.name);else{if(d=f.name?r.get(f.name):n.find(w=>w.re.test(f.path)),!d)throw Dn(1,{location:u,currentLocation:f});b=d.record.name,h=ge({},f.params,u.params),v=d.stringify(h)}const C=[];let g=d;for(;g;)C.unshift(g.record),g=g.parent;return{name:b,path:v,params:h,matched:C,meta:dy(C)}}return e.forEach(u=>i(u)),{addRoute:i,resolve:c,removeRoute:o,getRoutes:l,getRecordMatcher:s}}function Al(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function uy(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:fy(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function fy(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]=typeof n=="object"?n[r]:n;return t}function Il(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function dy(e){return e.reduce((t,n)=>ge(t,n.meta),{})}function Nl(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function lf(e,t){return t.children.some(n=>n===e||lf(e,n))}const cf=/#/g,py=/&/g,my=/\//g,hy=/=/g,gy=/\?/g,uf=/\+/g,vy=/%5B/g,by=/%5D/g,ff=/%5E/g,yy=/%60/g,df=/%7B/g,_y=/%7C/g,pf=/%7D/g,Ey=/%20/g;function Io(e){return encodeURI(""+e).replace(_y,"|").replace(vy,"[").replace(by,"]")}function wy(e){return Io(e).replace(df,"{").replace(pf,"}").replace(ff,"^")}function Hi(e){return Io(e).replace(uf,"%2B").replace(Ey,"+").replace(cf,"%23").replace(py,"%26").replace(yy,"`").replace(df,"{").replace(pf,"}").replace(ff,"^")}function Sy(e){return Hi(e).replace(hy,"%3D")}function Ty(e){return Io(e).replace(cf,"%23").replace(gy,"%3F")}function Cy(e){return e==null?"":Ty(e).replace(my,"%2F")}function rs(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Oy(e){const t={};if(e===""||e==="?")return t;const r=(e[0]==="?"?e.slice(1):e).split("&");for(let s=0;s<r.length;++s){const i=r[s].replace(uf," "),o=i.indexOf("="),l=rs(o<0?i:i.slice(0,o)),a=o<0?null:rs(i.slice(o+1));if(l in t){let c=t[l];ut(c)||(c=t[l]=[c]),c.push(a)}else t[l]=a}return t}function Rl(e){let t="";for(let n in e){const r=e[n];if(n=Sy(n),r==null){r!==void 0&&(t+=(t.length?"&":"")+n);continue}(ut(r)?r.map(i=>i&&Hi(i)):[r&&Hi(r)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function xy(e){const t={};for(const n in e){const r=e[n];r!==void 0&&(t[n]=ut(r)?r.map(s=>s==null?null:""+s):r==null?r:""+r)}return t}const Py=Symbol(""),Ml=Symbol(""),No=Symbol(""),mf=Symbol(""),Ui=Symbol("");function Jn(){let e=[];function t(r){return e.push(r),()=>{const s=e.indexOf(r);s>-1&&e.splice(s,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Ut(e,t,n,r,s){const i=r&&(r.enterCallbacks[s]=r.enterCallbacks[s]||[]);return()=>new Promise((o,l)=>{const a=f=>{f===!1?l(Dn(4,{from:n,to:t})):f instanceof Error?l(f):Zb(f)?l(Dn(2,{from:t,to:f})):(i&&r.enterCallbacks[s]===i&&typeof f=="function"&&i.push(f),o())},c=e.call(r&&r.instances[s],t,n,a);let u=Promise.resolve(c);e.length<3&&(u=u.then(a)),u.catch(f=>l(f))})}function Zs(e,t,n,r){const s=[];for(const i of e)for(const o in i.components){let l=i.components[o];if(!(t!=="beforeRouteEnter"&&!i.instances[o]))if(Ly(l)){const c=(l.__vccOpts||l)[t];c&&s.push(Ut(c,n,r,i,o))}else{let a=l();s.push(()=>a.then(c=>{if(!c)return Promise.reject(new Error("Couldn't resolve component \"".concat(o,'" at "').concat(i.path,'"')));const u=kb(c)?c.default:c;i.components[o]=u;const d=(u.__vccOpts||u)[t];return d&&Ut(d,n,r,i,o)()}))}}return s}function Ly(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Fl(e){const t=Ze(No),n=Ze(mf),r=fe(()=>t.resolve(yt(e.to))),s=fe(()=>{const{matched:a}=r.value,{length:c}=a,u=a[c-1],f=n.matched;if(!u||!f.length)return-1;const d=f.findIndex(kn.bind(null,u));if(d>-1)return d;const h=kl(a[c-2]);return c>1&&kl(u)===h&&f[f.length-1].path!==h?f.findIndex(kn.bind(null,a[c-2])):d}),i=fe(()=>s.value>-1&&Ry(n.params,r.value.params)),o=fe(()=>s.value>-1&&s.value===n.matched.length-1&&rf(n.params,r.value.params));function l(a={}){return Ny(a)?t[yt(e.replace)?"replace":"push"](yt(e.to)).catch(lr):Promise.resolve()}return{route:r,href:fe(()=>r.value.href),isActive:i,isExactActive:o,navigate:l}}const Ay=Nt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Fl,setup(e,{slots:t}){const n=It(Fl(e)),{options:r}=Ze(No),s=fe(()=>({[Dl(e.activeClass,r.linkActiveClass,"router-link-active")]:n.isActive,[Dl(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&t.default(n);return e.custom?i:$e("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:s.value},i)}}}),Iy=Ay;function Ny(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ry(e,t){for(const n in t){const r=t[n],s=e[n];if(typeof r=="string"){if(r!==s)return!1}else if(!ut(s)||s.length!==r.length||r.some((i,o)=>i!==s[o]))return!1}return!0}function kl(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Dl=(e,t,n)=>e!=null?e:t!=null?t:n,My=Nt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const r=Ze(Ui),s=fe(()=>e.route||r.value),i=Ze(Ml,0),o=fe(()=>{let c=yt(i);const{matched:u}=s.value;let f;for(;(f=u[c])&&!f.components;)c++;return c}),l=fe(()=>s.value.matched[o.value]);pn(Ml,fe(()=>o.value+1)),pn(Py,l),pn(Ui,s);const a=Q();return De(()=>[a.value,l.value,e.name],([c,u,f],[d,h,v])=>{u&&(u.instances[f]=c,h&&h!==u&&c&&c===d&&(u.leaveGuards.size||(u.leaveGuards=h.leaveGuards),u.updateGuards.size||(u.updateGuards=h.updateGuards))),c&&u&&(!h||!kn(u,h)||!d)&&(u.enterCallbacks[f]||[]).forEach(b=>b(c))},{flush:"post"}),()=>{const c=s.value,u=e.name,f=l.value,d=f&&f.components[u];if(!d)return Bl(n.default,{Component:d,route:c});const h=f.props[u],v=h?h===!0?c.params:typeof h=="function"?h(c):h:null,C=$e(d,ge({},v,t,{onVnodeUnmounted:g=>{g.component.isUnmounted&&(f.instances[u]=null)},ref:a}));return Bl(n.default,{Component:C,route:c})||C}}});function Bl(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Fy=My;function IE(e){const t=cy(e.routes,e),n=e.parseQuery||Oy,r=e.stringifyQuery||Rl,s=e.history,i=Jn(),o=Jn(),l=Jn(),a=hc($t);let c=$t;On&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Js.bind(null,k=>""+k),f=Js.bind(null,Cy),d=Js.bind(null,rs);function h(k,z){let V,K;return of(k)?(V=t.getRecordMatcher(k),K=z):K=k,t.addRoute(K,V)}function v(k){const z=t.getRecordMatcher(k);z&&t.removeRoute(z)}function b(){return t.getRoutes().map(k=>k.record)}function C(k){return!!t.getRecordMatcher(k)}function g(k,z){if(z=ge({},z||a.value),typeof k=="string"){const m=Qs(n,k,z.path),y=t.resolve({path:m.path},z),O=s.createHref(m.fullPath);return ge(m,y,{params:d(y.params),hash:rs(m.hash),redirectedFrom:void 0,href:O})}let V;if("path"in k)V=ge({},k,{path:Qs(n,k.path,z.path).path});else{const m=ge({},k.params);for(const y in m)m[y]==null&&delete m[y];V=ge({},k,{params:f(m)}),z.params=f(z.params)}const K=t.resolve(V,z),ae=k.hash||"";K.params=u(d(K.params));const _=$b(r,ge({},k,{hash:wy(ae),path:K.path})),p=s.createHref(_);return ge({fullPath:_,hash:ae,query:r===Rl?xy(k.query):k.query||{}},K,{redirectedFrom:void 0,href:p})}function w(k){return typeof k=="string"?Qs(n,k,a.value.path):ge({},k)}function T(k,z){if(c!==k)return Dn(8,{from:z,to:k})}function E(k){return M(k)}function S(k){return E(ge(w(k),{replace:!0}))}function N(k){const z=k.matched[k.matched.length-1];if(z&&z.redirect){const{redirect:V}=z;let K=typeof V=="function"?V(k):V;return typeof K=="string"&&(K=K.includes("?")||K.includes("#")?K=w(K):{path:K},K.params={}),ge({query:k.query,hash:k.hash,params:"path"in K?{}:k.params},K)}}function M(k,z){const V=c=g(k),K=a.value,ae=k.state,_=k.force,p=k.replace===!0,m=N(V);if(m)return M(ge(w(m),{state:typeof m=="object"?ge({},ae,m.state):ae,force:_,replace:p}),z||V);const y=V;y.redirectedFrom=z;let O;return!_&&jb(r,K,V)&&(O=Dn(16,{to:y,from:K}),Te(K,K,!0,!1)),(O?Promise.resolve(O):W(y,K)).catch(R=>Tt(R)?Tt(R,2)?R:Le(R):ie(R,y,K)).then(R=>{if(R){if(Tt(R,2))return M(ge({replace:p},w(R.to),{state:typeof R.to=="object"?ge({},ae,R.to.state):ae,force:_}),z||y)}else R=A(y,K,!0,p,ae);return P(y,K,R),R})}function F(k,z){const V=T(k,z);return V?Promise.reject(V):Promise.resolve()}function x(k){const z=qe.values().next().value;return z&&typeof z.runWithContext=="function"?z.runWithContext(k):k()}function W(k,z){let V;const[K,ae,_]=ky(k,z);V=Zs(K.reverse(),"beforeRouteLeave",k,z);for(const m of K)m.leaveGuards.forEach(y=>{V.push(Ut(y,k,z))});const p=F.bind(null,k,z);return V.push(p),Ce(V).then(()=>{V=[];for(const m of i.list())V.push(Ut(m,k,z));return V.push(p),Ce(V)}).then(()=>{V=Zs(ae,"beforeRouteUpdate",k,z);for(const m of ae)m.updateGuards.forEach(y=>{V.push(Ut(y,k,z))});return V.push(p),Ce(V)}).then(()=>{V=[];for(const m of _)if(m.beforeEnter)if(ut(m.beforeEnter))for(const y of m.beforeEnter)V.push(Ut(y,k,z));else V.push(Ut(m.beforeEnter,k,z));return V.push(p),Ce(V)}).then(()=>(k.matched.forEach(m=>m.enterCallbacks={}),V=Zs(_,"beforeRouteEnter",k,z),V.push(p),Ce(V))).then(()=>{V=[];for(const m of o.list())V.push(Ut(m,k,z));return V.push(p),Ce(V)}).catch(m=>Tt(m,8)?m:Promise.reject(m))}function P(k,z,V){l.list().forEach(K=>x(()=>K(k,z,V)))}function A(k,z,V,K,ae){const _=T(k,z);if(_)return _;const p=z===$t,m=On?history.state:{};V&&(K||p?s.replace(k.fullPath,ge({scroll:p&&m&&m.scroll},ae)):s.push(k.fullPath,ae)),a.value=k,Te(k,z,V,p),Le()}let H;function J(){H||(H=s.listen((k,z,V)=>{if(!ft.listening)return;const K=g(k),ae=N(K);if(ae){M(ge(ae,{replace:!0}),K).catch(lr);return}c=K;const _=a.value;On&&Yb(Cl(_.fullPath,V.delta),Fs()),W(K,_).catch(p=>Tt(p,12)?p:Tt(p,2)?(M(p.to,K).then(m=>{Tt(m,20)&&!V.delta&&V.type===_r.pop&&s.go(-1,!1)}).catch(lr),Promise.reject()):(V.delta&&s.go(-V.delta,!1),ie(p,K,_))).then(p=>{p=p||A(K,_,!1),p&&(V.delta&&!Tt(p,8)?s.go(-V.delta,!1):V.type===_r.pop&&Tt(p,20)&&s.go(-1,!1)),P(K,_,p)}).catch(lr)}))}let re=Jn(),X=Jn(),ne;function ie(k,z,V){Le(k);const K=X.list();return K.length?K.forEach(ae=>ae(k,z,V)):console.error(k),Promise.reject(k)}function we(){return ne&&a.value!==$t?Promise.resolve():new Promise((k,z)=>{re.add([k,z])})}function Le(k){return ne||(ne=!k,J(),re.list().forEach(([z,V])=>k?V(k):z()),re.reset()),k}function Te(k,z,V,K){const{scrollBehavior:ae}=e;if(!On||!ae)return Promise.resolve();const _=!V&&qb(Cl(k.fullPath,0))||(K||!V)&&history.state&&history.state.scroll||null;return Hn().then(()=>ae(k,z,_)).then(p=>p&&Kb(p)).catch(p=>ie(p,k,z))}const Se=k=>s.go(k);let Ye;const qe=new Set,ft={currentRoute:a,listening:!0,addRoute:h,removeRoute:v,hasRoute:C,getRoutes:b,resolve:g,options:e,push:E,replace:S,go:Se,back:()=>Se(-1),forward:()=>Se(1),beforeEach:i.add,beforeResolve:o.add,afterEach:l.add,onError:X.add,isReady:we,install(k){const z=this;k.component("RouterLink",Iy),k.component("RouterView",Fy),k.config.globalProperties.$router=z,Object.defineProperty(k.config.globalProperties,"$route",{enumerable:!0,get:()=>yt(a)}),On&&!Ye&&a.value===$t&&(Ye=!0,E(s.location).catch(ae=>{}));const V={};for(const ae in $t)Object.defineProperty(V,ae,{get:()=>a.value[ae],enumerable:!0});k.provide(No,z),k.provide(mf,uc(V)),k.provide(Ui,a);const K=k.unmount;qe.add(k),k.unmount=function(){qe.delete(k),qe.size<1&&(c=$t,H&&H(),H=null,a.value=$t,Ye=!1,ne=!1),K()}}};function Ce(k){return k.reduce((z,V)=>z.then(()=>x(V)),Promise.resolve())}return ft}function ky(e,t){const n=[],r=[],s=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const l=t.matched[o];l&&(e.matched.find(c=>kn(c,l))?r.push(l):n.push(l));const a=e.matched[o];a&&(t.matched.find(c=>kn(c,a))||s.push(a))}return[n,r,s]}function $l(e){return e!==null&&typeof e=="object"&&"constructor"in e&&e.constructor===Object}function Ro(e,t){e===void 0&&(e={}),t===void 0&&(t={}),Object.keys(t).forEach(n=>{typeof e[n]>"u"?e[n]=t[n]:$l(t[n])&&$l(e[n])&&Object.keys(t[n]).length>0&&Ro(e[n],t[n])})}const hf={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector(){return null},querySelectorAll(){return[]},getElementById(){return null},createEvent(){return{initEvent(){}}},createElement(){return{children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName(){return[]}}},createElementNS(){return{}},importNode(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function _n(){const e=typeof document<"u"?document:{};return Ro(e,hf),e}const Dy={document:hf,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle(){return{getPropertyValue(){return""}}},Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia(){return{}},requestAnimationFrame(e){return typeof setTimeout>"u"?(e(),null):setTimeout(e,0)},cancelAnimationFrame(e){typeof setTimeout>"u"||clearTimeout(e)}};function et(){const e=typeof window<"u"?window:{};return Ro(e,Dy),e}function By(e){const t=e;Object.keys(t).forEach(n=>{try{t[n]=null}catch(r){}try{delete t[n]}catch(r){}})}function Vi(e,t){return t===void 0&&(t=0),setTimeout(e,t)}function ss(){return Date.now()}function $y(e){const t=et();let n;return t.getComputedStyle&&(n=t.getComputedStyle(e,null)),!n&&e.currentStyle&&(n=e.currentStyle),n||(n=e.style),n}function jy(e,t){t===void 0&&(t="x");const n=et();let r,s,i;const o=$y(e);return n.WebKitCSSMatrix?(s=o.transform||o.webkitTransform,s.split(",").length>6&&(s=s.split(", ").map(l=>l.replace(",",".")).join(", ")),i=new n.WebKitCSSMatrix(s==="none"?"":s)):(i=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,"),r=i.toString().split(",")),t==="x"&&(n.WebKitCSSMatrix?s=i.m41:r.length===16?s=parseFloat(r[12]):s=parseFloat(r[4])),t==="y"&&(n.WebKitCSSMatrix?s=i.m42:r.length===16?s=parseFloat(r[13]):s=parseFloat(r[5])),s||0}function $r(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function Hy(e){return typeof window<"u"&&typeof window.HTMLElement<"u"?e instanceof HTMLElement:e&&(e.nodeType===1||e.nodeType===11)}function Qe(){const e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let n=1;n<arguments.length;n+=1){const r=n<0||arguments.length<=n?void 0:arguments[n];if(r!=null&&!Hy(r)){const s=Object.keys(Object(r)).filter(i=>t.indexOf(i)<0);for(let i=0,o=s.length;i<o;i+=1){const l=s[i],a=Object.getOwnPropertyDescriptor(r,l);a!==void 0&&a.enumerable&&($r(e[l])&&$r(r[l])?r[l].__swiper__?e[l]=r[l]:Qe(e[l],r[l]):!$r(e[l])&&$r(r[l])?(e[l]={},r[l].__swiper__?e[l]=r[l]:Qe(e[l],r[l])):e[l]=r[l])}}}return e}function jr(e,t,n){e.style.setProperty(t,n)}function gf(e){let{swiper:t,targetPosition:n,side:r}=e;const s=et(),i=-t.translate;let o=null,l;const a=t.params.speed;t.wrapperEl.style.scrollSnapType="none",s.cancelAnimationFrame(t.cssModeFrameID);const c=n>i?"next":"prev",u=(d,h)=>c==="next"&&d>=h||c==="prev"&&d<=h,f=()=>{l=new Date().getTime(),o===null&&(o=l);const d=Math.max(Math.min((l-o)/a,1),0),h=.5-Math.cos(d*Math.PI)/2;let v=i+h*(n-i);if(u(v,n)&&(v=n),t.wrapperEl.scrollTo({[r]:v}),u(v,n)){t.wrapperEl.style.overflow="hidden",t.wrapperEl.style.scrollSnapType="",setTimeout(()=>{t.wrapperEl.style.overflow="",t.wrapperEl.scrollTo({[r]:v})}),s.cancelAnimationFrame(t.cssModeFrameID);return}t.cssModeFrameID=s.requestAnimationFrame(f)};f()}function xt(e,t){return t===void 0&&(t=""),[...e.children].filter(n=>n.matches(t))}function Uy(e,t){t===void 0&&(t=[]);const n=document.createElement(e);return n.classList.add(...Array.isArray(t)?t:[t]),n}function Vy(e,t){const n=[];for(;e.previousElementSibling;){const r=e.previousElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function Wy(e,t){const n=[];for(;e.nextElementSibling;){const r=e.nextElementSibling;t?r.matches(t)&&n.push(r):n.push(r),e=r}return n}function Vt(e,t){return et().getComputedStyle(e,null).getPropertyValue(t)}function jl(e){let t=e,n;if(t){for(n=0;(t=t.previousSibling)!==null;)t.nodeType===1&&(n+=1);return n}}function zy(e,t){const n=[];let r=e.parentElement;for(;r;)t?r.matches(t)&&n.push(r):n.push(r),r=r.parentElement;return n}function Hl(e,t,n){const r=et();return n?e[t==="width"?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue(t==="width"?"margin-left":"margin-bottom")):e.offsetWidth}let ei;function Gy(){const e=et(),t=_n();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}function vf(){return ei||(ei=Gy()),ei}let ti;function Ky(e){let{userAgent:t}=e===void 0?{}:e;const n=vf(),r=et(),s=r.navigator.platform,i=t||r.navigator.userAgent,o={ios:!1,android:!1},l=r.screen.width,a=r.screen.height,c=i.match(/(Android);?[\s\/]+([\d.]+)?/);let u=i.match(/(iPad).*OS\s([\d_]+)/);const f=i.match(/(iPod)(.*OS\s([\d_]+))?/),d=!u&&i.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h=s==="Win32";let v=s==="MacIntel";const b=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"];return!u&&v&&n.touch&&b.indexOf("".concat(l,"x").concat(a))>=0&&(u=i.match(/(Version)\/([\d.]+)/),u||(u=[0,1,"13_0_0"]),v=!1),c&&!h&&(o.os="android",o.android=!0),(u||d||f)&&(o.os="ios",o.ios=!0),o}function Yy(e){return e===void 0&&(e={}),ti||(ti=Ky(e)),ti}let ni;function qy(){const e=et();let t=!1;function n(){const r=e.navigator.userAgent.toLowerCase();return r.indexOf("safari")>=0&&r.indexOf("chrome")<0&&r.indexOf("android")<0}if(n()){const r=String(e.navigator.userAgent);if(r.includes("Version/")){const[s,i]=r.split("Version/")[1].split(" ")[0].split(".").map(o=>Number(o));t=s<16||s===16&&i<2}}return{isSafari:t||n(),needPerspectiveFix:t,isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}function Xy(){return ni||(ni=qy()),ni}function Jy(e){let{swiper:t,on:n,emit:r}=e;const s=et();let i=null,o=null;const l=()=>{!t||t.destroyed||!t.initialized||(r("beforeResize"),r("resize"))},a=()=>{!t||t.destroyed||!t.initialized||(i=new ResizeObserver(f=>{o=s.requestAnimationFrame(()=>{const{width:d,height:h}=t;let v=d,b=h;f.forEach(C=>{let{contentBoxSize:g,contentRect:w,target:T}=C;T&&T!==t.el||(v=w?w.width:(g[0]||g).inlineSize,b=w?w.height:(g[0]||g).blockSize)}),(v!==d||b!==h)&&l()})}),i.observe(t.el))},c=()=>{o&&s.cancelAnimationFrame(o),i&&i.unobserve&&t.el&&(i.unobserve(t.el),i=null)},u=()=>{!t||t.destroyed||!t.initialized||r("orientationchange")};n("init",()=>{if(t.params.resizeObserver&&typeof s.ResizeObserver<"u"){a();return}s.addEventListener("resize",l),s.addEventListener("orientationchange",u)}),n("destroy",()=>{c(),s.removeEventListener("resize",l),s.removeEventListener("orientationchange",u)})}function Qy(e){let{swiper:t,extendParams:n,on:r,emit:s}=e;const i=[],o=et(),l=function(u,f){f===void 0&&(f={});const d=o.MutationObserver||o.WebkitMutationObserver,h=new d(v=>{if(t.__preventObserver__)return;if(v.length===1){s("observerUpdate",v[0]);return}const b=function(){s("observerUpdate",v[0])};o.requestAnimationFrame?o.requestAnimationFrame(b):o.setTimeout(b,0)});h.observe(u,{attributes:typeof f.attributes>"u"?!0:f.attributes,childList:typeof f.childList>"u"?!0:f.childList,characterData:typeof f.characterData>"u"?!0:f.characterData}),i.push(h)},a=()=>{if(t.params.observer){if(t.params.observeParents){const u=zy(t.hostEl);for(let f=0;f<u.length;f+=1)l(u[f])}l(t.hostEl,{childList:t.params.observeSlideChildren}),l(t.wrapperEl,{attributes:!1})}},c=()=>{i.forEach(u=>{u.disconnect()}),i.splice(0,i.length)};n({observer:!1,observeParents:!1,observeSlideChildren:!1}),r("init",a),r("destroy",c)}var Zy={on(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;const s=n?"unshift":"push";return e.split(" ").forEach(i=>{r.eventsListeners[i]||(r.eventsListeners[i]=[]),r.eventsListeners[i][s](t)}),r},once(e,t,n){const r=this;if(!r.eventsListeners||r.destroyed||typeof t!="function")return r;function s(){r.off(e,s),s.__emitterProxy&&delete s.__emitterProxy;for(var i=arguments.length,o=new Array(i),l=0;l<i;l++)o[l]=arguments[l];t.apply(r,o)}return s.__emitterProxy=t,r.on(e,s,n)},onAny(e,t){const n=this;if(!n.eventsListeners||n.destroyed||typeof e!="function")return n;const r=t?"unshift":"push";return n.eventsAnyListeners.indexOf(e)<0&&n.eventsAnyListeners[r](e),n},offAny(e){const t=this;if(!t.eventsListeners||t.destroyed||!t.eventsAnyListeners)return t;const n=t.eventsAnyListeners.indexOf(e);return n>=0&&t.eventsAnyListeners.splice(n,1),t},off(e,t){const n=this;return!n.eventsListeners||n.destroyed||!n.eventsListeners||e.split(" ").forEach(r=>{typeof t>"u"?n.eventsListeners[r]=[]:n.eventsListeners[r]&&n.eventsListeners[r].forEach((s,i)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&n.eventsListeners[r].splice(i,1)})}),n},emit(){const e=this;if(!e.eventsListeners||e.destroyed||!e.eventsListeners)return e;let t,n,r;for(var s=arguments.length,i=new Array(s),o=0;o<s;o++)i[o]=arguments[o];return typeof i[0]=="string"||Array.isArray(i[0])?(t=i[0],n=i.slice(1,i.length),r=e):(t=i[0].events,n=i[0].data,r=i[0].context||e),n.unshift(r),(Array.isArray(t)?t:t.split(" ")).forEach(a=>{e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(c=>{c.apply(r,[a,...n])}),e.eventsListeners&&e.eventsListeners[a]&&e.eventsListeners[a].forEach(c=>{c.apply(r,n)})}),e}};function e_(){const e=this;let t,n;const r=e.el;typeof e.params.width<"u"&&e.params.width!==null?t=e.params.width:t=r.clientWidth,typeof e.params.height<"u"&&e.params.height!==null?n=e.params.height:n=r.clientHeight,!(t===0&&e.isHorizontal()||n===0&&e.isVertical())&&(t=t-parseInt(Vt(r,"padding-left")||0,10)-parseInt(Vt(r,"padding-right")||0,10),n=n-parseInt(Vt(r,"padding-top")||0,10)-parseInt(Vt(r,"padding-bottom")||0,10),Number.isNaN(t)&&(t=0),Number.isNaN(n)&&(n=0),Object.assign(e,{width:t,height:n,size:e.isHorizontal()?t:n}))}function t_(){const e=this;function t(P){return e.isHorizontal()?P:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[P]}function n(P,A){return parseFloat(P.getPropertyValue(t(A))||0)}const r=e.params,{wrapperEl:s,slidesEl:i,size:o,rtlTranslate:l,wrongRTL:a}=e,c=e.virtual&&r.virtual.enabled,u=c?e.virtual.slides.length:e.slides.length,f=xt(i,".".concat(e.params.slideClass,", swiper-slide")),d=c?e.virtual.slides.length:f.length;let h=[];const v=[],b=[];let C=r.slidesOffsetBefore;typeof C=="function"&&(C=r.slidesOffsetBefore.call(e));let g=r.slidesOffsetAfter;typeof g=="function"&&(g=r.slidesOffsetAfter.call(e));const w=e.snapGrid.length,T=e.slidesGrid.length;let E=r.spaceBetween,S=-C,N=0,M=0;if(typeof o>"u")return;typeof E=="string"&&E.indexOf("%")>=0?E=parseFloat(E.replace("%",""))/100*o:typeof E=="string"&&(E=parseFloat(E)),e.virtualSize=-E,f.forEach(P=>{l?P.style.marginLeft="":P.style.marginRight="",P.style.marginBottom="",P.style.marginTop=""}),r.centeredSlides&&r.cssMode&&(jr(s,"--swiper-centered-offset-before",""),jr(s,"--swiper-centered-offset-after",""));const F=r.grid&&r.grid.rows>1&&e.grid;F&&e.grid.initSlides(d);let x;const W=r.slidesPerView==="auto"&&r.breakpoints&&Object.keys(r.breakpoints).filter(P=>typeof r.breakpoints[P].slidesPerView<"u").length>0;for(let P=0;P<d;P+=1){x=0;let A;if(f[P]&&(A=f[P]),F&&e.grid.updateSlide(P,A,d,t),!(f[P]&&Vt(A,"display")==="none")){if(r.slidesPerView==="auto"){W&&(f[P].style[t("width")]="");const H=getComputedStyle(A),J=A.style.transform,re=A.style.webkitTransform;if(J&&(A.style.transform="none"),re&&(A.style.webkitTransform="none"),r.roundLengths)x=e.isHorizontal()?Hl(A,"width",!0):Hl(A,"height",!0);else{const X=n(H,"width"),ne=n(H,"padding-left"),ie=n(H,"padding-right"),we=n(H,"margin-left"),Le=n(H,"margin-right"),Te=H.getPropertyValue("box-sizing");if(Te&&Te==="border-box")x=X+we+Le;else{const{clientWidth:Se,offsetWidth:Ye}=A;x=X+ne+ie+we+Le+(Ye-Se)}}J&&(A.style.transform=J),re&&(A.style.webkitTransform=re),r.roundLengths&&(x=Math.floor(x))}else x=(o-(r.slidesPerView-1)*E)/r.slidesPerView,r.roundLengths&&(x=Math.floor(x)),f[P]&&(f[P].style[t("width")]="".concat(x,"px"));f[P]&&(f[P].swiperSlideSize=x),b.push(x),r.centeredSlides?(S=S+x/2+N/2+E,N===0&&P!==0&&(S=S-o/2-E),P===0&&(S=S-o/2-E),Math.abs(S)<1/1e3&&(S=0),r.roundLengths&&(S=Math.floor(S)),M%r.slidesPerGroup===0&&h.push(S),v.push(S)):(r.roundLengths&&(S=Math.floor(S)),(M-Math.min(e.params.slidesPerGroupSkip,M))%e.params.slidesPerGroup===0&&h.push(S),v.push(S),S=S+x+E),e.virtualSize+=x+E,N=x,M+=1}}if(e.virtualSize=Math.max(e.virtualSize,o)+g,l&&a&&(r.effect==="slide"||r.effect==="coverflow")&&(s.style.width="".concat(e.virtualSize+E,"px")),r.setWrapperSize&&(s.style[t("width")]="".concat(e.virtualSize+E,"px")),F&&e.grid.updateWrapperSize(x,h,t),!r.centeredSlides){const P=[];for(let A=0;A<h.length;A+=1){let H=h[A];r.roundLengths&&(H=Math.floor(H)),h[A]<=e.virtualSize-o&&P.push(H)}h=P,Math.floor(e.virtualSize-o)-Math.floor(h[h.length-1])>1&&h.push(e.virtualSize-o)}if(c&&r.loop){const P=b[0]+E;if(r.slidesPerGroup>1){const A=Math.ceil((e.virtual.slidesBefore+e.virtual.slidesAfter)/r.slidesPerGroup),H=P*r.slidesPerGroup;for(let J=0;J<A;J+=1)h.push(h[h.length-1]+H)}for(let A=0;A<e.virtual.slidesBefore+e.virtual.slidesAfter;A+=1)r.slidesPerGroup===1&&h.push(h[h.length-1]+P),v.push(v[v.length-1]+P),e.virtualSize+=P}if(h.length===0&&(h=[0]),E!==0){const P=e.isHorizontal()&&l?"marginLeft":t("marginRight");f.filter((A,H)=>!r.cssMode||r.loop?!0:H!==f.length-1).forEach(A=>{A.style[P]="".concat(E,"px")})}if(r.centeredSlides&&r.centeredSlidesBounds){let P=0;b.forEach(H=>{P+=H+(E||0)}),P-=E;const A=P-o;h=h.map(H=>H<=0?-C:H>A?A+g:H)}if(r.centerInsufficientSlides){let P=0;if(b.forEach(A=>{P+=A+(E||0)}),P-=E,P<o){const A=(o-P)/2;h.forEach((H,J)=>{h[J]=H-A}),v.forEach((H,J)=>{v[J]=H+A})}}if(Object.assign(e,{slides:f,snapGrid:h,slidesGrid:v,slidesSizesGrid:b}),r.centeredSlides&&r.cssMode&&!r.centeredSlidesBounds){jr(s,"--swiper-centered-offset-before","".concat(-h[0],"px")),jr(s,"--swiper-centered-offset-after","".concat(e.size/2-b[b.length-1]/2,"px"));const P=-e.snapGrid[0],A=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map(H=>H+P),e.slidesGrid=e.slidesGrid.map(H=>H+A)}if(d!==u&&e.emit("slidesLengthChange"),h.length!==w&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),v.length!==T&&e.emit("slidesGridLengthChange"),r.watchSlidesProgress&&e.updateSlidesOffset(),!c&&!r.cssMode&&(r.effect==="slide"||r.effect==="fade")){const P="".concat(r.containerModifierClass,"backface-hidden"),A=e.el.classList.contains(P);d<=r.maxBackfaceHiddenSlides?A||e.el.classList.add(P):A&&e.el.classList.remove(P)}}function n_(e){const t=this,n=[],r=t.virtual&&t.params.virtual.enabled;let s=0,i;typeof e=="number"?t.setTransition(e):e===!0&&t.setTransition(t.params.speed);const o=l=>r?t.slides[t.getSlideIndexByData(l)]:t.slides[l];if(t.params.slidesPerView!=="auto"&&t.params.slidesPerView>1)if(t.params.centeredSlides)(t.visibleSlides||[]).forEach(l=>{n.push(l)});else for(i=0;i<Math.ceil(t.params.slidesPerView);i+=1){const l=t.activeIndex+i;if(l>t.slides.length&&!r)break;n.push(o(l))}else n.push(o(t.activeIndex));for(i=0;i<n.length;i+=1)if(typeof n[i]<"u"){const l=n[i].offsetHeight;s=l>s?l:s}(s||s===0)&&(t.wrapperEl.style.height="".concat(s,"px"))}function r_(){const e=this,t=e.slides,n=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0;for(let r=0;r<t.length;r+=1)t[r].swiperSlideOffset=(e.isHorizontal()?t[r].offsetLeft:t[r].offsetTop)-n-e.cssOverflowAdjustment()}function s_(e){e===void 0&&(e=this&&this.translate||0);const t=this,n=t.params,{slides:r,rtlTranslate:s,snapGrid:i}=t;if(r.length===0)return;typeof r[0].swiperSlideOffset>"u"&&t.updateSlidesOffset();let o=-e;s&&(o=e),r.forEach(a=>{a.classList.remove(n.slideVisibleClass)}),t.visibleSlidesIndexes=[],t.visibleSlides=[];let l=n.spaceBetween;typeof l=="string"&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*t.size:typeof l=="string"&&(l=parseFloat(l));for(let a=0;a<r.length;a+=1){const c=r[a];let u=c.swiperSlideOffset;n.cssMode&&n.centeredSlides&&(u-=r[0].swiperSlideOffset);const f=(o+(n.centeredSlides?t.minTranslate():0)-u)/(c.swiperSlideSize+l),d=(o-i[0]+(n.centeredSlides?t.minTranslate():0)-u)/(c.swiperSlideSize+l),h=-(o-u),v=h+t.slidesSizesGrid[a];(h>=0&&h<t.size-1||v>1&&v<=t.size||h<=0&&v>=t.size)&&(t.visibleSlides.push(c),t.visibleSlidesIndexes.push(a),r[a].classList.add(n.slideVisibleClass)),c.progress=s?-f:f,c.originalProgress=s?-d:d}}function i_(e){const t=this;if(typeof e>"u"){const u=t.rtlTranslate?-1:1;e=t&&t.translate&&t.translate*u||0}const n=t.params,r=t.maxTranslate()-t.minTranslate();let{progress:s,isBeginning:i,isEnd:o,progressLoop:l}=t;const a=i,c=o;if(r===0)s=0,i=!0,o=!0;else{s=(e-t.minTranslate())/r;const u=Math.abs(e-t.minTranslate())<1,f=Math.abs(e-t.maxTranslate())<1;i=u||s<=0,o=f||s>=1,u&&(s=0),f&&(s=1)}if(n.loop){const u=t.getSlideIndexByData(0),f=t.getSlideIndexByData(t.slides.length-1),d=t.slidesGrid[u],h=t.slidesGrid[f],v=t.slidesGrid[t.slidesGrid.length-1],b=Math.abs(e);b>=d?l=(b-d)/v:l=(b+v-h)/v,l>1&&(l-=1)}Object.assign(t,{progress:s,progressLoop:l,isBeginning:i,isEnd:o}),(n.watchSlidesProgress||n.centeredSlides&&n.autoHeight)&&t.updateSlidesProgress(e),i&&!a&&t.emit("reachBeginning toEdge"),o&&!c&&t.emit("reachEnd toEdge"),(a&&!i||c&&!o)&&t.emit("fromEdge"),t.emit("progress",s)}function o_(){const e=this,{slides:t,params:n,slidesEl:r,activeIndex:s}=e,i=e.virtual&&n.virtual.enabled,o=a=>xt(r,".".concat(n.slideClass).concat(a,", swiper-slide").concat(a))[0];t.forEach(a=>{a.classList.remove(n.slideActiveClass,n.slideNextClass,n.slidePrevClass)});let l;if(i)if(n.loop){let a=s-e.virtual.slidesBefore;a<0&&(a=e.virtual.slides.length+a),a>=e.virtual.slides.length&&(a-=e.virtual.slides.length),l=o('[data-swiper-slide-index="'.concat(a,'"]'))}else l=o('[data-swiper-slide-index="'.concat(s,'"]'));else l=t[s];if(l){l.classList.add(n.slideActiveClass);let a=Wy(l,".".concat(n.slideClass,", swiper-slide"))[0];n.loop&&!a&&(a=t[0]),a&&a.classList.add(n.slideNextClass);let c=Vy(l,".".concat(n.slideClass,", swiper-slide"))[0];n.loop&&!c===0&&(c=t[t.length-1]),c&&c.classList.add(n.slidePrevClass)}e.emitSlidesClasses()}const Yr=(e,t)=>{if(!e||e.destroyed||!e.params)return;const n=()=>e.isElement?"swiper-slide":".".concat(e.params.slideClass),r=t.closest(n());if(r){const s=r.querySelector(".".concat(e.params.lazyPreloaderClass));s&&s.remove()}},ri=(e,t)=>{if(!e.slides[t])return;const n=e.slides[t].querySelector('[loading="lazy"]');n&&n.removeAttribute("loading")},Wi=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext;const n=e.slides.length;if(!n||!t||t<0)return;t=Math.min(t,n);const r=e.params.slidesPerView==="auto"?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),s=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){const o=s,l=[o-t];l.push(...Array.from({length:t}).map((a,c)=>o+r+c)),e.slides.forEach((a,c)=>{l.includes(a.column)&&ri(e,c)});return}const i=s+r-1;if(e.params.rewind||e.params.loop)for(let o=s-t;o<=i+t;o+=1){const l=(o%n+n)%n;(l<s||l>i)&&ri(e,l)}else for(let o=Math.max(s-t,0);o<=Math.min(i+t,n-1);o+=1)o!==s&&(o>i||o<s)&&ri(e,o)};function a_(e){const{slidesGrid:t,params:n}=e,r=e.rtlTranslate?e.translate:-e.translate;let s;for(let i=0;i<t.length;i+=1)typeof t[i+1]<"u"?r>=t[i]&&r<t[i+1]-(t[i+1]-t[i])/2?s=i:r>=t[i]&&r<t[i+1]&&(s=i+1):r>=t[i]&&(s=i);return n.normalizeSlideIndex&&(s<0||typeof s>"u")&&(s=0),s}function l_(e){const t=this,n=t.rtlTranslate?t.translate:-t.translate,{snapGrid:r,params:s,activeIndex:i,realIndex:o,snapIndex:l}=t;let a=e,c;const u=d=>{let h=d-t.virtual.slidesBefore;return h<0&&(h=t.virtual.slides.length+h),h>=t.virtual.slides.length&&(h-=t.virtual.slides.length),h};if(typeof a>"u"&&(a=a_(t)),r.indexOf(n)>=0)c=r.indexOf(n);else{const d=Math.min(s.slidesPerGroupSkip,a);c=d+Math.floor((a-d)/s.slidesPerGroup)}if(c>=r.length&&(c=r.length-1),a===i){c!==l&&(t.snapIndex=c,t.emit("snapIndexChange")),t.params.loop&&t.virtual&&t.params.virtual.enabled&&(t.realIndex=u(a));return}let f;t.virtual&&s.virtual.enabled&&s.loop?f=u(a):t.slides[a]?f=parseInt(t.slides[a].getAttribute("data-swiper-slide-index")||a,10):f=a,Object.assign(t,{previousSnapIndex:l,snapIndex:c,previousRealIndex:o,realIndex:f,previousIndex:i,activeIndex:a}),t.initialized&&Wi(t),t.emit("activeIndexChange"),t.emit("snapIndexChange"),o!==f&&t.emit("realIndexChange"),(t.initialized||t.params.runCallbacksOnInit)&&t.emit("slideChange")}function c_(e){const t=this,n=t.params,r=e.closest(".".concat(n.slideClass,", swiper-slide"));let s=!1,i;if(r){for(let o=0;o<t.slides.length;o+=1)if(t.slides[o]===r){s=!0,i=o;break}}if(r&&s)t.clickedSlide=r,t.virtual&&t.params.virtual.enabled?t.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):t.clickedIndex=i;else{t.clickedSlide=void 0,t.clickedIndex=void 0;return}n.slideToClickedSlide&&t.clickedIndex!==void 0&&t.clickedIndex!==t.activeIndex&&t.slideToClickedSlide()}var u_={updateSize:e_,updateSlides:t_,updateAutoHeight:n_,updateSlidesOffset:r_,updateSlidesProgress:s_,updateProgress:i_,updateSlidesClasses:o_,updateActiveIndex:l_,updateClickedSlide:c_};function f_(e){e===void 0&&(e=this.isHorizontal()?"x":"y");const t=this,{params:n,rtlTranslate:r,translate:s,wrapperEl:i}=t;if(n.virtualTranslate)return r?-s:s;if(n.cssMode)return s;let o=jy(i,e);return o+=t.cssOverflowAdjustment(),r&&(o=-o),o||0}function d_(e,t){const n=this,{rtlTranslate:r,params:s,wrapperEl:i,progress:o}=n;let l=0,a=0;const c=0;n.isHorizontal()?l=r?-e:e:a=e,s.roundLengths&&(l=Math.floor(l),a=Math.floor(a)),n.previousTranslate=n.translate,n.translate=n.isHorizontal()?l:a,s.cssMode?i[n.isHorizontal()?"scrollLeft":"scrollTop"]=n.isHorizontal()?-l:-a:s.virtualTranslate||(n.isHorizontal()?l-=n.cssOverflowAdjustment():a-=n.cssOverflowAdjustment(),i.style.transform="translate3d(".concat(l,"px, ").concat(a,"px, ").concat(c,"px)"));let u;const f=n.maxTranslate()-n.minTranslate();f===0?u=0:u=(e-n.minTranslate())/f,u!==o&&n.updateProgress(e),n.emit("setTranslate",n.translate,t)}function p_(){return-this.snapGrid[0]}function m_(){return-this.snapGrid[this.snapGrid.length-1]}function h_(e,t,n,r,s){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),r===void 0&&(r=!0);const i=this,{params:o,wrapperEl:l}=i;if(i.animating&&o.preventInteractionOnTransition)return!1;const a=i.minTranslate(),c=i.maxTranslate();let u;if(r&&e>a?u=a:r&&e<c?u=c:u=e,i.updateProgress(u),o.cssMode){const f=i.isHorizontal();if(t===0)l[f?"scrollLeft":"scrollTop"]=-u;else{if(!i.support.smoothScroll)return gf({swiper:i,targetPosition:-u,side:f?"left":"top"}),!0;l.scrollTo({[f?"left":"top"]:-u,behavior:"smooth"})}return!0}return t===0?(i.setTransition(0),i.setTranslate(u),n&&(i.emit("beforeTransitionStart",t,s),i.emit("transitionEnd"))):(i.setTransition(t),i.setTranslate(u),n&&(i.emit("beforeTransitionStart",t,s),i.emit("transitionStart")),i.animating||(i.animating=!0,i.onTranslateToWrapperTransitionEnd||(i.onTranslateToWrapperTransitionEnd=function(d){!i||i.destroyed||d.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onTranslateToWrapperTransitionEnd),i.onTranslateToWrapperTransitionEnd=null,delete i.onTranslateToWrapperTransitionEnd,n&&i.emit("transitionEnd"))}),i.wrapperEl.addEventListener("transitionend",i.onTranslateToWrapperTransitionEnd))),!0}var g_={getTranslate:f_,setTranslate:d_,minTranslate:p_,maxTranslate:m_,translateTo:h_};function v_(e,t){const n=this;n.params.cssMode||(n.wrapperEl.style.transitionDuration="".concat(e,"ms")),n.emit("setTransition",e,t)}function bf(e){let{swiper:t,runCallbacks:n,direction:r,step:s}=e;const{activeIndex:i,previousIndex:o}=t;let l=r;if(l||(i>o?l="next":i<o?l="prev":l="reset"),t.emit("transition".concat(s)),n&&i!==o){if(l==="reset"){t.emit("slideResetTransition".concat(s));return}t.emit("slideChangeTransition".concat(s)),l==="next"?t.emit("slideNextTransition".concat(s)):t.emit("slidePrevTransition".concat(s))}}function b_(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;r.cssMode||(r.autoHeight&&n.updateAutoHeight(),bf({swiper:n,runCallbacks:e,direction:t,step:"Start"}))}function y_(e,t){e===void 0&&(e=!0);const n=this,{params:r}=n;n.animating=!1,!r.cssMode&&(n.setTransition(0),bf({swiper:n,runCallbacks:e,direction:t,step:"End"}))}var __={setTransition:v_,transitionStart:b_,transitionEnd:y_};function E_(e,t,n,r,s){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const i=this;let o=e;o<0&&(o=0);const{params:l,snapGrid:a,slidesGrid:c,previousIndex:u,activeIndex:f,rtlTranslate:d,wrapperEl:h,enabled:v}=i;if(i.animating&&l.preventInteractionOnTransition||!v&&!r&&!s)return!1;const b=Math.min(i.params.slidesPerGroupSkip,o);let C=b+Math.floor((o-b)/i.params.slidesPerGroup);C>=a.length&&(C=a.length-1);const g=-a[C];if(l.normalizeSlideIndex)for(let T=0;T<c.length;T+=1){const E=-Math.floor(g*100),S=Math.floor(c[T]*100),N=Math.floor(c[T+1]*100);typeof c[T+1]<"u"?E>=S&&E<N-(N-S)/2?o=T:E>=S&&E<N&&(o=T+1):E>=S&&(o=T)}if(i.initialized&&o!==f&&(!i.allowSlideNext&&(d?g>i.translate&&g>i.minTranslate():g<i.translate&&g<i.minTranslate())||!i.allowSlidePrev&&g>i.translate&&g>i.maxTranslate()&&(f||0)!==o))return!1;o!==(u||0)&&n&&i.emit("beforeSlideChangeStart"),i.updateProgress(g);let w;if(o>f?w="next":o<f?w="prev":w="reset",d&&-g===i.translate||!d&&g===i.translate)return i.updateActiveIndex(o),l.autoHeight&&i.updateAutoHeight(),i.updateSlidesClasses(),l.effect!=="slide"&&i.setTranslate(g),w!=="reset"&&(i.transitionStart(n,w),i.transitionEnd(n,w)),!1;if(l.cssMode){const T=i.isHorizontal(),E=d?g:-g;if(t===0){const S=i.virtual&&i.params.virtual.enabled;S&&(i.wrapperEl.style.scrollSnapType="none",i._immediateVirtual=!0),S&&!i._cssModeVirtualInitialSet&&i.params.initialSlide>0?(i._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{h[T?"scrollLeft":"scrollTop"]=E})):h[T?"scrollLeft":"scrollTop"]=E,S&&requestAnimationFrame(()=>{i.wrapperEl.style.scrollSnapType="",i._immediateVirtual=!1})}else{if(!i.support.smoothScroll)return gf({swiper:i,targetPosition:E,side:T?"left":"top"}),!0;h.scrollTo({[T?"left":"top"]:E,behavior:"smooth"})}return!0}return i.setTransition(t),i.setTranslate(g),i.updateActiveIndex(o),i.updateSlidesClasses(),i.emit("beforeTransitionStart",t,r),i.transitionStart(n,w),t===0?i.transitionEnd(n,w):i.animating||(i.animating=!0,i.onSlideToWrapperTransitionEnd||(i.onSlideToWrapperTransitionEnd=function(E){!i||i.destroyed||E.target===this&&(i.wrapperEl.removeEventListener("transitionend",i.onSlideToWrapperTransitionEnd),i.onSlideToWrapperTransitionEnd=null,delete i.onSlideToWrapperTransitionEnd,i.transitionEnd(n,w))}),i.wrapperEl.addEventListener("transitionend",i.onSlideToWrapperTransitionEnd)),!0}function w_(e,t,n,r){e===void 0&&(e=0),t===void 0&&(t=this.params.speed),n===void 0&&(n=!0),typeof e=="string"&&(e=parseInt(e,10));const s=this;let i=e;return s.params.loop&&(s.virtual&&s.params.virtual.enabled?i=i+s.virtual.slidesBefore:i=s.getSlideIndexByData(i)),s.slideTo(i,t,n,r)}function S_(e,t,n){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0);const r=this,{enabled:s,params:i,animating:o}=r;if(!s)return r;let l=i.slidesPerGroup;i.slidesPerView==="auto"&&i.slidesPerGroup===1&&i.slidesPerGroupAuto&&(l=Math.max(r.slidesPerViewDynamic("current",!0),1));const a=r.activeIndex<i.slidesPerGroupSkip?1:l,c=r.virtual&&i.virtual.enabled;if(i.loop){if(o&&!c&&i.loopPreventsSliding)return!1;r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft}return i.rewind&&r.isEnd?r.slideTo(0,e,t,n):r.slideTo(r.activeIndex+a,e,t,n)}function T_(e,t,n){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0);const r=this,{params:s,snapGrid:i,slidesGrid:o,rtlTranslate:l,enabled:a,animating:c}=r;if(!a)return r;const u=r.virtual&&s.virtual.enabled;if(s.loop){if(c&&!u&&s.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}const f=l?r.translate:-r.translate;function d(g){return g<0?-Math.floor(Math.abs(g)):Math.floor(g)}const h=d(f),v=i.map(g=>d(g));let b=i[v.indexOf(h)-1];if(typeof b>"u"&&s.cssMode){let g;i.forEach((w,T)=>{h>=w&&(g=T)}),typeof g<"u"&&(b=i[g>0?g-1:g])}let C=0;if(typeof b<"u"&&(C=o.indexOf(b),C<0&&(C=r.activeIndex-1),s.slidesPerView==="auto"&&s.slidesPerGroup===1&&s.slidesPerGroupAuto&&(C=C-r.slidesPerViewDynamic("previous",!0)+1,C=Math.max(C,0))),s.rewind&&r.isBeginning){const g=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1;return r.slideTo(g,e,t,n)}return r.slideTo(C,e,t,n)}function C_(e,t,n){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0);const r=this;return r.slideTo(r.activeIndex,e,t,n)}function O_(e,t,n,r){e===void 0&&(e=this.params.speed),t===void 0&&(t=!0),r===void 0&&(r=.5);const s=this;let i=s.activeIndex;const o=Math.min(s.params.slidesPerGroupSkip,i),l=o+Math.floor((i-o)/s.params.slidesPerGroup),a=s.rtlTranslate?s.translate:-s.translate;if(a>=s.snapGrid[l]){const c=s.snapGrid[l],u=s.snapGrid[l+1];a-c>(u-c)*r&&(i+=s.params.slidesPerGroup)}else{const c=s.snapGrid[l-1],u=s.snapGrid[l];a-c<=(u-c)*r&&(i-=s.params.slidesPerGroup)}return i=Math.max(i,0),i=Math.min(i,s.slidesGrid.length-1),s.slideTo(i,e,t,n)}function x_(){const e=this,{params:t,slidesEl:n}=e,r=t.slidesPerView==="auto"?e.slidesPerViewDynamic():t.slidesPerView;let s=e.clickedIndex,i;const o=e.isElement?"swiper-slide":".".concat(t.slideClass);if(t.loop){if(e.animating)return;i=parseInt(e.clickedSlide.getAttribute("data-swiper-slide-index"),10),t.centeredSlides?s<e.loopedSlides-r/2||s>e.slides.length-e.loopedSlides+r/2?(e.loopFix(),s=e.getSlideIndex(xt(n,"".concat(o,'[data-swiper-slide-index="').concat(i,'"]'))[0]),Vi(()=>{e.slideTo(s)})):e.slideTo(s):s>e.slides.length-r?(e.loopFix(),s=e.getSlideIndex(xt(n,"".concat(o,'[data-swiper-slide-index="').concat(i,'"]'))[0]),Vi(()=>{e.slideTo(s)})):e.slideTo(s)}else e.slideTo(s)}var P_={slideTo:E_,slideToLoop:w_,slideNext:S_,slidePrev:T_,slideReset:C_,slideToClosest:O_,slideToClickedSlide:x_};function L_(e){const t=this,{params:n,slidesEl:r}=t;if(!n.loop||t.virtual&&t.params.virtual.enabled)return;xt(r,".".concat(n.slideClass,", swiper-slide")).forEach((i,o)=>{i.setAttribute("data-swiper-slide-index",o)}),t.loopFix({slideRealIndex:e,direction:n.centeredSlides?void 0:"next"})}function A_(e){let{slideRealIndex:t,slideTo:n=!0,direction:r,setTranslate:s,activeSlideIndex:i,byController:o,byMousewheel:l}=e===void 0?{}:e;const a=this;if(!a.params.loop)return;a.emit("beforeLoopFix");const{slides:c,allowSlidePrev:u,allowSlideNext:f,slidesEl:d,params:h}=a;if(a.allowSlidePrev=!0,a.allowSlideNext=!0,a.virtual&&h.virtual.enabled){n&&(!h.centeredSlides&&a.snapIndex===0?a.slideTo(a.virtual.slides.length,0,!1,!0):h.centeredSlides&&a.snapIndex<h.slidesPerView?a.slideTo(a.virtual.slides.length+a.snapIndex,0,!1,!0):a.snapIndex===a.snapGrid.length-1&&a.slideTo(a.virtual.slidesBefore,0,!1,!0)),a.allowSlidePrev=u,a.allowSlideNext=f,a.emit("loopFix");return}const v=h.slidesPerView==="auto"?a.slidesPerViewDynamic():Math.ceil(parseFloat(h.slidesPerView,10));let b=h.loopedSlides||v;b%h.slidesPerGroup!==0&&(b+=h.slidesPerGroup-b%h.slidesPerGroup),a.loopedSlides=b;const C=[],g=[];let w=a.activeIndex;typeof i>"u"?i=a.getSlideIndex(a.slides.filter(M=>M.classList.contains(h.slideActiveClass))[0]):w=i;const T=r==="next"||!r,E=r==="prev"||!r;let S=0,N=0;if(i<b){S=Math.max(b-i,h.slidesPerGroup);for(let M=0;M<b-i;M+=1){const F=M-Math.floor(M/c.length)*c.length;C.push(c.length-F-1)}}else if(i>a.slides.length-b*2){N=Math.max(i-(a.slides.length-b*2),h.slidesPerGroup);for(let M=0;M<N;M+=1){const F=M-Math.floor(M/c.length)*c.length;g.push(F)}}if(E&&C.forEach(M=>{a.slides[M].swiperLoopMoveDOM=!0,d.prepend(a.slides[M]),a.slides[M].swiperLoopMoveDOM=!1}),T&&g.forEach(M=>{a.slides[M].swiperLoopMoveDOM=!0,d.append(a.slides[M]),a.slides[M].swiperLoopMoveDOM=!1}),a.recalcSlides(),h.slidesPerView==="auto"&&a.updateSlides(),h.watchSlidesProgress&&a.updateSlidesOffset(),n){if(C.length>0&&E)if(typeof t>"u"){const M=a.slidesGrid[w],x=a.slidesGrid[w+S]-M;l?a.setTranslate(a.translate-x):(a.slideTo(w+S,0,!1,!0),s&&(a.touches[a.isHorizontal()?"startX":"startY"]+=x,a.touchEventsData.currentTranslate=a.translate))}else s&&(a.slideToLoop(t,0,!1,!0),a.touchEventsData.currentTranslate=a.translate);else if(g.length>0&&T)if(typeof t>"u"){const M=a.slidesGrid[w],x=a.slidesGrid[w-N]-M;l?a.setTranslate(a.translate-x):(a.slideTo(w-N,0,!1,!0),s&&(a.touches[a.isHorizontal()?"startX":"startY"]+=x,a.touchEventsData.currentTranslate=a.translate))}else a.slideToLoop(t,0,!1,!0)}if(a.allowSlidePrev=u,a.allowSlideNext=f,a.controller&&a.controller.control&&!o){const M={slideRealIndex:t,slideTo:!1,direction:r,setTranslate:s,activeSlideIndex:i,byController:!0};Array.isArray(a.controller.control)?a.controller.control.forEach(F=>{!F.destroyed&&F.params.loop&&F.loopFix(M)}):a.controller.control instanceof a.constructor&&a.controller.control.params.loop&&a.controller.control.loopFix(M)}a.emit("loopFix")}function I_(){const e=this,{params:t,slidesEl:n}=e;if(!t.loop||e.virtual&&e.params.virtual.enabled)return;e.recalcSlides();const r=[];e.slides.forEach(s=>{const i=typeof s.swiperSlideIndex>"u"?s.getAttribute("data-swiper-slide-index")*1:s.swiperSlideIndex;r[i]=s}),e.slides.forEach(s=>{s.removeAttribute("data-swiper-slide-index")}),r.forEach(s=>{n.append(s)}),e.recalcSlides(),e.slideTo(e.realIndex,0)}var N_={loopCreate:L_,loopFix:A_,loopDestroy:I_};function R_(e){const t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;const n=t.params.touchEventsTarget==="container"?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),n.style.cursor="move",n.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})}function M_(){const e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e[e.params.touchEventsTarget==="container"?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}var F_={setGrabCursor:R_,unsetGrabCursor:M_};function k_(e,t){t===void 0&&(t=this);function n(r){if(!r||r===_n()||r===et())return null;r.assignedSlot&&(r=r.assignedSlot);const s=r.closest(e);return!s&&!r.getRootNode?null:s||n(r.getRootNode().host)}return n(t)}function D_(e){const t=this,n=_n(),r=et(),s=t.touchEventsData;s.evCache.push(e);const{params:i,touches:o,enabled:l}=t;if(!l||!i.simulateTouch&&e.pointerType==="mouse"||t.animating&&i.preventInteractionOnTransition)return;!t.animating&&i.cssMode&&i.loop&&t.loopFix();let a=e;a.originalEvent&&(a=a.originalEvent);let c=a.target;if(i.touchEventsTarget==="wrapper"&&!t.wrapperEl.contains(c)||"which"in a&&a.which===3||"button"in a&&a.button>0||s.isTouched&&s.isMoved)return;const u=!!i.noSwipingClass&&i.noSwipingClass!=="",f=e.composedPath?e.composedPath():e.path;u&&a.target&&a.target.shadowRoot&&f&&(c=f[0]);const d=i.noSwipingSelector?i.noSwipingSelector:".".concat(i.noSwipingClass),h=!!(a.target&&a.target.shadowRoot);if(i.noSwiping&&(h?k_(d,c):c.closest(d))){t.allowClick=!0;return}if(i.swipeHandler&&!c.closest(i.swipeHandler))return;o.currentX=a.pageX,o.currentY=a.pageY;const v=o.currentX,b=o.currentY,C=i.edgeSwipeDetection||i.iOSEdgeSwipeDetection,g=i.edgeSwipeThreshold||i.iOSEdgeSwipeThreshold;if(C&&(v<=g||v>=r.innerWidth-g))if(C==="prevent")e.preventDefault();else return;Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),o.startX=v,o.startY=b,s.touchStartTime=ss(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,i.threshold>0&&(s.allowThresholdMove=!1);let w=!0;c.matches(s.focusableElements)&&(w=!1,c.nodeName==="SELECT"&&(s.isTouched=!1)),n.activeElement&&n.activeElement.matches(s.focusableElements)&&n.activeElement!==c&&n.activeElement.blur();const T=w&&t.allowTouchMove&&i.touchStartPreventDefault;(i.touchStartForcePreventDefault||T)&&!c.isContentEditable&&a.preventDefault(),i.freeMode&&i.freeMode.enabled&&t.freeMode&&t.animating&&!i.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",a)}function B_(e){const t=_n(),n=this,r=n.touchEventsData,{params:s,touches:i,rtlTranslate:o,enabled:l}=n;if(!l||!s.simulateTouch&&e.pointerType==="mouse")return;let a=e;if(a.originalEvent&&(a=a.originalEvent),!r.isTouched){r.startMoving&&r.isScrolling&&n.emit("touchMoveOpposite",a);return}const c=r.evCache.findIndex(N=>N.pointerId===a.pointerId);c>=0&&(r.evCache[c]=a);const u=r.evCache.length>1?r.evCache[0]:a,f=u.pageX,d=u.pageY;if(a.preventedByNestedSwiper){i.startX=f,i.startY=d;return}if(!n.allowTouchMove){a.target.matches(r.focusableElements)||(n.allowClick=!1),r.isTouched&&(Object.assign(i,{startX:f,startY:d,prevX:n.touches.currentX,prevY:n.touches.currentY,currentX:f,currentY:d}),r.touchStartTime=ss());return}if(s.touchReleaseOnEdges&&!s.loop){if(n.isVertical()){if(d<i.startY&&n.translate<=n.maxTranslate()||d>i.startY&&n.translate>=n.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(f<i.startX&&n.translate<=n.maxTranslate()||f>i.startX&&n.translate>=n.minTranslate())return}if(t.activeElement&&a.target===t.activeElement&&a.target.matches(r.focusableElements)){r.isMoved=!0,n.allowClick=!1;return}if(r.allowTouchCallbacks&&n.emit("touchMove",a),a.targetTouches&&a.targetTouches.length>1)return;i.currentX=f,i.currentY=d;const h=i.currentX-i.startX,v=i.currentY-i.startY;if(n.params.threshold&&Math.sqrt(h**2+v**2)<n.params.threshold)return;if(typeof r.isScrolling>"u"){let N;n.isHorizontal()&&i.currentY===i.startY||n.isVertical()&&i.currentX===i.startX?r.isScrolling=!1:h*h+v*v>=25&&(N=Math.atan2(Math.abs(v),Math.abs(h))*180/Math.PI,r.isScrolling=n.isHorizontal()?N>s.touchAngle:90-N>s.touchAngle)}if(r.isScrolling&&n.emit("touchMoveOpposite",a),typeof r.startMoving>"u"&&(i.currentX!==i.startX||i.currentY!==i.startY)&&(r.startMoving=!0),r.isScrolling||n.zoom&&n.params.zoom&&n.params.zoom.enabled&&r.evCache.length>1){r.isTouched=!1;return}if(!r.startMoving)return;n.allowClick=!1,!s.cssMode&&a.cancelable&&a.preventDefault(),s.touchMoveStopPropagation&&!s.nested&&a.stopPropagation();let b=n.isHorizontal()?h:v,C=n.isHorizontal()?i.currentX-i.previousX:i.currentY-i.previousY;s.oneWayMovement&&(b=Math.abs(b)*(o?1:-1),C=Math.abs(C)*(o?1:-1)),i.diff=b,b*=s.touchRatio,o&&(b=-b,C=-C);const g=n.touchesDirection;n.swipeDirection=b>0?"prev":"next",n.touchesDirection=C>0?"prev":"next";const w=n.params.loop&&!s.cssMode;if(!r.isMoved){if(w&&n.loopFix({direction:n.swipeDirection}),r.startTranslate=n.getTranslate(),n.setTransition(0),n.animating){const N=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});n.wrapperEl.dispatchEvent(N)}r.allowMomentumBounce=!1,s.grabCursor&&(n.allowSlideNext===!0||n.allowSlidePrev===!0)&&n.setGrabCursor(!0),n.emit("sliderFirstMove",a)}let T;r.isMoved&&g!==n.touchesDirection&&w&&Math.abs(b)>=1&&(n.loopFix({direction:n.swipeDirection,setTranslate:!0}),T=!0),n.emit("sliderMove",a),r.isMoved=!0,r.currentTranslate=b+r.startTranslate;let E=!0,S=s.resistanceRatio;if(s.touchReleaseOnEdges&&(S=0),b>0?(w&&!T&&r.currentTranslate>(s.centeredSlides?n.minTranslate()-n.size/2:n.minTranslate())&&n.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>n.minTranslate()&&(E=!1,s.resistance&&(r.currentTranslate=n.minTranslate()-1+(-n.minTranslate()+r.startTranslate+b)**S))):b<0&&(w&&!T&&r.currentTranslate<(s.centeredSlides?n.maxTranslate()+n.size/2:n.maxTranslate())&&n.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:n.slides.length-(s.slidesPerView==="auto"?n.slidesPerViewDynamic():Math.ceil(parseFloat(s.slidesPerView,10)))}),r.currentTranslate<n.maxTranslate()&&(E=!1,s.resistance&&(r.currentTranslate=n.maxTranslate()+1-(n.maxTranslate()-r.startTranslate-b)**S))),E&&(a.preventedByNestedSwiper=!0),!n.allowSlideNext&&n.swipeDirection==="next"&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&n.swipeDirection==="prev"&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),!n.allowSlidePrev&&!n.allowSlideNext&&(r.currentTranslate=r.startTranslate),s.threshold>0)if(Math.abs(b)>s.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,i.startX=i.currentX,i.startY=i.currentY,r.currentTranslate=r.startTranslate,i.diff=n.isHorizontal()?i.currentX-i.startX:i.currentY-i.startY;return}}else{r.currentTranslate=r.startTranslate;return}!s.followFinger||s.cssMode||((s.freeMode&&s.freeMode.enabled&&n.freeMode||s.watchSlidesProgress)&&(n.updateActiveIndex(),n.updateSlidesClasses()),s.freeMode&&s.freeMode.enabled&&n.freeMode&&n.freeMode.onTouchMove(),n.updateProgress(r.currentTranslate),n.setTranslate(r.currentTranslate))}function $_(e){const t=this,n=t.touchEventsData,r=n.evCache.findIndex(T=>T.pointerId===e.pointerId);if(r>=0&&n.evCache.splice(r,1),["pointercancel","pointerout","pointerleave"].includes(e.type)&&!(e.type==="pointercancel"&&(t.browser.isSafari||t.browser.isWebView)))return;const{params:s,touches:i,rtlTranslate:o,slidesGrid:l,enabled:a}=t;if(!a||!s.simulateTouch&&e.pointerType==="mouse")return;let c=e;if(c.originalEvent&&(c=c.originalEvent),n.allowTouchCallbacks&&t.emit("touchEnd",c),n.allowTouchCallbacks=!1,!n.isTouched){n.isMoved&&s.grabCursor&&t.setGrabCursor(!1),n.isMoved=!1,n.startMoving=!1;return}s.grabCursor&&n.isMoved&&n.isTouched&&(t.allowSlideNext===!0||t.allowSlidePrev===!0)&&t.setGrabCursor(!1);const u=ss(),f=u-n.touchStartTime;if(t.allowClick){const T=c.path||c.composedPath&&c.composedPath();t.updateClickedSlide(T&&T[0]||c.target),t.emit("tap click",c),f<300&&u-n.lastClickTime<300&&t.emit("doubleTap doubleClick",c)}if(n.lastClickTime=ss(),Vi(()=>{t.destroyed||(t.allowClick=!0)}),!n.isTouched||!n.isMoved||!t.swipeDirection||i.diff===0||n.currentTranslate===n.startTranslate){n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;return}n.isTouched=!1,n.isMoved=!1,n.startMoving=!1;let d;if(s.followFinger?d=o?t.translate:-t.translate:d=-n.currentTranslate,s.cssMode)return;if(s.freeMode&&s.freeMode.enabled){t.freeMode.onTouchEnd({currentPos:d});return}let h=0,v=t.slidesSizesGrid[0];for(let T=0;T<l.length;T+=T<s.slidesPerGroupSkip?1:s.slidesPerGroup){const E=T<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;typeof l[T+E]<"u"?d>=l[T]&&d<l[T+E]&&(h=T,v=l[T+E]-l[T]):d>=l[T]&&(h=T,v=l[l.length-1]-l[l.length-2])}let b=null,C=null;s.rewind&&(t.isBeginning?C=s.virtual&&s.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(b=0));const g=(d-l[h])/v,w=h<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;if(f>s.longSwipesMs){if(!s.longSwipes){t.slideTo(t.activeIndex);return}t.swipeDirection==="next"&&(g>=s.longSwipesRatio?t.slideTo(s.rewind&&t.isEnd?b:h+w):t.slideTo(h)),t.swipeDirection==="prev"&&(g>1-s.longSwipesRatio?t.slideTo(h+w):C!==null&&g<0&&Math.abs(g)>s.longSwipesRatio?t.slideTo(C):t.slideTo(h))}else{if(!s.shortSwipes){t.slideTo(t.activeIndex);return}t.navigation&&(c.target===t.navigation.nextEl||c.target===t.navigation.prevEl)?c.target===t.navigation.nextEl?t.slideTo(h+w):t.slideTo(h):(t.swipeDirection==="next"&&t.slideTo(b!==null?b:h+w),t.swipeDirection==="prev"&&t.slideTo(C!==null?C:h))}}function Ul(){const e=this,{params:t,el:n}=e;if(n&&n.offsetWidth===0)return;t.breakpoints&&e.setBreakpoint();const{allowSlideNext:r,allowSlidePrev:s,snapGrid:i}=e,o=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();const l=o&&t.loop;(t.slidesPerView==="auto"||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides&&!l?e.slideTo(e.slides.length-1,0,!1,!0):e.params.loop&&!o?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=s,e.allowSlideNext=r,e.params.watchOverflow&&i!==e.snapGrid&&e.checkOverflow()}function j_(e){const t=this;t.enabled&&(t.allowClick||(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function H_(){const e=this,{wrapperEl:t,rtlTranslate:n,enabled:r}=e;if(!r)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,e.translate===0&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let s;const i=e.maxTranslate()-e.minTranslate();i===0?s=0:s=(e.translate-e.minTranslate())/i,s!==e.progress&&e.updateProgress(n?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}function U_(e){const t=this;Yr(t,e.target),!(t.params.cssMode||t.params.slidesPerView!=="auto"&&!t.params.autoHeight)&&t.update()}let Vl=!1;function V_(){}const yf=(e,t)=>{const n=_n(),{params:r,el:s,wrapperEl:i,device:o}=e,l=!!r.nested,a=t==="on"?"addEventListener":"removeEventListener",c=t;s[a]("pointerdown",e.onTouchStart,{passive:!1}),n[a]("pointermove",e.onTouchMove,{passive:!1,capture:l}),n[a]("pointerup",e.onTouchEnd,{passive:!0}),n[a]("pointercancel",e.onTouchEnd,{passive:!0}),n[a]("pointerout",e.onTouchEnd,{passive:!0}),n[a]("pointerleave",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&s[a]("click",e.onClick,!0),r.cssMode&&i[a]("scroll",e.onScroll),r.updateOnWindowResize?e[c](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",Ul,!0):e[c]("observerUpdate",Ul,!0),s[a]("load",e.onLoad,{capture:!0})};function W_(){const e=this,t=_n(),{params:n}=e;e.onTouchStart=D_.bind(e),e.onTouchMove=B_.bind(e),e.onTouchEnd=$_.bind(e),n.cssMode&&(e.onScroll=H_.bind(e)),e.onClick=j_.bind(e),e.onLoad=U_.bind(e),Vl||(t.addEventListener("touchstart",V_),Vl=!0),yf(e,"on")}function z_(){yf(this,"off")}var G_={attachEvents:W_,detachEvents:z_};const Wl=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;function K_(){const e=this,{realIndex:t,initialized:n,params:r,el:s}=e,i=r.breakpoints;if(!i||i&&Object.keys(i).length===0)return;const o=e.getBreakpoint(i,e.params.breakpointsBase,e.el);if(!o||e.currentBreakpoint===o)return;const a=(o in i?i[o]:void 0)||e.originalParams,c=Wl(e,r),u=Wl(e,a),f=r.enabled;c&&!u?(s.classList.remove("".concat(r.containerModifierClass,"grid"),"".concat(r.containerModifierClass,"grid-column")),e.emitContainerClasses()):!c&&u&&(s.classList.add("".concat(r.containerModifierClass,"grid")),(a.grid.fill&&a.grid.fill==="column"||!a.grid.fill&&r.grid.fill==="column")&&s.classList.add("".concat(r.containerModifierClass,"grid-column")),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(b=>{if(typeof a[b]>"u")return;const C=r[b]&&r[b].enabled,g=a[b]&&a[b].enabled;C&&!g&&e[b].disable(),!C&&g&&e[b].enable()});const d=a.direction&&a.direction!==r.direction,h=r.loop&&(a.slidesPerView!==r.slidesPerView||d);d&&n&&e.changeDirection(),Qe(e.params,a);const v=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),f&&!v?e.disable():!f&&v&&e.enable(),e.currentBreakpoint=o,e.emit("_beforeBreakpoint",a),h&&n&&(e.loopDestroy(),e.loopCreate(t),e.updateSlides()),e.emit("breakpoint",a)}function Y_(e,t,n){if(t===void 0&&(t="window"),!e||t==="container"&&!n)return;let r=!1;const s=et(),i=t==="window"?s.innerHeight:n.clientHeight,o=Object.keys(e).map(l=>{if(typeof l=="string"&&l.indexOf("@")===0){const a=parseFloat(l.substr(1));return{value:i*a,point:l}}return{value:l,point:l}});o.sort((l,a)=>parseInt(l.value,10)-parseInt(a.value,10));for(let l=0;l<o.length;l+=1){const{point:a,value:c}=o[l];t==="window"?s.matchMedia("(min-width: ".concat(c,"px)")).matches&&(r=a):c<=n.clientWidth&&(r=a)}return r||"max"}var q_={setBreakpoint:K_,getBreakpoint:Y_};function X_(e,t){const n=[];return e.forEach(r=>{typeof r=="object"?Object.keys(r).forEach(s=>{r[s]&&n.push(t+s)}):typeof r=="string"&&n.push(t+r)}),n}function J_(){const e=this,{classNames:t,params:n,rtl:r,el:s,device:i}=e,o=X_(["initialized",n.direction,{"free-mode":e.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:r},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&n.grid.fill==="column"},{android:i.android},{ios:i.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],n.containerModifierClass);t.push(...o),s.classList.add(...t),e.emitContainerClasses()}function Q_(){const e=this,{el:t,classNames:n}=e;t.classList.remove(...n),e.emitContainerClasses()}var Z_={addClasses:J_,removeClasses:Q_};function eE(){const e=this,{isLocked:t,params:n}=e,{slidesOffsetBefore:r}=n;if(r){const s=e.slides.length-1,i=e.slidesGrid[s]+e.slidesSizesGrid[s]+r*2;e.isLocked=e.size>i}else e.isLocked=e.snapGrid.length===1;n.allowSlideNext===!0&&(e.allowSlideNext=!e.isLocked),n.allowSlidePrev===!0&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}var tE={checkOverflow:eE},zi={init:!0,direction:"horizontal",oneWayMovement:!1,touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopedSlides:null,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};function nE(e,t){return function(r){r===void 0&&(r={});const s=Object.keys(r)[0],i=r[s];if(typeof i!="object"||i===null){Qe(t,r);return}if(["navigation","pagination","scrollbar"].indexOf(s)>=0&&e[s]===!0&&(e[s]={auto:!0}),!(s in e&&"enabled"in i)){Qe(t,r);return}e[s]===!0&&(e[s]={enabled:!0}),typeof e[s]=="object"&&!("enabled"in e[s])&&(e[s].enabled=!0),e[s]||(e[s]={enabled:!1}),Qe(t,r)}}const si={eventsEmitter:Zy,update:u_,translate:g_,transition:__,slide:P_,loop:N_,grabCursor:F_,events:G_,breakpoints:q_,checkOverflow:tE,classes:Z_},ii={};let Mo=class Ct{constructor(){let t,n;for(var r=arguments.length,s=new Array(r),i=0;i<r;i++)s[i]=arguments[i];s.length===1&&s[0].constructor&&Object.prototype.toString.call(s[0]).slice(8,-1)==="Object"?n=s[0]:[t,n]=s,n||(n={}),n=Qe({},n),t&&!n.el&&(n.el=t);const o=_n();if(n.el&&typeof n.el=="string"&&o.querySelectorAll(n.el).length>1){const u=[];return o.querySelectorAll(n.el).forEach(f=>{const d=Qe({},n,{el:f});u.push(new Ct(d))}),u}const l=this;l.__swiper__=!0,l.support=vf(),l.device=Yy({userAgent:n.userAgent}),l.browser=Xy(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],n.modules&&Array.isArray(n.modules)&&l.modules.push(...n.modules);const a={};l.modules.forEach(u=>{u({params:n,swiper:l,extendParams:nE(n,a),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});const c=Qe({},zi,a);return l.params=Qe({},c,ii,n),l.originalParams=Qe({},l.params),l.passedParams=Qe({},n),l.params&&l.params.on&&Object.keys(l.params.on).forEach(u=>{l.on(u,l.params.on[u])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:t,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal(){return l.params.direction==="horizontal"},isVertical(){return l.params.direction==="vertical"},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return Math.trunc(this.translate/2**23)*2**23},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,evCache:[]},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getSlideIndex(t){const{slidesEl:n,params:r}=this,s=xt(n,".".concat(r.slideClass,", swiper-slide")),i=jl(s[0]);return jl(t)-i}getSlideIndexByData(t){return this.getSlideIndex(this.slides.filter(n=>n.getAttribute("data-swiper-slide-index")*1===t)[0])}recalcSlides(){const t=this,{slidesEl:n,params:r}=t;t.slides=xt(n,".".concat(r.slideClass,", swiper-slide"))}enable(){const t=this;t.enabled||(t.enabled=!0,t.params.grabCursor&&t.setGrabCursor(),t.emit("enable"))}disable(){const t=this;t.enabled&&(t.enabled=!1,t.params.grabCursor&&t.unsetGrabCursor(),t.emit("disable"))}setProgress(t,n){const r=this;t=Math.min(Math.max(t,0),1);const s=r.minTranslate(),o=(r.maxTranslate()-s)*t+s;r.translateTo(o,typeof n>"u"?0:n),r.updateActiveIndex(),r.updateSlidesClasses()}emitContainerClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=t.el.className.split(" ").filter(r=>r.indexOf("swiper")===0||r.indexOf(t.params.containerModifierClass)===0);t.emit("_containerClasses",n.join(" "))}getSlideClasses(t){const n=this;return n.destroyed?"":t.className.split(" ").filter(r=>r.indexOf("swiper-slide")===0||r.indexOf(n.params.slideClass)===0).join(" ")}emitSlidesClasses(){const t=this;if(!t.params._emitClasses||!t.el)return;const n=[];t.slides.forEach(r=>{const s=t.getSlideClasses(r);n.push({slideEl:r,classNames:s}),t.emit("_slideClass",r,s)}),t.emit("_slideClasses",n)}slidesPerViewDynamic(t,n){t===void 0&&(t="current"),n===void 0&&(n=!1);const r=this,{params:s,slides:i,slidesGrid:o,slidesSizesGrid:l,size:a,activeIndex:c}=r;let u=1;if(s.centeredSlides){let f=i[c]?i[c].swiperSlideSize:0,d;for(let h=c+1;h<i.length;h+=1)i[h]&&!d&&(f+=i[h].swiperSlideSize,u+=1,f>a&&(d=!0));for(let h=c-1;h>=0;h-=1)i[h]&&!d&&(f+=i[h].swiperSlideSize,u+=1,f>a&&(d=!0))}else if(t==="current")for(let f=c+1;f<i.length;f+=1)(n?o[f]+l[f]-o[c]<a:o[f]-o[c]<a)&&(u+=1);else for(let f=c-1;f>=0;f-=1)o[c]-o[f]<a&&(u+=1);return u}update(){const t=this;if(!t||t.destroyed)return;const{snapGrid:n,params:r}=t;r.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(o=>{o.complete&&Yr(t,o)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses();function s(){const o=t.rtlTranslate?t.translate*-1:t.translate,l=Math.min(Math.max(o,t.maxTranslate()),t.minTranslate());t.setTranslate(l),t.updateActiveIndex(),t.updateSlidesClasses()}let i;if(r.freeMode&&r.freeMode.enabled&&!r.cssMode)s(),r.autoHeight&&t.updateAutoHeight();else{if((r.slidesPerView==="auto"||r.slidesPerView>1)&&t.isEnd&&!r.centeredSlides){const o=t.virtual&&r.virtual.enabled?t.virtual.slides:t.slides;i=t.slideTo(o.length-1,0,!1,!0)}else i=t.slideTo(t.activeIndex,0,!1,!0);i||s()}r.watchOverflow&&n!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(t,n){n===void 0&&(n=!0);const r=this,s=r.params.direction;return t||(t=s==="horizontal"?"vertical":"horizontal"),t===s||t!=="horizontal"&&t!=="vertical"||(r.el.classList.remove("".concat(r.params.containerModifierClass).concat(s)),r.el.classList.add("".concat(r.params.containerModifierClass).concat(t)),r.emitContainerClasses(),r.params.direction=t,r.slides.forEach(i=>{t==="vertical"?i.style.width="":i.style.height=""}),r.emit("changeDirection"),n&&r.update()),r}changeLanguageDirection(t){const n=this;n.rtl&&t==="rtl"||!n.rtl&&t==="ltr"||(n.rtl=t==="rtl",n.rtlTranslate=n.params.direction==="horizontal"&&n.rtl,n.rtl?(n.el.classList.add("".concat(n.params.containerModifierClass,"rtl")),n.el.dir="rtl"):(n.el.classList.remove("".concat(n.params.containerModifierClass,"rtl")),n.el.dir="ltr"),n.update())}mount(t){const n=this;if(n.mounted)return!0;let r=t||n.params.el;if(typeof r=="string"&&(r=document.querySelector(r)),!r)return!1;r.swiper=n,r.parentNode&&r.parentNode.host&&r.parentNode.host.nodeName==="SWIPER-CONTAINER"&&(n.isElement=!0);const s=()=>".".concat((n.params.wrapperClass||"").trim().split(" ").join("."));let o=(()=>r&&r.shadowRoot&&r.shadowRoot.querySelector?r.shadowRoot.querySelector(s()):xt(r,s())[0])();return!o&&n.params.createElements&&(o=Uy("div",n.params.wrapperClass),r.append(o),xt(r,".".concat(n.params.slideClass)).forEach(l=>{o.append(l)})),Object.assign(n,{el:r,wrapperEl:o,slidesEl:n.isElement&&!r.parentNode.host.slideSlots?r.parentNode.host:o,hostEl:n.isElement?r.parentNode.host:r,mounted:!0,rtl:r.dir.toLowerCase()==="rtl"||Vt(r,"direction")==="rtl",rtlTranslate:n.params.direction==="horizontal"&&(r.dir.toLowerCase()==="rtl"||Vt(r,"direction")==="rtl"),wrongRTL:Vt(o,"display")==="-webkit-box"}),!0}init(t){const n=this;return n.initialized||n.mount(t)===!1||(n.emit("beforeInit"),n.params.breakpoints&&n.setBreakpoint(),n.addClasses(),n.updateSize(),n.updateSlides(),n.params.watchOverflow&&n.checkOverflow(),n.params.grabCursor&&n.enabled&&n.setGrabCursor(),n.params.loop&&n.virtual&&n.params.virtual.enabled?n.slideTo(n.params.initialSlide+n.virtual.slidesBefore,0,n.params.runCallbacksOnInit,!1,!0):n.slideTo(n.params.initialSlide,0,n.params.runCallbacksOnInit,!1,!0),n.params.loop&&n.loopCreate(),n.attachEvents(),[...n.el.querySelectorAll('[loading="lazy"]')].forEach(s=>{s.complete?Yr(n,s):s.addEventListener("load",i=>{Yr(n,i.target)})}),Wi(n),n.initialized=!0,Wi(n),n.emit("init"),n.emit("afterInit")),n}destroy(t,n){t===void 0&&(t=!0),n===void 0&&(n=!0);const r=this,{params:s,el:i,wrapperEl:o,slides:l}=r;return typeof r.params>"u"||r.destroyed||(r.emit("beforeDestroy"),r.initialized=!1,r.detachEvents(),s.loop&&r.loopDestroy(),n&&(r.removeClasses(),i.removeAttribute("style"),o.removeAttribute("style"),l&&l.length&&l.forEach(a=>{a.classList.remove(s.slideVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass),a.removeAttribute("style"),a.removeAttribute("data-swiper-slide-index")})),r.emit("destroy"),Object.keys(r.eventsListeners).forEach(a=>{r.off(a)}),t!==!1&&(r.el.swiper=null,By(r)),r.destroyed=!0),null}static extendDefaults(t){Qe(ii,t)}static get extendedDefaults(){return ii}static get defaults(){return zi}static installModule(t){Ct.prototype.__modules__||(Ct.prototype.__modules__=[]);const n=Ct.prototype.__modules__;typeof t=="function"&&n.indexOf(t)<0&&n.push(t)}static use(t){return Array.isArray(t)?(t.forEach(n=>Ct.installModule(n)),Ct):(Ct.installModule(t),Ct)}};Object.keys(si).forEach(e=>{Object.keys(si[e]).forEach(t=>{Mo.prototype[t]=si[e][t]})});Mo.use([Jy,Qy]);const _f=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopedSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideNextClass","slidePrevClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function gn(e){return typeof e=="object"&&e!==null&&e.constructor&&Object.prototype.toString.call(e).slice(8,-1)==="Object"}function hn(e,t){const n=["__proto__","constructor","prototype"];Object.keys(t).filter(r=>n.indexOf(r)<0).forEach(r=>{typeof e[r]>"u"?e[r]=t[r]:gn(t[r])&&gn(e[r])&&Object.keys(t[r]).length>0?t[r].__swiper__?e[r]=t[r]:hn(e[r],t[r]):e[r]=t[r]})}function Ef(e){return e===void 0&&(e={}),e.navigation&&typeof e.navigation.nextEl>"u"&&typeof e.navigation.prevEl>"u"}function wf(e){return e===void 0&&(e={}),e.pagination&&typeof e.pagination.el>"u"}function Sf(e){return e===void 0&&(e={}),e.scrollbar&&typeof e.scrollbar.el>"u"}function Tf(e){e===void 0&&(e="");const t=e.split(" ").map(r=>r.trim()).filter(r=>!!r),n=[];return t.forEach(r=>{n.indexOf(r)<0&&n.push(r)}),n.join(" ")}function rE(e){return e===void 0&&(e=""),e?e.includes("swiper-wrapper")?e:"swiper-wrapper ".concat(e):"swiper-wrapper"}function sE(e){let{swiper:t,slides:n,passedParams:r,changedParams:s,nextEl:i,prevEl:o,scrollbarEl:l,paginationEl:a}=e;const c=s.filter(x=>x!=="children"&&x!=="direction"&&x!=="wrapperClass"),{params:u,pagination:f,navigation:d,scrollbar:h,virtual:v,thumbs:b}=t;let C,g,w,T,E,S,N,M;s.includes("thumbs")&&r.thumbs&&r.thumbs.swiper&&u.thumbs&&!u.thumbs.swiper&&(C=!0),s.includes("controller")&&r.controller&&r.controller.control&&u.controller&&!u.controller.control&&(g=!0),s.includes("pagination")&&r.pagination&&(r.pagination.el||a)&&(u.pagination||u.pagination===!1)&&f&&!f.el&&(w=!0),s.includes("scrollbar")&&r.scrollbar&&(r.scrollbar.el||l)&&(u.scrollbar||u.scrollbar===!1)&&h&&!h.el&&(T=!0),s.includes("navigation")&&r.navigation&&(r.navigation.prevEl||o)&&(r.navigation.nextEl||i)&&(u.navigation||u.navigation===!1)&&d&&!d.prevEl&&!d.nextEl&&(E=!0);const F=x=>{t[x]&&(t[x].destroy(),x==="navigation"?(t.isElement&&(t[x].prevEl.remove(),t[x].nextEl.remove()),u[x].prevEl=void 0,u[x].nextEl=void 0,t[x].prevEl=void 0,t[x].nextEl=void 0):(t.isElement&&t[x].el.remove(),u[x].el=void 0,t[x].el=void 0))};s.includes("loop")&&t.isElement&&(u.loop&&!r.loop?S=!0:!u.loop&&r.loop?N=!0:M=!0),c.forEach(x=>{if(gn(u[x])&&gn(r[x]))hn(u[x],r[x]),(x==="navigation"||x==="pagination"||x==="scrollbar")&&"enabled"in r[x]&&!r[x].enabled&&F(x);else{const W=r[x];(W===!0||W===!1)&&(x==="navigation"||x==="pagination"||x==="scrollbar")?W===!1&&F(x):u[x]=r[x]}}),c.includes("controller")&&!g&&t.controller&&t.controller.control&&u.controller&&u.controller.control&&(t.controller.control=u.controller.control),s.includes("children")&&n&&v&&u.virtual.enabled&&(v.slides=n,v.update(!0)),s.includes("children")&&n&&u.loop&&(M=!0),C&&b.init()&&b.update(!0),g&&(t.controller.control=u.controller.control),w&&(t.isElement&&(!a||typeof a=="string")&&(a=document.createElement("div"),a.classList.add("swiper-pagination"),t.el.appendChild(a)),a&&(u.pagination.el=a),f.init(),f.render(),f.update()),T&&(t.isElement&&(!l||typeof l=="string")&&(l=document.createElement("div"),l.classList.add("swiper-scrollbar"),t.el.appendChild(l)),l&&(u.scrollbar.el=l),h.init(),h.updateSize(),h.setTranslate()),E&&(t.isElement&&((!i||typeof i=="string")&&(i=document.createElement("div"),i.classList.add("swiper-button-next"),i.innerHTML=t.hostEl.nextButtonSvg,t.el.appendChild(i)),(!o||typeof o=="string")&&(o=document.createElement("div"),o.classList.add("swiper-button-prev"),i.innerHTML=t.hostEl.prevButtonSvg,t.el.appendChild(o))),i&&(u.navigation.nextEl=i),o&&(u.navigation.prevEl=o),d.init(),d.update()),s.includes("allowSlideNext")&&(t.allowSlideNext=r.allowSlideNext),s.includes("allowSlidePrev")&&(t.allowSlidePrev=r.allowSlidePrev),s.includes("direction")&&t.changeDirection(r.direction,!1),(S||M)&&t.loopDestroy(),(N||M)&&t.loopCreate(),t.update()}function zl(e,t){e===void 0&&(e={}),t===void 0&&(t=!0);const n={on:{}},r={},s={};hn(n,zi),n._emitClasses=!0,n.init=!1;const i={},o=_f.map(a=>a.replace(/_/,"")),l=Object.assign({},e);return Object.keys(l).forEach(a=>{typeof e[a]>"u"||(o.indexOf(a)>=0?gn(e[a])?(n[a]={},s[a]={},hn(n[a],e[a]),hn(s[a],e[a])):(n[a]=e[a],s[a]=e[a]):a.search(/on[A-Z]/)===0&&typeof e[a]=="function"?t?r["".concat(a[2].toLowerCase()).concat(a.substr(3))]=e[a]:n.on["".concat(a[2].toLowerCase()).concat(a.substr(3))]=e[a]:i[a]=e[a])}),["navigation","pagination","scrollbar"].forEach(a=>{n[a]===!0&&(n[a]={}),n[a]===!1&&delete n[a]}),{params:n,passedParams:s,rest:i,events:r}}function iE(e,t){let{el:n,nextEl:r,prevEl:s,paginationEl:i,scrollbarEl:o,swiper:l}=e;Ef(t)&&r&&s&&(l.params.navigation.nextEl=r,l.originalParams.navigation.nextEl=r,l.params.navigation.prevEl=s,l.originalParams.navigation.prevEl=s),wf(t)&&i&&(l.params.pagination.el=i,l.originalParams.pagination.el=i),Sf(t)&&o&&(l.params.scrollbar.el=o,l.originalParams.scrollbar.el=o),l.init(n)}function oE(e,t,n,r,s){const i=[];if(!t)return i;const o=a=>{i.indexOf(a)<0&&i.push(a)};if(n&&r){const a=r.map(s),c=n.map(s);a.join("")!==c.join("")&&o("children"),r.length!==n.length&&o("children")}return _f.filter(a=>a[0]==="_").map(a=>a.replace(/_/,"")).forEach(a=>{if(a in e&&a in t)if(gn(e[a])&&gn(t[a])){const c=Object.keys(e[a]),u=Object.keys(t[a]);c.length!==u.length?o(a):(c.forEach(f=>{e[a][f]!==t[a][f]&&o(a)}),u.forEach(f=>{e[a][f]!==t[a][f]&&o(a)}))}else e[a]!==t[a]&&o(a)}),i}const aE=e=>{!e||e.destroyed||!e.params.virtual||e.params.virtual&&!e.params.virtual.enabled||(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function oi(e,t,n){e===void 0&&(e={});const r=[],s={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]},i=(o,l)=>{Array.isArray(o)&&o.forEach(a=>{const c=typeof a.type=="symbol";l==="default"&&(l="container-end"),c&&a.children?i(a.children,l):a.type&&(a.type.name==="SwiperSlide"||a.type.name==="AsyncComponentWrapper")?r.push(a):s[l]&&s[l].push(a)})};return Object.keys(e).forEach(o=>{if(typeof e[o]!="function")return;const l=e[o]();i(l,o)}),n.value=t.value,t.value=r,{slides:r,slots:s}}function lE(e,t,n){if(!n)return null;const r=u=>{let f=u;return u<0?f=t.length+u:f>=t.length&&(f=f-t.length),f},s=e.value.isHorizontal()?{[e.value.rtlTranslate?"right":"left"]:"".concat(n.offset,"px")}:{top:"".concat(n.offset,"px")},{from:i,to:o}=n,l=e.value.params.loop?-t.length:0,a=e.value.params.loop?t.length*2:t.length,c=[];for(let u=l;u<a;u+=1)u>=i&&u<=o&&c.push(t[r(u)]);return c.map(u=>(u.props||(u.props={}),u.props.style||(u.props.style={}),u.props.swiperRef=e,u.props.style=s,$e(u.type,{...u.props},u.children)))}const NE={name:"Swiper",props:{tag:{type:String,default:"div"},wrapperTag:{type:String,default:"div"},modules:{type:Array,default:void 0},init:{type:Boolean,default:void 0},direction:{type:String,default:void 0},oneWayMovement:{type:Boolean,default:void 0},touchEventsTarget:{type:String,default:void 0},initialSlide:{type:Number,default:void 0},speed:{type:Number,default:void 0},cssMode:{type:Boolean,default:void 0},updateOnWindowResize:{type:Boolean,default:void 0},resizeObserver:{type:Boolean,default:void 0},nested:{type:Boolean,default:void 0},focusableElements:{type:String,default:void 0},width:{type:Number,default:void 0},height:{type:Number,default:void 0},preventInteractionOnTransition:{type:Boolean,default:void 0},userAgent:{type:String,default:void 0},url:{type:String,default:void 0},edgeSwipeDetection:{type:[Boolean,String],default:void 0},edgeSwipeThreshold:{type:Number,default:void 0},autoHeight:{type:Boolean,default:void 0},setWrapperSize:{type:Boolean,default:void 0},virtualTranslate:{type:Boolean,default:void 0},effect:{type:String,default:void 0},breakpoints:{type:Object,default:void 0},spaceBetween:{type:[Number,String],default:void 0},slidesPerView:{type:[Number,String],default:void 0},maxBackfaceHiddenSlides:{type:Number,default:void 0},slidesPerGroup:{type:Number,default:void 0},slidesPerGroupSkip:{type:Number,default:void 0},slidesPerGroupAuto:{type:Boolean,default:void 0},centeredSlides:{type:Boolean,default:void 0},centeredSlidesBounds:{type:Boolean,default:void 0},slidesOffsetBefore:{type:Number,default:void 0},slidesOffsetAfter:{type:Number,default:void 0},normalizeSlideIndex:{type:Boolean,default:void 0},centerInsufficientSlides:{type:Boolean,default:void 0},watchOverflow:{type:Boolean,default:void 0},roundLengths:{type:Boolean,default:void 0},touchRatio:{type:Number,default:void 0},touchAngle:{type:Number,default:void 0},simulateTouch:{type:Boolean,default:void 0},shortSwipes:{type:Boolean,default:void 0},longSwipes:{type:Boolean,default:void 0},longSwipesRatio:{type:Number,default:void 0},longSwipesMs:{type:Number,default:void 0},followFinger:{type:Boolean,default:void 0},allowTouchMove:{type:Boolean,default:void 0},threshold:{type:Number,default:void 0},touchMoveStopPropagation:{type:Boolean,default:void 0},touchStartPreventDefault:{type:Boolean,default:void 0},touchStartForcePreventDefault:{type:Boolean,default:void 0},touchReleaseOnEdges:{type:Boolean,default:void 0},uniqueNavElements:{type:Boolean,default:void 0},resistance:{type:Boolean,default:void 0},resistanceRatio:{type:Number,default:void 0},watchSlidesProgress:{type:Boolean,default:void 0},grabCursor:{type:Boolean,default:void 0},preventClicks:{type:Boolean,default:void 0},preventClicksPropagation:{type:Boolean,default:void 0},slideToClickedSlide:{type:Boolean,default:void 0},loop:{type:Boolean,default:void 0},loopedSlides:{type:Number,default:void 0},loopPreventsSliding:{type:Boolean,default:void 0},rewind:{type:Boolean,default:void 0},allowSlidePrev:{type:Boolean,default:void 0},allowSlideNext:{type:Boolean,default:void 0},swipeHandler:{type:Boolean,default:void 0},noSwiping:{type:Boolean,default:void 0},noSwipingClass:{type:String,default:void 0},noSwipingSelector:{type:String,default:void 0},passiveListeners:{type:Boolean,default:void 0},containerModifierClass:{type:String,default:void 0},slideClass:{type:String,default:void 0},slideActiveClass:{type:String,default:void 0},slideVisibleClass:{type:String,default:void 0},slideNextClass:{type:String,default:void 0},slidePrevClass:{type:String,default:void 0},wrapperClass:{type:String,default:void 0},lazyPreloaderClass:{type:String,default:void 0},lazyPreloadPrevNext:{type:Number,default:void 0},runCallbacksOnInit:{type:Boolean,default:void 0},observer:{type:Boolean,default:void 0},observeParents:{type:Boolean,default:void 0},observeSlideChildren:{type:Boolean,default:void 0},a11y:{type:[Boolean,Object],default:void 0},autoplay:{type:[Boolean,Object],default:void 0},controller:{type:Object,default:void 0},coverflowEffect:{type:Object,default:void 0},cubeEffect:{type:Object,default:void 0},fadeEffect:{type:Object,default:void 0},flipEffect:{type:Object,default:void 0},creativeEffect:{type:Object,default:void 0},cardsEffect:{type:Object,default:void 0},hashNavigation:{type:[Boolean,Object],default:void 0},history:{type:[Boolean,Object],default:void 0},keyboard:{type:[Boolean,Object],default:void 0},mousewheel:{type:[Boolean,Object],default:void 0},navigation:{type:[Boolean,Object],default:void 0},pagination:{type:[Boolean,Object],default:void 0},parallax:{type:[Boolean,Object],default:void 0},scrollbar:{type:[Boolean,Object],default:void 0},thumbs:{type:Object,default:void 0},virtual:{type:[Boolean,Object],default:void 0},zoom:{type:[Boolean,Object],default:void 0},grid:{type:[Object],default:void 0},freeMode:{type:[Boolean,Object],default:void 0},enabled:{type:Boolean,default:void 0}},emits:["_beforeBreakpoint","_containerClasses","_slideClass","_slideClasses","_swiper","_freeModeNoMomentumRelease","activeIndexChange","afterInit","autoplay","autoplayStart","autoplayStop","autoplayPause","autoplayResume","autoplayTimeLeft","beforeDestroy","beforeInit","beforeLoopFix","beforeResize","beforeSlideChangeStart","beforeTransitionStart","breakpoint","changeDirection","click","disable","doubleTap","doubleClick","destroy","enable","fromEdge","hashChange","hashSet","init","keyPress","lock","loopFix","momentumBounce","navigationHide","navigationShow","navigationPrev","navigationNext","observerUpdate","orientationchange","paginationHide","paginationRender","paginationShow","paginationUpdate","progress","reachBeginning","reachEnd","realIndexChange","resize","scroll","scrollbarDragEnd","scrollbarDragMove","scrollbarDragStart","setTransition","setTranslate","slideChange","slideChangeTransitionEnd","slideChangeTransitionStart","slideNextTransitionEnd","slideNextTransitionStart","slidePrevTransitionEnd","slidePrevTransitionStart","slideResetTransitionStart","slideResetTransitionEnd","sliderMove","sliderFirstMove","slidesLengthChange","slidesGridLengthChange","snapGridLengthChange","snapIndexChange","swiper","tap","toEdge","touchEnd","touchMove","touchMoveOpposite","touchStart","transitionEnd","transitionStart","unlock","update","virtualUpdate","zoomChange"],setup(e,t){let{slots:n,emit:r}=t;const{tag:s,wrapperTag:i}=e,o=Q("swiper"),l=Q(null),a=Q(!1),c=Q(!1),u=Q(null),f=Q(null),d=Q(null),h={value:[]},v={value:[]},b=Q(null),C=Q(null),g=Q(null),w=Q(null),{params:T,passedParams:E}=zl(e,!1);oi(n,h,v),d.value=E,v.value=h.value;const S=()=>{oi(n,h,v),a.value=!0};T.onAny=function(F){for(var x=arguments.length,W=new Array(x>1?x-1:0),P=1;P<x;P++)W[P-1]=arguments[P];r(F,...W)},Object.assign(T.on,{_beforeBreakpoint:S,_containerClasses(F,x){o.value=x}});const N={...T};if(delete N.wrapperClass,f.value=new Mo(N),f.value.virtual&&f.value.params.virtual.enabled){f.value.virtual.slides=h.value;const F={cache:!1,slides:h.value,renderExternal:x=>{l.value=x},renderExternalUpdate:!1};hn(f.value.params.virtual,F),hn(f.value.originalParams.virtual,F)}uo(()=>{!c.value&&f.value&&(f.value.emitSlidesClasses(),c.value=!0);const{passedParams:F}=zl(e,!1),x=oE(F,d.value,h.value,v.value,W=>W.props&&W.props.key);d.value=F,(x.length||a.value)&&f.value&&!f.value.destroyed&&sE({swiper:f.value,slides:h.value,passedParams:F,changedParams:x,nextEl:b.value,prevEl:C.value,scrollbarEl:w.value,paginationEl:g.value}),a.value=!1}),pn("swiper",f),De(l,()=>{Hn(()=>{aE(f.value)})}),Jt(()=>{u.value&&(iE({el:u.value,nextEl:b.value,prevEl:C.value,paginationEl:g.value,scrollbarEl:w.value,swiper:f.value},T),r("swiper",f.value))}),Er(()=>{f.value&&!f.value.destroyed&&f.value.destroy(!0,!1)});function M(F){return T.virtual?lE(f,F,l.value):(F.forEach((x,W)=>{x.props||(x.props={}),x.props.swiperRef=f,x.props.swiperSlideIndex=W}),F)}return()=>{const{slides:F,slots:x}=oi(n,h,v);return $e(s,{ref:u,class:Tf(o.value)},[x["container-start"],$e(i,{class:rE(T.wrapperClass)},[x["wrapper-start"],M(F),x["wrapper-end"]]),Ef(e)&&[$e("div",{ref:C,class:"swiper-button-prev"}),$e("div",{ref:b,class:"swiper-button-next"})],Sf(e)&&$e("div",{ref:w,class:"swiper-scrollbar"}),wf(e)&&$e("div",{ref:g,class:"swiper-pagination"}),x["container-end"]])}}},RE={name:"SwiperSlide",props:{tag:{type:String,default:"div"},swiperRef:{type:Object,required:!1},swiperSlideIndex:{type:Number,default:void 0,required:!1},zoom:{type:Boolean,default:void 0,required:!1},lazy:{type:Boolean,default:!1,required:!1},virtualIndex:{type:[String,Number],default:void 0}},setup(e,t){let{slots:n}=t,r=!1;const{swiperRef:s}=e,i=Q(null),o=Q("swiper-slide"),l=Q(!1);function a(f,d,h){d===i.value&&(o.value=h)}Jt(()=>{!s||!s.value||(s.value.on("_slideClass",a),r=!0)}),Rc(()=>{r||!s||!s.value||(s.value.on("_slideClass",a),r=!0)}),uo(()=>{!i.value||!s||!s.value||(typeof e.swiperSlideIndex<"u"&&(i.value.swiperSlideIndex=e.swiperSlideIndex),s.value.destroyed&&o.value!=="swiper-slide"&&(o.value="swiper-slide"))}),Er(()=>{!s||!s.value||s.value.off("_slideClass",a)});const c=fe(()=>({isActive:o.value.indexOf("swiper-slide-active")>=0,isVisible:o.value.indexOf("swiper-slide-visible")>=0,isPrev:o.value.indexOf("swiper-slide-prev")>=0,isNext:o.value.indexOf("swiper-slide-next")>=0}));pn("swiperSlide",c);const u=()=>{l.value=!0};return()=>$e(e.tag,{class:Tf("".concat(o.value)),ref:i,"data-swiper-slide-index":typeof e.virtualIndex>"u"&&s&&s.value&&s.value.params.loop?e.swiperSlideIndex:e.virtualIndex,onLoadCapture:u},e.zoom?$e("div",{class:"swiper-zoom-container","data-swiper-zoom":typeof e.zoom=="number"?e.zoom:void 0},[n.default&&n.default(c.value),e.lazy&&!l.value&&$e("div",{class:"swiper-lazy-preloader"})]):[n.default&&n.default(c.value),e.lazy&&!l.value&&$e("div",{class:"swiper-lazy-preloader"})])}};export{Qi as A,yt as B,pE as C,gE as D,ee as E,Ge as F,xc as G,vE as H,Yt as I,mp as J,Qc as K,nb as L,SE as M,mE as N,ub as O,Ji as P,bE as Q,RE as R,OE as S,NE as T,EE as a,Gc as b,wE as c,Nt as d,PE as e,It as f,fe as g,xE as h,IE as i,AE as j,Zp as k,_E as l,Q as m,Yc as n,Wc as o,uE as p,fE as q,dE as r,CE as s,cE as t,yE as u,LE as v,Od as w,De as x,Jt as y,hE as z};
