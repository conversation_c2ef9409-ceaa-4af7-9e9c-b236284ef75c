import{d as V,m as g,b as M,w as O,O as X,o as l,n as t,t as o,p as K,q as R,v as nt,x as ot,y as Z,r as lt,z as _,A,B as e,F as h,C as U,D as m,E as I,s as it,S as _t,G as ct,H as ut,I as rt}from"./vendor-20bb6b23.js";import{u as q,g as dt,b as vt,a as x,c as mt,i as z,f as E,d as Y,e as Q,h as W,j as gt}from"./index-dd96cb3d.js";import{_ as j,a as ft,R as tt,b as pt,l as N,T as st,I as ht}from"./InvalidUser-204184d6.js";const $t=u=>(K("data-v-49f4534c"),u=u(),R(),u),bt={class:"wrapper"},yt={class:"body"},It={class:"title"},wt=$t(()=>t("div",{class:"line"},null,-1)),kt={class:"content bet"},At=["innerHTML"],Ct=["innerHTML"],Tt={class:"btns"},Bt={class:"tips"},St=V({__name:"BetComp",props:{teamKey:{default:""},teamId:{default:""},coin:{default:0},odds:{default:"0"},activityId:{default:""},group:{default:""}},emits:["betted"],setup(u,{expose:p,emit:d}){const s=u,{updateTeamOdds:$,updateUserCoin:w}=q(),c=g(!1),T=()=>{B(),c.value=!0},B=()=>{dt({activity_id:s.activityId,group:s.group}).then(n=>$(n))};p({show:T});const k=g(!1),L=()=>{k.value||(k.value=!0,vt({activity_id:s.activityId,group:s.group,team_id:s.teamId,bet_nums:s.coin}).then(n=>{x("bet",{activity_id:s.activityId,group:s.group,team_id:s.teamId,bet_nums:s.coin}),B(),w(n.allstar_coin),d("betted"),c.value=!1}).catch(()=>{setTimeout(()=>{window.location.reload()},300)}).finally(()=>{k.value=!1}))};return(n,b)=>{const v=X;return l(),M(v,{show:c.value},{default:O(()=>[t("div",bt,[t("div",yt,[t("div",{class:"close",onClick:b[0]||(b[0]=H=>c.value=!1)}),t("div",It,o(n.$t("all_star_guess_bet_title")),1),wt,t("div",kt,[t("div",{class:"txt",innerHTML:n.$t("all_star_guess_bet_infomation_1",[n.$t("all_star_".concat(s.teamKey,"_name")),s.coin])},null,8,At),t("div",{class:"odds",innerHTML:n.$t("all_star_guess_bet_infomation_2",[,s.odds])},null,8,Ct),t("div",Tt,[t("button",{class:"btn cancel",onClick:b[1]||(b[1]=H=>c.value=!1)},o(n.$t("all_star_guess_bet_pop_button_1")),1),t("button",{class:"btn submit",onClick:L},o(n.$t("all_star_guess_bet_pop_button_2")),1)]),t("div",Bt,o(n.$t("all_star_guess_bet_infomation_3")),1)])])])]),_:1},8,["show"])}}});const Lt=j(St,[["__scopeId","data-v-49f4534c"]]),C=u=>(K("data-v-6cf694f2"),u=u(),R(),u),Mt={class:"main-page"},Nt={class:"top"},Ht=C(()=>t("div",{class:"logo"},null,-1)),Dt={class:"act-time"},Pt={class:"t"},Et={class:"b"},Ut=["onClick"],Vt={class:"time"},Ft={class:"title"},Ot={key:0,class:"my-coin"},zt={class:"l"},Kt={class:"txt"},Rt=C(()=>t("div",{class:"icon"},null,-1)),qt={class:"count"},jt={key:1,class:"section betting"},Gt={class:"section-title"},Jt={class:"betting-list"},Yt=["onClick"],Qt={class:"title"},Wt={class:"odds"},Xt=["text"],Zt={class:"team"},xt={class:"guess"},ts={key:0,class:"guess-wrap"},ss=["max"],es={class:"slider"},as=C(()=>t("div",{class:"limit min"},"0",-1)),ns={class:"limit max"},os=["disabled"],ls={class:"tips"},is={key:1,class:"guess-wrap"},_s={class:"tips"},cs={key:0,class:"guess-wrap guess-txt"},us=["innerHTML"],rs={key:0,class:"guess-wrap guess-txt"},ds=["innerHTML"],vs={key:1,class:"guess-wrap guess-txt"},ms=["innerHTML"],gs=C(()=>t("div",{class:"line van-hairline--top"},null,-1)),fs={class:"teams-list"},ps=["onClick"],hs={key:0,class:"winner"},$s={key:1,class:"same-server"},bs={class:"team-info"},ys={key:0,class:"same-alliance"},Is={class:"numericals"},ws=C(()=>t("div",{class:"l-line"},null,-1)),ks={class:"team-name"},As=C(()=>t("div",{class:"s-line"},null,-1)),Cs={key:2,class:"section betting"},Ts={class:"guess guess-unopen"},Bs={class:"guess-wrap"},Ss={class:"tips"},Ls={class:"wrapper"},Ms={class:"body"},Ns={class:"title"},Hs=C(()=>t("div",{class:"line"},null,-1)),Ds={class:"content first-bet"},Ps={class:"txt"},Es=C(()=>t("div",{class:"diamond-icon"},null,-1)),Us=V({__name:"HomePage",props:{betTime:{type:Number,default:0},totalBetTimes:{type:Number,default:1}},setup(u){const p=u,d=q(),{state:s}=nt(d),{crtTeamKey:$,setActInfos:w}=d,c=g(0);ot(c,a=>{a>=s.value.userInfo.allstar_coin&&(c.value=s.value.userInfo.allstar_coin),a<=0&&(c.value=0)});const T=g(null),B=()=>{T.value.show()},k=g(null),L=()=>{k.value.show()},n=g(0),b=a=>{n.value=a},v=g(0);let H;const D=(a,r=!1)=>{!r&&v.value===a||(x("change_active",{activity_id:s.value.activeInfo[a].activity_id,group:s.value.activeInfo[a].group}),c.value=0,n.value=0,v.value=a,et(a),clearTimeout(H))},et=a=>{const r=s.value.activeInfo[a];N.show(),mt({activity_id:r.activity_id,group:r.group}).then(S=>{if(w(S),s.value.currentActInfo.status===1&&!s.value.currentActInfo.betting_status){let y=0,P=0;s.value.currentActInfo.team_infos.forEach((i,f)=>{Number(i.odds)>y&&(y=Number(i.odds),P=f)}),b(P)}}).finally(()=>{N.hide();const S=[s.value.activeInfo[a].bet_end_time,s.value.activeInfo[a].result_time].filter(y=>y>window.timeNow).sort();if(S.length){const y=S[0]-window.timeNow;y<86400&&(H=setTimeout(()=>{D(a,!0)},y*1e3))}})},G=g();Z(()=>{for(let a=0;a<s.value.activeInfo.length;a++){if(s.value.activeInfo[a].status===1){v.value=a;break}v.value=0}D(v.value,!0),v.value>1&&(G.value.scrollLeft=window.rootFontSize*244*(v.value-.8)/75)});const F=g(!1),at=()=>{const a=window.localStorage.getItem("isAvatarBonusPop"),r="".concat(new Date().getFullYear(),"/").concat(new Date().getMonth()+1,"/").concat(new Date().getDate());r!==a?(window.localStorage.setItem("isAvatarBonusPop",r),F.value=!0):it(z.global.t("alliance_guess_bet_success")),D(v.value,!0)};return(a,r)=>{const S=lt("i18n-t"),y=_t,P=X;return l(),_(h,null,[t("div",Mt,[t("div",Nt,[Ht,t("div",{class:A(["act-title",e(z).global.locale])},null,2),t("div",Dt,[t("div",Pt,o(a.$t("all_star_guess_game_time",[e(E)(e(s).activeInfo[v.value].game_time)])),1),t("div",Et,o(a.$t("all_star_guess_end_time",[e(E)(e(s).activeInfo[v.value].bet_end_time)])),1)])]),t("div",{class:"act-list",ref_key:"actEle",ref:G},[(l(!0),_(h,null,U(e(s).activeInfo,(i,f)=>(l(),_("div",{key:i.activity_id,class:A(["act-item",{active:f===v.value}]),onClick:J=>D(f)},[t("div",Vt,o(e(E)(i.game_time,"MM/DD")),1),t("div",Ft,o(a.$t("all_star_guess_game_name_".concat(f+1))),1),t("div",{class:A(["status","status-".concat(i.status)])},o(a.$t("all_star_guess_state_".concat(i.status))),3)],10,Ut))),128))],512),e(s).currentActInfo.status>0?(l(),_("div",Ot,[t("div",zt,[t("div",Kt,o(a.$t("all_star_guess_coin")),1),Rt,t("div",qt,o(e(s).userInfo.allstar_coin),1)]),t("div",{class:"r",onClick:L})])):m("",!0),e(s).currentActInfo.status>0?(l(),_("div",jt,[t("div",Gt,[t("div",null,o(a.$t("all_star_guess_odds_title")),1)]),t("div",Jt,[(l(!0),_(h,null,U(e(s).currentActInfo.team_infos,(i,f)=>(l(),_("div",{key:i.team_id,class:A(["betting-item",{active:n.value===f}]),onClick:J=>b(f)},[t("div",Qt,o(a.$t("all_star_guess_odds_subtitle")),1),t("div",Wt,[t("div",{text:i.odds,class:A({high:Number(i.odds)>=4})},o(i.odds),11,Xt)]),t("div",Zt,o(a.$t("all_star_team_".concat(f+1,"_name"))),1)],10,Yt))),128))]),t("div",xt,[e(s).currentActInfo.betting_status===!1?(l(),_(h,{key:0},[e(s).currentActInfo.status===1?(l(),_("div",ts,[I(S,{keypath:"all_star_guess_bet_choose",tag:"div",class:"tips-wrap"},{default:O(()=>[t("span",null,o(a.$t("all_star_team_".concat(n.value+1,"_name"))),1),ct(t("input",{type:"number",onkeyup:"value=value.replace(/^(0+)|[^\\d]+/g,'')","onUpdate:modelValue":r[0]||(r[0]=i=>c.value=i),min:"0",max:e(s).userInfo.allstar_coin},null,8,ss),[[ut,c.value]])]),_:1}),t("div",es,[I(y,{modelValue:c.value,"onUpdate:modelValue":r[1]||(r[1]=i=>c.value=i),max:e(s).userInfo.allstar_coin},null,8,["modelValue","max"]),as,t("div",ns,o(e(s).userInfo.allstar_coin),1)]),t("button",{class:"button submit-guess",disabled:c.value===0,onClick:B},o(a.$t("all_star_guess_bet_button")),9,os),t("div",ls,o(a.$t("all_star_guess_bet_attention")),1)])):m("",!0),e(s).currentActInfo.status>=2?(l(),_("div",is,[t("div",_s,o(a.$t("all_star_guess_result_3")),1)])):m("",!0)],64)):m("",!0),e(s).currentActInfo.betting_status===!0?(l(),_(h,{key:1},[e(s).currentActInfo.status<=2?(l(),_("div",cs,[t("div",{class:"guess-betting",innerHTML:a.$t("all_star_guess_result_0",[e(E)(e(s).userBettingInfo.ts),a.$t("all_star_team_".concat(e($)(e(s).currentActInfo.user_betting.team_id)+1,"_name")),e(s).currentActInfo.user_betting.bet_nums,e(s).currentActInfo.user_betting.odds,e(s).currentActInfo.user_betting.send_nums])},null,8,us),t("div",null,o(a.$t("all_star_guess_fail_tips",[e(s).currentActInfo.user_betting.fail_send_nums])),1)])):m("",!0),e(s).currentActInfo.status===3?(l(),_(h,{key:1},[e(s).currentActInfo.is_winner?(l(),_("div",rs,[t("div",{class:"guess-win",innerHTML:a.$t("all_star_guess_result_1",[a.$t("all_star_team_".concat(e($)(e(s).currentActInfo.user_betting.team_id)+1,"_name")),a.$t("all_star_".concat(e(s).winTeam,"_name")),e(s).currentActInfo.user_betting.send_nums])},null,8,ds)])):m("",!0),e(s).currentActInfo.is_winner?m("",!0):(l(),_("div",vs,[t("div",{class:"guess-loss",innerHTML:a.$t("all_star_guess_result_2",[a.$t("all_star_".concat(e(s).winTeam,"_name")),a.$t("all_star_team_".concat(e($)(e(s).currentActInfo.user_betting.team_id)+1,"_name")),e(s).currentActInfo.user_betting.fail_send_nums])},null,8,ms)]))],64)):m("",!0)],64)):m("",!0),gs,t("div",fs,[(l(!0),_(h,null,U(e(s).currentActInfo.team_infos,(i,f)=>(l(),_("div",{key:f,class:A(["team-item","team-item-".concat(i.order),{active:f===n.value}]),onClick:J=>b(f)},[i.is_winner>0?(l(),_("div",hs)):m("",!0),i.same_kingdom?(l(),_("div",$s)):m("",!0),t("div",bs,[i.same_alliance?(l(),_("div",ys)):m("",!0),t("div",Is,[t("div",null,o(a.$t("all_star_guess_team_power",[e(Y)(i.total_power||0)])),1),t("div",null,o(a.$t("all_star_guess_team_points",[e(Y)(i.total_score)])),1)]),ws,t("div",ks,o(a.$t("all_star_team_".concat(i.order,"_name"))),1),As])],10,ps))),128))])]),I(ft,{"show-title":"","crt-team-index":n.value,"team-name":a.$t("all_star_team_".concat(e(s).currentActInfo.team_infos[n.value].order,"_name")),data:e(s).currentActInfo.team_infos[n.value].users,columns:[{lang:"all_star_guess_team_member_name",prop:"nick_name"},{lang:"all_star_guess_team_member_sever",prop:"king",width:"150px"},{lang:"all_star_guess_team_member_points",prop:"score",width:"135px",isNumber:!0},{lang:"all_star_guess_team_member_power",prop:"power",width:"135px",isNumber:!0}]},null,8,["crt-team-index","team-name","data"])])):m("",!0),e(s).currentActInfo.status===0?(l(),_("div",Cs,[t("div",Ts,[t("div",Bs,[t("div",Ss,o(a.$t("all_star_guess_no_team")),1)])])])):m("",!0),I(tt,{"lang-key":"all_star_guess_rule_detail_new",class:"section","lang-slot":[p.betTime,p.totalBetTimes]},null,8,["lang-slot"]),e(s).currentActInfo.status>0?(l(),M(Lt,{key:3,onBetted:at,ref_key:"betComp",ref:T,"team-id":e(s).currentActInfo.team_infos[n.value].team_id,"team-key":"team_".concat(n.value+1),coin:c.value,odds:e(s).currentActInfo.team_infos[n.value].odds,"activity-id":e(s).activeInfo[v.value].activity_id,group:e(s).activeInfo[v.value].group},null,8,["team-id","team-key","coin","odds","activity-id","group"])):m("",!0),I(pt,{ref_key:"coinComp",ref:k,lang:"all_star_guess_coin_detail",imgs:[e(Q)("swiper1"),e(Q)("swiper2")]},null,8,["imgs"])]),I(st),I(P,{show:F.value},{default:O(()=>[t("div",Ls,[t("div",Ms,[t("div",{class:"close",onClick:r[2]||(r[2]=i=>F.value=!1)}),t("div",Ns,o(a.$t("alliance_guess_bet_success_pop_title")),1),Hs,t("div",Ds,[t("div",Ps,o(a.$t("alliance_guess_bet_success_pop_slogan")),1),Es,t("div",{class:"btn",onClick:r[3]||(r[3]=(...i)=>e(W)&&e(W)(...i))},o(a.$t("alliance_guess_bet_success_pop_button")),1)])])])]),_:1},8,["show"])],64)}}});const Vs=j(Us,[["__scopeId","data-v-6cf694f2"]]),Fs=u=>(K("data-v-ff3734e9"),u=u(),R(),u),Os={class:"main-page landing"},zs={class:"top"},Ks=Fs(()=>t("div",{class:"logo"},null,-1)),Rs={class:"act-time"},qs={class:"t"},js={class:"b"},Gs={class:"act-list"},Js=["onClick"],Ys={class:"time"},Qs={class:"title"},Ws={class:"status status-2"},Xs={class:"section betting"},Zs={class:"guess guess-unopen"},xs={class:"guess-wrap"},te={class:"tips"},se=V({__name:"LandingPage",setup(u){const p=[{start_day:"12/05",start_time:"12/05 20:00",end_time:"12/05 19:59"},{start_day:"12/05",start_time:"12/05 20:00",end_time:"12/05 19:59"},{start_day:"12/05",start_time:"12/05 20:00",end_time:"12/05 19:59"},{start_day:"12/05",start_time:"12/05 20:00",end_time:"12/05 19:59"},{start_day:"12/07",start_time:"12/07 20:00",end_time:"12/07 19:59"},{start_day:"12/07",start_time:"12/07 20:00",end_time:"12/07 19:59"},{start_day:"12/09",start_time:"12/09 20:00",end_time:"12/09 19:59"}],d=g(0);return(s,$)=>(l(),_(h,null,[t("div",Os,[t("div",zs,[Ks,t("div",{class:A(["act-title",e(z).global.locale])},null,2),t("div",Rs,[t("div",qs,o(s.$t("all_star_guess_game_time",[p[d.value].start_time])),1),t("div",js,o(s.$t("all_star_guess_end_time",[p[d.value].end_time])),1)])]),t("div",Gs,[(l(),_(h,null,U(p,(w,c)=>t("div",{class:A(["act-item",{active:d.value===c}]),key:c,onClick:T=>d.value=c},[t("div",Ys,o(w.start_day),1),t("div",Qs,o(s.$t("all_star_guess_game_name_".concat(c+1))),1),t("div",Ws,o(s.$t("all_star_guess_state_0")),1)],10,Js)),64))]),t("div",Xs,[t("div",Zs,[t("div",xs,[t("div",te,o(s.$t("all_star_guess_no_team")),1)])])]),I(tt,{"lang-key":"all_star_guess_rule_detail_new",class:"section","lang-slot":[1,1]})]),I(st)],64))}});const ee=j(se,[["__scopeId","data-v-ff3734e9"]]),le=V({__name:"AllStar",setup(u){const p=JSON.parse(window.sessionStorage.getItem("query")||"{}"),d=g(!1),s=g(!1),$=g(0),w=g(1);Z(()=>{const{ctx:n}=rt();document.getElementsByTagName("title")[0].innerText=n.$t("all_star_guess_main_title")});const c=q(),{setActiveInfo:T,setUserInfo:B}=c,k=()=>{N.show(),gt().then(n=>{N.hide(),d.value=!0,s.value=!1,T(n.activity_info),B(n.user_info),$.value=n.bet_countdown,w.value=n.total_bet_nums}).catch(n=>{N.hide(),n.errCode===7010?(d.value=!1,s.value=!1):n.errCode===7e3&&(s.value=!0,d.value=!1)})};p.openid?k():(s.value=!0,d.value=!1);const L=()=>{s.value=!1,d.value=!1};return(n,b)=>s.value?(l(),M(ht,{key:0,onBack:L})):(l(),_(h,{key:1},[d.value?(l(),M(Vs,{key:0,"bet-time":$.value,"total-bet-times":w.value},null,8,["bet-time","total-bet-times"])):(l(),M(ee,{key:1}))],64))}});export{le as default};
