import { fileURLToPath, URL } from 'node:url'

import { defineConfig, type PluginOption } from 'vite'
import Components from 'unplugin-vue-components/vite';
import { VantResolver } from 'unplugin-vue-components/resolvers';
import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import { visualizer } from 'rollup-plugin-visualizer'
import { createHtmlPlugin } from "vite-plugin-html";

// https://vitejs.dev/config/
// const cdn = 'https://asd.sd.d/asdz/mjqwe'
// const tag = 'master_global_v0.0.1'
const { cdn, tag } = process.env
const branch = tag ? tag.split('_')[0] : ''
const version = tag ? tag.split('_')[2] : ''
const base = branch === 'master' ? cdn : '/'
const outDir = branch === 'master' ? `${version}/dist` : 'dist_test'
console.log('base', base)
console.log('outDir', outDir)
console.log('version', version)
export default defineConfig(() => {
  return {
    base,
    build: {
      outDir,
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
          assetFileNames: '[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
          manualChunks:(id) => {
            if (id.includes('node_modules')) {
              return 'vendor'
            }
          }
        }
      },
    },
    server: {
      host: '0.0.0.0',
      proxy: {
        '/api': {
          target: 'http://st-events-master.kingsgroupgames.com/api', // master
          changeOrigin: true, // 是否跨域
          ws: false, // 是否支持websocket
          secure: false, // 如果是https接口，需要配置这个参数
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@assets': fileURLToPath(new URL('./src/assets', import.meta.url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@assets/styles/mixin.scss";@import "@assets/styles/vantUI.scss";'
        }
      },
    },
    define: {
      'import.meta.env.VITE_CDN_URL': JSON.stringify(base),
      'import.meta.env.VITE_VERSION': JSON.stringify(version)
    },
    plugins: [
      vue(),
      Components({
        resolvers: [VantResolver()],
      }),
      legacy({
        targets: ['defaults', 'ie >= 11', 'chrome 52', 'not dead', '> 1%'],  //需要兼容的目标列表，可以设置多个
        additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
        renderLegacyChunks:true,
        polyfills:[
          'es.symbol',
          'es.array.filter',
          'es.promise',
          'es.promise.finally',
          'es/map',
          'es/set',
          'es.array.for-each',
          'es.object.define-properties',
          'es.object.define-property',
          'es.object.get-own-property-descriptor',
          'es.object.get-own-property-descriptors',
          'es.object.keys',
          'es.object.to-string',
          'web.dom-collections.for-each',
          'esnext.global-this',
          'esnext.string.match-all'
        ]
      }),
      visualizer({
        template: "treemap", // or sunburst
        gzipSize: true,
        brotliSize: true,
        filename: "analyse.html", // will be saved in project's root
      }) as PluginOption,
      createHtmlPlugin({
        minify: true,
        entry: '/src/main.ts',
        inject: {
          data: {
            injectScript: branch === 'master' ? '' : `<script src="https://cdnjs.cloudflare.com/ajax/libs/vConsole/3.15.1/vconsole.min.js"></script><script>new window.VConsole()</script>`
          }
        }
      }),
    ]
  }
})
