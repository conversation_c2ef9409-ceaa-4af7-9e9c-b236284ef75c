/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CoinComp: typeof import('./src/components/common/CoinComp.vue')['default']
    CustomLoading: typeof import('./src/components/loading/CustomLoading.vue')['default']
    InvalidUser: typeof import('./src/components/common/InvalidUser.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RulesComp: typeof import('./src/components/common/RulesComp.vue')['default']
    TeamUsers: typeof import('./src/components/common/TeamUsers.vue')['default']
    TeamUsersCell: typeof import('./src/components/common/TeamUsersCell.vue')['default']
    TeamUsersHCell: typeof import('./src/components/common/TeamUsersHCell.vue')['default']
    TipsComp: typeof import('./src/components/common/TipsComp.vue')['default']
    VanLoading: typeof import('vant/es')['Loading']
    VanOverlay: typeof import('vant/es')['Overlay']
    VanSlider: typeof import('vant/es')['Slider']
  }
}
