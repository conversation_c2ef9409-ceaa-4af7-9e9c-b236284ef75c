{"name": "ttt", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build-o": "run-p type-check build-only", "build": "vite build", "build:test": "vite build --mode test", "build:master": "vite build --mode master", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.app.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "amfe-flexible": "^2.2.1", "axios": "^1.4.0", "pinia": "^2.1.4", "swiper": "^10.1.0", "vant": "^4.6.3", "vite-plugin-html": "^3.2.0", "vue": "^3.3.4", "vue-gtag-next": "^1.14.0", "vue-i18n": "^9.2.2", "vue-router": "^4.2.4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.2", "@tsconfig/node18": "^18.2.0", "@types/node": "^18.17.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.27", "postcss-pxtorem": "^6.0.0", "prettier": "^3.0.0", "rollup-plugin-copy": "^3.5.0", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.64.1", "terser": "^5.19.2", "typescript": "~5.1.6", "unplugin-vue-components": "^0.25.1", "vconsole": "^3.15.1", "vite": "^4.4.6", "vue-tsc": "^1.8.6"}}